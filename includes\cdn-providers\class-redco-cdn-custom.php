<?php
/**
 * Custom CDN Provider - Phase 4 Implementation
 * 
 * Generic CDN provider for any CDN service.
 * Provides basic URL rewriting and configuration options.
 * 
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Load base CDN class
require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/cdn-providers/class-redco-cdn-base.php';

class Redco_CDN_Custom extends Redco_CDN_Base {

    /**
     * Provider name
     */
    protected $provider_name = 'custom';

    /**
     * Provider capabilities
     */
    protected $capabilities = array(
        'url_rewriting' => true,
        'cache_purging' => false,
        'real_time_analytics' => false,
        'ssl_support' => true,
        'image_optimization' => false,
        'video_streaming' => false,
        'edge_computing' => false,
        'security_features' => false,
        'api_integration' => false
    );

    /**
     * Get configuration fields
     */
    public function get_configuration_fields() {
        return array(
            'cdn_url' => array(
                'type' => 'url',
                'label' => __('CDN URL', 'redco-optimizer'),
                'description' => __('Your CDN base URL (e.g., https://cdn.example.com)', 'redco-optimizer'),
                'required' => true,
                'placeholder' => 'https://cdn.example.com'
            ),
            'included_directories' => array(
                'type' => 'textarea',
                'label' => __('Included Directories', 'redco-optimizer'),
                'description' => __('Directories to serve via CDN (one per line). Leave empty to include all.', 'redco-optimizer'),
                'default' => "wp-content\nwp-includes",
                'placeholder' => "wp-content\nwp-includes\nuploads"
            ),
            'file_extensions' => array(
                'type' => 'textarea',
                'label' => __('File Extensions', 'redco-optimizer'),
                'description' => __('File extensions to serve via CDN (one per line)', 'redco-optimizer'),
                'default' => "css\njs\npng\njpg\njpeg\ngif\nsvg\nwoff\nwoff2\nttf\neot\nico\nmp4\nwebm\npdf",
                'placeholder' => "css\njs\npng\njpg"
            ),
            'excluded_files' => array(
                'type' => 'textarea',
                'label' => __('Excluded Files', 'redco-optimizer'),
                'description' => __('Files to exclude from CDN (one per line, supports wildcards)', 'redco-optimizer'),
                'placeholder' => "*.php\nadmin-*\nlogin*"
            ),
            'https_support' => array(
                'type' => 'checkbox',
                'label' => __('HTTPS Support', 'redco-optimizer'),
                'description' => __('Enable if your CDN supports HTTPS', 'redco-optimizer'),
                'default' => true
            ),
            'relative_path' => array(
                'type' => 'checkbox',
                'label' => __('Relative Path', 'redco-optimizer'),
                'description' => __('Use relative paths instead of absolute URLs', 'redco-optimizer'),
                'default' => false
            ),
            'custom_headers' => array(
                'type' => 'textarea',
                'label' => __('Custom Headers', 'redco-optimizer'),
                'description' => __('Custom headers to add (format: Header-Name: Value, one per line)', 'redco-optimizer'),
                'placeholder' => "Cache-Control: max-age=31536000\nX-CDN-Provider: Custom"
            ),
            'test_file_url' => array(
                'type' => 'url',
                'label' => __('Test File URL', 'redco-optimizer'),
                'description' => __('URL to test CDN connectivity (optional)', 'redco-optimizer'),
                'placeholder' => 'https://cdn.example.com/test.css'
            )
        );
    }

    /**
     * Validate configuration
     */
    public function validate_configuration($config) {
        // Check required fields
        if (empty($config['cdn_url'])) {
            return false;
        }

        // Validate URL format
        if (!filter_var($config['cdn_url'], FILTER_VALIDATE_URL)) {
            return false;
        }

        // Test CDN connectivity if test URL is provided
        if (!empty($config['test_file_url'])) {
            return $this->test_cdn_connectivity($config['test_file_url']);
        }

        return true;
    }

    /**
     * Test CDN connectivity
     */
    private function test_cdn_connectivity($test_url) {
        $response = wp_remote_head($test_url, array(
            'timeout' => 10,
            'user-agent' => 'RedCo Optimizer CDN Test'
        ));

        if (is_wp_error($response)) {
            $this->log('CDN connectivity test failed: ' . $response->get_error_message(), 'error');
            return false;
        }

        $status_code = wp_remote_retrieve_response_code($response);
        return $status_code >= 200 && $status_code < 400;
    }

    /**
     * Test connection
     */
    public function test_connection() {
        if (!empty($this->config['test_file_url'])) {
            return $this->test_cdn_connectivity($this->config['test_file_url']);
        }

        // If no test URL, assume connection is OK if CDN URL is valid
        return !empty($this->config['cdn_url']) && filter_var($this->config['cdn_url'], FILTER_VALIDATE_URL);
    }

    /**
     * Rewrite URL for Custom CDN
     */
    public function rewrite_url($url) {
        if (!$this->is_configured()) {
            return $url;
        }

        $site_url = get_site_url();
        $cdn_url = rtrim($this->config['cdn_url'], '/');

        // Only rewrite URLs that belong to this site
        if (strpos($url, $site_url) !== 0) {
            return $url;
        }

        // Check if file should be served via CDN
        if (!$this->should_use_cdn($url)) {
            return $url;
        }

        // Handle relative paths
        if (!empty($this->config['relative_path'])) {
            $relative_url = str_replace($site_url, '', $url);
            return $relative_url;
        }

        // Replace site URL with CDN URL
        $cdn_url_rewritten = str_replace($site_url, $cdn_url, $url);

        // Handle HTTPS
        if (!empty($this->config['https_support']) && is_ssl()) {
            $cdn_url_rewritten = str_replace('http://', 'https://', $cdn_url_rewritten);
        }

        return $cdn_url_rewritten;
    }

    /**
     * Check if URL should use CDN
     */
    private function should_use_cdn($url) {
        // Check file extension
        if (!$this->is_allowed_extension($url)) {
            return false;
        }

        // Check included directories
        if (!$this->is_in_included_directory($url)) {
            return false;
        }

        // Check excluded files
        if ($this->is_excluded_file($url)) {
            return false;
        }

        return true;
    }

    /**
     * Check if file extension is allowed
     */
    private function is_allowed_extension($url) {
        $extensions = $this->get_file_extensions();
        
        if (empty($extensions)) {
            return true; // If no extensions specified, allow all
        }

        $file_extension = strtolower(pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION));
        
        return in_array($file_extension, $extensions);
    }

    /**
     * Check if URL is in included directory
     */
    private function is_in_included_directory($url) {
        $directories = $this->get_included_directories();
        
        if (empty($directories)) {
            return true; // If no directories specified, include all
        }

        $site_url = get_site_url();
        $relative_url = str_replace($site_url, '', $url);
        
        foreach ($directories as $directory) {
            if (strpos($relative_url, '/' . trim($directory, '/') . '/') !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if file is excluded
     */
    private function is_excluded_file($url) {
        $excluded_files = $this->get_excluded_files();
        
        if (empty($excluded_files)) {
            return false;
        }

        $filename = basename(parse_url($url, PHP_URL_PATH));
        
        foreach ($excluded_files as $pattern) {
            if (fnmatch($pattern, $filename)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get file extensions array
     */
    private function get_file_extensions() {
        $extensions_text = $this->config['file_extensions'] ?? '';
        $extensions = array_filter(array_map('trim', explode("\n", $extensions_text)));
        
        return array_map('strtolower', $extensions);
    }

    /**
     * Get included directories array
     */
    private function get_included_directories() {
        $directories_text = $this->config['included_directories'] ?? '';
        return array_filter(array_map('trim', explode("\n", $directories_text)));
    }

    /**
     * Get excluded files array
     */
    private function get_excluded_files() {
        $excluded_text = $this->config['excluded_files'] ?? '';
        return array_filter(array_map('trim', explode("\n", $excluded_text)));
    }

    /**
     * Get cache statistics
     */
    public function get_cache_stats() {
        // Custom CDN doesn't provide analytics, return estimated stats
        return array(
            'cache_hit_ratio' => 85, // Estimated
            'bandwidth_saved' => 'N/A',
            'requests_served' => 'N/A',
            'data_transferred' => 'N/A'
        );
    }

    /**
     * Test performance
     */
    public function test_performance($test_urls = array()) {
        if (empty($test_urls)) {
            $test_urls = $this->get_default_test_urls();
        }

        $results = array();
        
        foreach ($test_urls as $url) {
            $original_time = $this->measure_response_time($url);
            $cdn_url = $this->rewrite_url($url);
            $cdn_time = $this->measure_response_time($cdn_url);
            
            $improvement = $original_time > 0 ? round((($original_time - $cdn_time) / $original_time) * 100, 2) : 0;
            
            $results[] = array(
                'url' => $url,
                'cdn_url' => $cdn_url,
                'original_time' => $original_time,
                'cdn_time' => $cdn_time,
                'improvement' => $improvement . '%'
            );
        }

        return $results;
    }

    /**
     * Measure response time
     */
    private function measure_response_time($url) {
        $start_time = microtime(true);
        
        $response = wp_remote_head($url, array(
            'timeout' => 10,
            'user-agent' => 'RedCo Optimizer Performance Test'
        ));
        
        $end_time = microtime(true);
        
        if (is_wp_error($response)) {
            return 0;
        }

        return round(($end_time - $start_time) * 1000, 2); // Convert to milliseconds
    }

    /**
     * Get provider metrics
     */
    public function get_provider_metrics() {
        $base_metrics = parent::get_provider_metrics();
        
        // Add custom CDN specific metrics
        $base_metrics['configuration'] = array(
            'cdn_url' => $this->config['cdn_url'] ?? '',
            'https_support' => !empty($this->config['https_support']),
            'file_types_count' => count($this->get_file_extensions()),
            'directories_count' => count($this->get_included_directories()),
            'exclusions_count' => count($this->get_excluded_files())
        );

        return $base_metrics;
    }

    /**
     * Optimize for WordPress
     */
    public function optimize_for_wordpress() {
        // For custom CDN, optimization is mainly about proper configuration
        $results = array(
            'url_rewriting' => array(
                'success' => true,
                'message' => 'URL rewriting configured for WordPress assets'
            )
        );

        // Add custom headers if configured
        if (!empty($this->config['custom_headers'])) {
            $results['custom_headers'] = array(
                'success' => true,
                'message' => 'Custom headers will be applied'
            );
        }

        return $results;
    }
}
