<?php
/**
 * Tiered Fix System for Redco Optimizer
 * 
 * Implements Safe/Moderate/Advanced fix categories with granular control
 * Integrated with existing diagnostic system
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Tiered_Fix_System {
    
    private $fix_tiers = array(
        'safe' => array(
            'risk_level' => 'low',
            'backup_required' => false,
            'user_confirmation' => false,
            'rollback_window' => 24, // hours
            'description' => 'Low-risk optimizations that are unlikely to cause issues',
            'color' => '#28a745',
            'icon' => '🟢'
        ),
        'moderate' => array(
            'risk_level' => 'medium',
            'backup_required' => true,
            'user_confirmation' => true,
            'rollback_window' => 72, // hours
            'description' => 'Medium-risk optimizations that may require testing',
            'color' => '#ffc107',
            'icon' => '🟡'
        ),
        'advanced' => array(
            'risk_level' => 'high',
            'backup_required' => true,
            'user_confirmation' => true,
            'rollback_window' => 168, // hours (1 week)
            'description' => 'High-risk optimizations for experienced users only',
            'color' => '#dc3545',
            'icon' => '🔴'
        )
    );
    
    private $fix_categories = array(
        'database' => array(
            'name' => 'Database Optimization',
            'icon' => 'dashicons-database',
            'description' => 'Database cleanup and optimization fixes'
        ),
        'frontend' => array(
            'name' => 'Frontend Performance',
            'icon' => 'dashicons-performance',
            'description' => 'CSS, JavaScript, and rendering optimizations'
        ),
        'server' => array(
            'name' => 'Server Configuration',
            'icon' => 'dashicons-admin-settings',
            'description' => 'Server-level performance configurations'
        ),
        'security' => array(
            'name' => 'Security Optimization',
            'icon' => 'dashicons-shield',
            'description' => 'Security-related performance improvements'
        ),
        'seo' => array(
            'name' => 'SEO Performance',
            'icon' => 'dashicons-search',
            'description' => 'SEO-focused performance optimizations'
        ),
        'images' => array(
            'name' => 'Image Optimization',
            'icon' => 'dashicons-format-image',
            'description' => 'Image compression and delivery optimizations'
        )
    );
    
    /**
     * Initialize the tiered fix system
     */
    public function init() {
        // Hook into existing diagnostic system
        add_filter('redco_diagnostic_issues', array($this, 'enhance_issues_with_tier_data'), 10, 1);
        add_filter('redco_fix_details', array($this, 'add_tier_metadata'), 10, 2);
        add_action('wp_ajax_redco_get_fix_tiers', array($this, 'ajax_get_fix_tiers'));
        add_action('wp_ajax_redco_get_tier_info', array($this, 'ajax_get_tier_info'));
    }
    
    /**
     * Enhance existing diagnostic issues with tier data
     */
    public function enhance_issues_with_tier_data($issues) {
        if (!is_array($issues)) {
            return $issues;
        }
        
        foreach ($issues as &$issue) {
            // Add tier classification if not already present
            if (!isset($issue['tier'])) {
                $issue['tier'] = $this->classify_fix_tier($issue);
            }
            
            // Add tier configuration
            if (isset($this->fix_tiers[$issue['tier']])) {
                $issue['tier_config'] = $this->fix_tiers[$issue['tier']];
            }
            
            // Add category if not present
            if (!isset($issue['category'])) {
                $issue['category'] = $this->classify_fix_category($issue);
            }
            
            // Add category configuration
            if (isset($this->fix_categories[$issue['category']])) {
                $issue['category_config'] = $this->fix_categories[$issue['category']];
            }
            
            // Add safety information
            $issue['safety_info'] = $this->get_safety_information($issue);
            
            // Add compatibility check
            $issue['compatibility'] = $this->check_compatibility($issue);
        }
        
        return $issues;
    }
    
    /**
     * Classify fix tier based on issue characteristics
     */
    private function classify_fix_tier($issue) {
        // Default tier classification logic
        $fix_id = $issue['id'] ?? '';
        $severity = $issue['severity'] ?? 'medium';
        
        // Safe tier fixes
        $safe_fixes = array(
            'cleanup_transients',
            'remove_query_strings',
            'optimize_images_lazy_load',
            'enable_gzip_compression',
            'set_browser_caching'
        );
        
        // Advanced tier fixes
        $advanced_fixes = array(
            'implement_critical_css',
            'defer_javascript',
            'optimize_wp_config',
            'database_index_optimization',
            'implement_preloading'
        );
        
        if (in_array($fix_id, $safe_fixes)) {
            return 'safe';
        } elseif (in_array($fix_id, $advanced_fixes)) {
            return 'advanced';
        } elseif ($severity === 'critical') {
            return 'moderate';
        } else {
            return 'safe';
        }
    }
    
    /**
     * Classify fix category based on issue characteristics
     */
    private function classify_fix_category($issue) {
        $fix_id = $issue['id'] ?? '';
        $category = $issue['category'] ?? '';
        
        // If category already set, return it
        if (!empty($category) && isset($this->fix_categories[$category])) {
            return $category;
        }
        
        // Category classification based on fix ID patterns
        if (strpos($fix_id, 'database') !== false || strpos($fix_id, 'db_') !== false) {
            return 'database';
        } elseif (strpos($fix_id, 'css') !== false || strpos($fix_id, 'js') !== false || strpos($fix_id, 'frontend') !== false) {
            return 'frontend';
        } elseif (strpos($fix_id, 'server') !== false || strpos($fix_id, 'htaccess') !== false) {
            return 'server';
        } elseif (strpos($fix_id, 'image') !== false || strpos($fix_id, 'img') !== false) {
            return 'images';
        } elseif (strpos($fix_id, 'seo') !== false) {
            return 'seo';
        } elseif (strpos($fix_id, 'security') !== false || strpos($fix_id, 'ssl') !== false) {
            return 'security';
        }
        
        return 'frontend'; // Default category
    }
    
    /**
     * Get safety information for a fix
     */
    private function get_safety_information($fix) {
        $tier = $fix['tier'] ?? 'safe';
        $tier_config = $this->fix_tiers[$tier] ?? $this->fix_tiers['safe'];
        
        return array(
            'risk_level' => $tier_config['risk_level'],
            'backup_required' => $tier_config['backup_required'],
            'user_confirmation' => $tier_config['user_confirmation'],
            'rollback_window' => $tier_config['rollback_window'],
            'reversible' => $fix['reversible'] ?? true,
            'prerequisites' => $fix['prerequisites'] ?? array(),
            'conflicts' => $fix['conflicts'] ?? array(),
            'warnings' => $this->get_fix_warnings($fix)
        );
    }
    
    /**
     * Get warnings for specific fixes
     */
    private function get_fix_warnings($fix) {
        $warnings = array();
        $fix_id = $fix['id'] ?? '';
        
        switch ($fix_id) {
            case 'minify_css_js':
                $warnings[] = 'May break sites with inline JavaScript that depends on formatting';
                break;
            case 'combine_css_js':
                $warnings[] = 'Not recommended for HTTP/2 servers';
                $warnings[] = 'May cause issues with conditional loading';
                break;
            case 'implement_critical_css':
                $warnings[] = 'Requires theme compatibility testing';
                $warnings[] = 'May cause flash of unstyled content (FOUC)';
                break;
            case 'defer_javascript':
                $warnings[] = 'May break functionality that depends on immediate script execution';
                break;
            case 'optimize_wp_config':
                $warnings[] = 'Modifies core WordPress configuration file';
                break;
        }
        
        return $warnings;
    }
    
    /**
     * Check compatibility for a fix
     */
    private function check_compatibility($fix) {
        $compatibility = array(
            'compatible' => true,
            'issues' => array(),
            'recommendations' => array()
        );
        
        $conflicts = $fix['conflicts'] ?? array();
        $prerequisites = $fix['prerequisites'] ?? array();
        
        // Check for conflicting plugins
        foreach ($conflicts as $conflict) {
            if ($this->has_conflicting_feature($conflict)) {
                $compatibility['compatible'] = false;
                $compatibility['issues'][] = "Conflicts with existing {$conflict}";
            }
        }
        
        // Check prerequisites
        foreach ($prerequisites as $prerequisite) {
            if (!$this->check_prerequisite($prerequisite)) {
                $compatibility['compatible'] = false;
                $compatibility['issues'][] = "Missing prerequisite: {$prerequisite}";
            }
        }
        
        return $compatibility;
    }
    
    /**
     * Check if a conflicting feature exists
     */
    private function has_conflicting_feature($conflict) {
        switch ($conflict) {
            case 'existing_caching_plugins':
                return $this->has_caching_plugin();
            case 'existing_minification_plugins':
                return $this->has_minification_plugin();
            case 'existing_lazy_load_plugins':
                return $this->has_lazy_load_plugin();
            default:
                return false;
        }
    }
    
    /**
     * Check if prerequisite is met
     */
    private function check_prerequisite($prerequisite) {
        switch ($prerequisite) {
            case 'mod_deflate':
            case 'mod_gzip':
                return $this->check_apache_module($prerequisite);
            case 'mod_expires':
                return $this->check_apache_module($prerequisite);
            case 'file_write_permissions':
                return is_writable(ABSPATH);
            case 'database_admin_access':
                return current_user_can('manage_options');
            default:
                return true;
        }
    }
    
    /**
     * AJAX: Get fix tiers configuration
     */
    public function ajax_get_fix_tiers() {
        check_ajax_referer('redco_diagnostic_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        wp_send_json_success(array(
            'tiers' => $this->fix_tiers,
            'categories' => $this->fix_categories
        ));
    }
    
    /**
     * AJAX: Get specific tier information
     */
    public function ajax_get_tier_info() {
        check_ajax_referer('redco_diagnostic_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $tier = sanitize_text_field($_POST['tier'] ?? '');
        
        if (!isset($this->fix_tiers[$tier])) {
            wp_send_json_error('Invalid tier');
            return;
        }
        
        wp_send_json_success($this->fix_tiers[$tier]);
    }
    
    /**
     * Helper methods for compatibility checking
     */
    private function has_caching_plugin() {
        $caching_plugins = array('w3-total-cache', 'wp-super-cache', 'wp-rocket', 'litespeed-cache');
        $active_plugins = get_option('active_plugins', array());
        
        foreach ($caching_plugins as $plugin) {
            foreach ($active_plugins as $active_plugin) {
                if (strpos($active_plugin, $plugin) !== false) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    private function has_minification_plugin() {
        $minification_plugins = array('autoptimize', 'wp-minify', 'fast-velocity-minify');
        $active_plugins = get_option('active_plugins', array());
        
        foreach ($minification_plugins as $plugin) {
            foreach ($active_plugins as $active_plugin) {
                if (strpos($active_plugin, $plugin) !== false) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    private function has_lazy_load_plugin() {
        $lazy_load_plugins = array('lazy-load', 'a3-lazy-load', 'wp-smushit');
        $active_plugins = get_option('active_plugins', array());
        
        foreach ($lazy_load_plugins as $plugin) {
            foreach ($active_plugins as $active_plugin) {
                if (strpos($active_plugin, $plugin) !== false) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    private function check_apache_module($module) {
        if (function_exists('apache_get_modules')) {
            return in_array($module, apache_get_modules());
        }
        
        // Fallback: check if module directives work
        return true; // Assume available if we can't check
    }
}
