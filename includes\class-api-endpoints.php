<?php
/**
 * API Endpoints Configuration
 * 
 * Centralized configuration for all external API endpoints used by Redco Optimizer
 * 
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_API_Endpoints {
    
    /**
     * Google PageSpeed Insights API configuration
     */
    const PAGESPEED_API_BASE = 'https://www.googleapis.com/pagespeedonline/v5/runPagespeed';
    const PAGESPEED_API_VERSION = 'v5';
    
    /**
     * WordPress.org API endpoints
     */
    const WORDPRESS_API_BASE = 'https://api.wordpress.org';
    const WORDPRESS_VERSION_CHECK = 'https://api.wordpress.org/core/version-check/1.7/';
    
    /**
     * CDN and external service endpoints
     */
    const GOOGLE_FONTS_API = 'https://fonts.googleapis.com';
    const GOOGLE_FONTS_STATIC = 'https://fonts.gstatic.com';
    const GOOGLE_ANALYTICS = 'https://google-analytics.com';
    const GOOGLE_TAG_MANAGER = 'https://googletagmanager.com';
    
    /**
     * Get PageSpeed API URL with parameters
     * 
     * @param string $url Site URL to test
     * @param string $api_key Google API key
     * @param string $strategy Testing strategy (mobile/desktop)
     * @param array $categories Categories to test
     * @return string Complete API URL
     */
    public static function get_pagespeed_url($url, $api_key, $strategy = 'mobile', $categories = null) {
        if ($categories === null) {
            $categories = array('performance', 'accessibility', 'best-practices', 'seo');
        }
        
        $query_parts = array();
        $query_parts[] = 'url=' . urlencode($url);
        $query_parts[] = 'key=' . urlencode($api_key);
        $query_parts[] = 'strategy=' . urlencode($strategy);
        
        foreach ($categories as $category) {
            $query_parts[] = 'category=' . urlencode($category);
        }
        
        return self::PAGESPEED_API_BASE . '?' . implode('&', $query_parts);
    }
    
    /**
     * Get simple PageSpeed API URL for validation
     * 
     * @param string $url Site URL to test
     * @param string $api_key Google API key
     * @return string Simple API URL for validation
     */
    public static function get_pagespeed_validation_url($url, $api_key) {
        return self::PAGESPEED_API_BASE . '?url=' . urlencode($url) . '&key=' . urlencode($api_key);
    }
    
    /**
     * Get WordPress version check URL
     * 
     * @return string WordPress version check API URL
     */
    public static function get_wordpress_version_url() {
        return self::WORDPRESS_VERSION_CHECK;
    }
    
    /**
     * Check if URL is a known external service
     * 
     * @param string $url URL to check
     * @return array Service information or false
     */
    public static function identify_external_service($url) {
        $services = array(
            'fonts.googleapis.com' => array('name' => 'Google Fonts', 'type' => 'font'),
            'fonts.gstatic.com' => array('name' => 'Google Fonts Static', 'type' => 'font'),
            'google-analytics.com' => array('name' => 'Google Analytics', 'type' => 'analytics'),
            'googletagmanager.com' => array('name' => 'Google Tag Manager', 'type' => 'analytics'),
            'facebook.net' => array('name' => 'Facebook', 'type' => 'social'),
            'doubleclick.net' => array('name' => 'Google DoubleClick', 'type' => 'advertising'),
            'googlesyndication.com' => array('name' => 'Google AdSense', 'type' => 'advertising')
        );
        
        foreach ($services as $domain => $info) {
            if (strpos($url, $domain) !== false) {
                return $info;
            }
        }
        
        return false;
    }
    
    /**
     * Get timeout settings for different API types
     * 
     * @param string $api_type Type of API (pagespeed, wordpress, etc.)
     * @return int Timeout in seconds
     */
    public static function get_timeout($api_type) {
        $timeouts = array(
            'pagespeed' => 60,
            'wordpress' => 30,
            'validation' => 30,
            'connectivity' => 15,
            'default' => 30
        );
        
        return isset($timeouts[$api_type]) ? $timeouts[$api_type] : $timeouts['default'];
    }
    
    /**
     * Get user agent string for API requests
     * 
     * @return string User agent string
     */
    public static function get_user_agent() {
        return 'Redco Optimizer Plugin/' . REDCO_OPTIMIZER_VERSION . ' (WordPress/' . get_bloginfo('version') . ')';
    }
    
    /**
     * Get default request headers
     * 
     * @return array Default headers for API requests
     */
    public static function get_default_headers() {
        return array(
            'User-Agent' => self::get_user_agent(),
            'Accept' => 'application/json',
            'Cache-Control' => 'no-cache'
        );
    }
}
