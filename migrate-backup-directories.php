<?php
/**
 * Migrate existing backups from old directory structure to new directory structure
 * This fixes the issue where rollback operations fail because backups are in different locations
 */

require_once('d:/xampp/htdocs/wordpress/wp-config.php');
require_once('includes/helpers.php');

echo "=== BACKUP DIRECTORY MIGRATION ===\n";

// UNIFIED: Use centralized backup directory function
$old_backup_dir = 'D:/xampp/htdocs/wordpress/wp-content/uploads/redco-cachediagnostic-backups/';
$new_backup_dir = redco_get_unified_backup_dir();

echo "Old backup directory: $old_backup_dir\n";
echo "New backup directory: $new_backup_dir\n";

// Check if old directory exists
if (!is_dir($old_backup_dir)) {
    echo "❌ Old backup directory does not exist. No migration needed.\n";
    exit(0);
}

// Create new backup directory if it doesn't exist
if (!is_dir($new_backup_dir)) {
    echo "Creating new backup directory...\n";
    if (!wp_mkdir_p($new_backup_dir)) {
        echo "❌ Failed to create new backup directory: $new_backup_dir\n";
        exit(1);
    }
    echo "✅ New backup directory created.\n";
}

// Get list of backups in old directory
$old_backups = glob($old_backup_dir . 'backup_*');
echo "Found " . count($old_backups) . " backups in old directory.\n";

if (empty($old_backups)) {
    echo "No backups to migrate.\n";
    exit(0);
}

$migrated_count = 0;
$failed_count = 0;
$skipped_count = 0;

foreach ($old_backups as $old_backup_path) {
    $backup_name = basename($old_backup_path);
    $new_backup_path = $new_backup_dir . $backup_name;
    
    echo "\nProcessing backup: $backup_name\n";
    
    // Check if backup already exists in new location
    if (is_dir($new_backup_path)) {
        echo "  ⚠️  Backup already exists in new location. Skipping.\n";
        $skipped_count++;
        continue;
    }
    
    // Validate old backup structure
    if (!is_dir($old_backup_path)) {
        echo "  ❌ Old backup is not a directory. Skipping.\n";
        $failed_count++;
        continue;
    }
    
    $backup_data_file = $old_backup_path . '/backup_data.json';
    if (!file_exists($backup_data_file)) {
        echo "  ❌ Backup metadata file missing. Skipping.\n";
        $failed_count++;
        continue;
    }
    
    // Move the backup directory
    echo "  📦 Moving backup directory...\n";
    if (rename($old_backup_path, $new_backup_path)) {
        echo "  ✅ Successfully migrated backup.\n";
        $migrated_count++;
        
        // Verify the migration
        if (is_dir($new_backup_path) && file_exists($new_backup_path . '/backup_data.json')) {
            echo "  ✅ Migration verified.\n";
        } else {
            echo "  ⚠️  Migration verification failed.\n";
        }
    } else {
        echo "  ❌ Failed to move backup directory.\n";
        $failed_count++;
    }
}

echo "\n=== MIGRATION SUMMARY ===\n";
echo "Total backups found: " . count($old_backups) . "\n";
echo "Successfully migrated: $migrated_count\n";
echo "Skipped (already exists): $skipped_count\n";
echo "Failed: $failed_count\n";

// Check if old directory is now empty (except for .htaccess)
$remaining_items = glob($old_backup_dir . '*');
$remaining_backups = array_filter($remaining_items, function($item) {
    return is_dir($item) && strpos(basename($item), 'backup_') === 0;
});

if (empty($remaining_backups)) {
    echo "\n✅ All backups successfully migrated!\n";
    echo "Old backup directory is now empty of backups.\n";
    
    // Optionally remove the old directory (keeping .htaccess for security)
    $htaccess_file = $old_backup_dir . '.htaccess';
    if (file_exists($htaccess_file)) {
        echo "Keeping .htaccess file in old directory for security.\n";
    }
} else {
    echo "\n⚠️  Some backups remain in old directory:\n";
    foreach ($remaining_backups as $remaining) {
        echo "  - " . basename($remaining) . "\n";
    }
}

// Verify new directory structure
echo "\n=== VERIFICATION ===\n";
$new_backups = glob($new_backup_dir . 'backup_*');
echo "Backups now in new directory: " . count($new_backups) . "\n";

if (count($new_backups) > 0) {
    echo "✅ New backup directory is populated and ready for use.\n";
    
    // Test with the diagnostic autofix engine
    echo "\nTesting with diagnostic autofix engine...\n";
    try {
        require_once('modules/diagnostic-autofix/class-diagnostic-autofix-engine.php');
        $engine = new Redco_Diagnostic_AutoFix_Engine();
        
        // Use reflection to get backup directory
        $reflection = new ReflectionClass($engine);
        $backup_dir_property = $reflection->getProperty('backup_dir');
        $backup_dir_property->setAccessible(true);
        $engine_backup_dir = $backup_dir_property->getValue($engine);
        
        echo "Engine backup directory: $engine_backup_dir\n";
        
        if (wp_normalize_path(trailingslashit($engine_backup_dir)) === wp_normalize_path(trailingslashit($new_backup_dir))) {
            echo "✅ Engine is using the correct backup directory!\n";
        } else {
            echo "⚠️  Engine is using a different backup directory.\n";
            echo "This may indicate the priority system is selecting a different location.\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Error testing engine: " . $e->getMessage() . "\n";
    }
} else {
    echo "⚠️  No backups found in new directory after migration.\n";
}

echo "\n=== MIGRATION COMPLETE ===\n";
echo "You can now test rollback operations with the migrated backups.\n";
