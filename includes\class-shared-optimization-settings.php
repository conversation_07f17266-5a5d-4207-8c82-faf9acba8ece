<?php
/**
 * Shared Optimization Settings - Phase 3 Consolidation
 * 
 * Provides unified optimization settings components for Lazy Load and Heartbeat Control modules.
 * Eliminates duplicate settings and provides consistent optimization recommendations.
 * 
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Shared_Optimization_Settings {

    /**
     * Render unified performance optimization panel
     *
     * @param string $module_context Module context (lazy-load, heartbeat-control)
     * @param array $current_settings Current module settings
     */
    public static function render_performance_optimization_panel($module_context, $current_settings = array()) {
        $performance_data = self::get_performance_data($module_context);
        ?>
        <div class="redco-sidebar-section shared-optimization-panel">
            <div class="sidebar-section-header">
                <h3>
                    <span class="dashicons dashicons-performance"></span>
                    <?php _e('Performance Optimization', 'redco-optimizer'); ?>
                </h3>
            </div>
            <div class="sidebar-section-content">
                <div class="optimization-intro">
                    <p class="description">
                        <?php echo esc_html($performance_data['description']); ?>
                    </p>
                    <div class="performance-score-display">
                        <div class="score-circle score-<?php echo esc_attr($performance_data['status']); ?>">
                            <span class="score-value"><?php echo $performance_data['score']; ?>%</span>
                            <span class="score-label"><?php _e('Optimized', 'redco-optimizer'); ?></span>
                        </div>
                        <div class="score-details">
                            <h4><?php echo esc_html($performance_data['status_text']); ?></h4>
                            <p><?php echo esc_html($performance_data['message']); ?></p>
                        </div>
                    </div>
                </div>

                <?php if (!empty($performance_data['recommendations'])): ?>
                <div class="optimization-recommendations">
                    <h4><?php _e('Optimization Recommendations', 'redco-optimizer'); ?></h4>
                    <div class="recommendations-list">
                        <?php foreach ($performance_data['recommendations'] as $recommendation): ?>
                            <div class="recommendation-item">
                                <span class="dashicons dashicons-<?php echo esc_attr($recommendation['icon']); ?>"></span>
                                <div class="recommendation-content">
                                    <strong><?php echo esc_html($recommendation['title']); ?></strong>
                                    <p><?php echo esc_html($recommendation['description']); ?></p>
                                </div>
                                <?php if (isset($recommendation['action'])): ?>
                                    <button type="button" class="recommendation-action" data-action="<?php echo esc_attr($recommendation['action']); ?>">
                                        <?php echo esc_html($recommendation['action_text']); ?>
                                    </button>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <div class="optimization-actions">
                    <button type="button" class="optimization-preset-btn" data-preset="performance" data-module="<?php echo esc_attr($module_context); ?>">
                        <span class="dashicons dashicons-performance"></span>
                        <?php _e('Optimize for Performance', 'redco-optimizer'); ?>
                    </button>
                    <button type="button" class="optimization-preset-btn" data-preset="balanced" data-module="<?php echo esc_attr($module_context); ?>">
                        <span class="dashicons dashicons-admin-settings"></span>
                        <?php _e('Balanced Settings', 'redco-optimizer'); ?>
                    </button>
                    <button type="button" class="optimization-preset-btn" data-preset="conservative" data-module="<?php echo esc_attr($module_context); ?>">
                        <span class="dashicons dashicons-shield"></span>
                        <?php _e('Conservative Mode', 'redco-optimizer'); ?>
                    </button>
                </div>
            </div>
        </div>

        <style>
        .shared-optimization-panel .performance-score-display {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 10px 0;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .score-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            text-align: center;
            flex-shrink: 0;
        }

        .score-circle.score-excellent { background: #4CAF50; }
        .score-circle.score-good { background: #8BC34A; }
        .score-circle.score-fair { background: #FF9800; }
        .score-circle.score-needs_improvement { background: #F44336; }

        .score-value { font-size: 18px; line-height: 1; }
        .score-label { font-size: 11px; opacity: 0.9; }

        .score-details h4 { margin: 0 0 5px 0; color: #333; }
        .score-details p { margin: 0; color: #666; }

        .optimization-recommendations {
            margin: 20px 0;
            padding: 15px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
        }

        .recommendations-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .recommendation-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
        }

        .recommendation-item .dashicons {
            color: #FF9800;
            font-size: 20px;
        }

        .recommendation-content {
            flex: 1;
        }

        .recommendation-content strong {
            display: block;
            margin-bottom: 3px;
        }

        .recommendation-content p {
            margin: 0;
            font-size: 13px;
            color: #666;
        }

        .recommendation-action {
            background: #007cba;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .recommendation-action:hover {
            background: #005a87;
        }

        .optimization-actions {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-top: 15px;
        }

        .optimization-preset-btn {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 12px;
            background: #f1f1f1;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 12px;
            width: 100%;
            justify-content: flex-start;
        }

        .optimization-preset-btn:hover {
            background: #e0e0e0;
            border-color: #ccc;
        }

        .optimization-preset-btn.loading {
            opacity: 0.7;
            pointer-events: none;
        }

        .optimization-preset-btn .dashicons {
            font-size: 16px;
        }
        </style>
        <?php
    }

    /**
     * Get performance data for module
     */
    private static function get_performance_data($module_context) {
        switch ($module_context) {
            case 'lazy-load':
                return self::get_lazy_load_performance_data();
            case 'heartbeat-control':
                return self::get_heartbeat_performance_data();
            default:
                return self::get_default_performance_data();
        }
    }

    /**
     * Get lazy load performance data
     */
    private static function get_lazy_load_performance_data() {
        if (!class_exists('Redco_Lazy_Load')) {
            return self::get_default_performance_data();
        }

        $lazy_load = new Redco_Lazy_Load();
        $stats = $lazy_load->get_stats();
        
        $recommendations = array();
        
        if ($stats['threshold'] > 200) {
            $recommendations[] = array(
                'icon' => 'performance',
                'title' => 'Reduce Loading Threshold',
                'description' => 'Lower threshold to 200px for better performance',
                'action' => 'optimize_threshold',
                'action_text' => 'Optimize'
            );
        }
        
        if ($stats['exclude_first_images'] < 2) {
            $recommendations[] = array(
                'icon' => 'visibility',
                'title' => 'Exclude Above-the-Fold Images',
                'description' => 'Exclude first 2 images to improve LCP scores',
                'action' => 'optimize_exclusions',
                'action_text' => 'Fix'
            );
        }

        $score = 70; // Base score
        if ($stats['threshold'] <= 200) $score += 15;
        if ($stats['exclude_first_images'] >= 2) $score += 15;

        return array(
            'score' => min(100, $score),
            'status' => self::get_status_from_score($score),
            'status_text' => self::get_status_text_from_score($score),
            'message' => 'Lazy loading optimizes image loading for better performance',
            'description' => 'Lazy loading delays image loading until they\'re needed, reducing initial page load time and bandwidth usage.',
            'recommendations' => $recommendations
        );
    }

    /**
     * Get heartbeat performance data
     */
    private static function get_heartbeat_performance_data() {
        if (!class_exists('Redco_Heartbeat_Control')) {
            return self::get_default_performance_data();
        }

        $heartbeat = new Redco_Heartbeat_Control();
        $stats = $heartbeat->get_stats();
        
        $recommendations = array();
        
        if ($stats['frontend_status'] !== 'disable') {
            $recommendations[] = array(
                'icon' => 'admin-network',
                'title' => 'Disable Frontend Heartbeat',
                'description' => 'Disable heartbeat on frontend for better performance',
                'action' => 'disable_frontend_heartbeat',
                'action_text' => 'Disable'
            );
        }
        
        if ($stats['admin_frequency'] < 60) {
            $recommendations[] = array(
                'icon' => 'clock',
                'title' => 'Increase Admin Frequency',
                'description' => 'Set admin heartbeat to 60 seconds to reduce server load',
                'action' => 'optimize_admin_frequency',
                'action_text' => 'Optimize'
            );
        }

        return array(
            'score' => $stats['optimization_level'],
            'status' => self::get_status_from_score($stats['optimization_level']),
            'status_text' => self::get_status_text_from_score($stats['optimization_level']),
            'message' => $stats['performance_impact']['message'],
            'description' => 'Heartbeat control reduces server load by optimizing WordPress background requests.',
            'recommendations' => $recommendations
        );
    }

    /**
     * Get default performance data
     */
    private static function get_default_performance_data() {
        return array(
            'score' => 0,
            'status' => 'needs_improvement',
            'status_text' => 'Module Disabled',
            'message' => 'Enable this module to start optimizing performance',
            'description' => 'This module is currently disabled.',
            'recommendations' => array()
        );
    }

    /**
     * Get status from score
     */
    private static function get_status_from_score($score) {
        if ($score >= 90) return 'excellent';
        if ($score >= 75) return 'good';
        if ($score >= 60) return 'fair';
        return 'needs_improvement';
    }

    /**
     * Get status text from score
     */
    private static function get_status_text_from_score($score) {
        if ($score >= 90) return __('Excellent Optimization', 'redco-optimizer');
        if ($score >= 75) return __('Good Optimization', 'redco-optimizer');
        if ($score >= 60) return __('Fair Optimization', 'redco-optimizer');
        return __('Needs Improvement', 'redco-optimizer');
    }

    /**
     * Render shared optimization JavaScript
     */
    public static function render_optimization_javascript() {
        ?>
        <script>
        jQuery(document).ready(function($) {
            // Handle optimization preset buttons
            $('.optimization-preset-btn').on('click', function() {
                const $button = $(this);
                const preset = $button.data('preset');
                const module = $button.data('module');
                
                $button.addClass('loading').text('<?php _e("Applying...", "redco-optimizer"); ?>');
                
                // Apply preset optimization
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'redco_apply_optimization_preset',
                        preset: preset,
                        module: module,
                        nonce: '<?php echo wp_create_nonce('redco_optimizer_nonce'); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            // Reload page to show updated settings
                            window.location.reload();
                        } else {
                            alert(response.data.message || '<?php _e("Failed to apply optimization", "redco-optimizer"); ?>');
                        }
                    },
                    error: function() {
                        alert('<?php _e("Error applying optimization", "redco-optimizer"); ?>');
                    },
                    complete: function() {
                        $button.removeClass('loading').text($button.find('.dashicons').length ? '' : '<?php _e("Optimize", "redco-optimizer"); ?>');
                    }
                });
            });
        });
        </script>
        <?php
    }
}
