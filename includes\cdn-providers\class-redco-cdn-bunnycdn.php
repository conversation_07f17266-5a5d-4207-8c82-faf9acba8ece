<?php
/**
 * <PERSON>CDN Provider - Phase 4 Implementation
 * 
 * BunnyCD<PERSON> integration with Pull/Push zones and real-time statistics.
 * Affordable CDN solution with excellent performance.
 * 
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Load base CDN class
require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/cdn-providers/class-redco-cdn-base.php';

class Redco_CDN_BunnyCDN extends Redco_CDN_Base {

    /**
     * Provider name
     */
    protected $provider_name = 'bunnycdn';

    /**
     * BunnyCDN API base URL
     */
    private $api_base = 'https://api.bunny.net';

    /**
     * Provider capabilities
     */
    protected $capabilities = array(
        'url_rewriting' => true,
        'cache_purging' => true,
        'real_time_analytics' => true,
        'ssl_support' => true,
        'image_optimization' => true,
        'video_streaming' => true,
        'edge_computing' => false,
        'security_features' => true,
        'api_integration' => true
    );

    /**
     * Get configuration fields
     */
    public function get_configuration_fields() {
        return array(
            'api_key' => array(
                'type' => 'password',
                'label' => __('BunnyCDN API Key', 'redco-optimizer'),
                'description' => __('Your BunnyCDN API key from the account settings', 'redco-optimizer'),
                'required' => true,
                'placeholder' => 'Enter your BunnyCDN API key'
            ),
            'pullzone_id' => array(
                'type' => 'text',
                'label' => __('Pull Zone ID', 'redco-optimizer'),
                'description' => __('The ID of your BunnyCDN Pull Zone', 'redco-optimizer'),
                'required' => true,
                'placeholder' => 'Enter your Pull Zone ID'
            ),
            'cdn_hostname' => array(
                'type' => 'text',
                'label' => __('CDN Hostname', 'redco-optimizer'),
                'description' => __('Your BunnyCDN hostname (e.g., mysite.b-cdn.net)', 'redco-optimizer'),
                'required' => true,
                'placeholder' => 'mysite.b-cdn.net'
            ),
            'custom_hostname' => array(
                'type' => 'text',
                'label' => __('Custom Hostname', 'redco-optimizer'),
                'description' => __('Optional: Custom domain for your CDN (e.g., cdn.mysite.com)', 'redco-optimizer'),
                'required' => false,
                'placeholder' => 'cdn.mysite.com'
            ),
            'cache_control_max_age' => array(
                'type' => 'select',
                'label' => __('Cache Control Max Age', 'redco-optimizer'),
                'description' => __('How long content should be cached', 'redco-optimizer'),
                'options' => array(
                    '3600' => '1 hour',
                    '14400' => '4 hours',
                    '86400' => '1 day',
                    '604800' => '1 week',
                    '2592000' => '1 month',
                    '31536000' => '1 year'
                ),
                'default' => '86400'
            ),
            'optimizer_enabled' => array(
                'type' => 'checkbox',
                'label' => __('Enable Bunny Optimizer', 'redco-optimizer'),
                'description' => __('Enable automatic image optimization and WebP conversion', 'redco-optimizer'),
                'default' => true
            ),
            'optimizer_quality' => array(
                'type' => 'select',
                'label' => __('Image Quality', 'redco-optimizer'),
                'description' => __('Quality level for optimized images', 'redco-optimizer'),
                'options' => array(
                    '50' => 'Low (50%)',
                    '70' => 'Medium (70%)',
                    '85' => 'High (85%)',
                    '95' => 'Very High (95%)'
                ),
                'default' => '85'
            ),
            'vary_cache' => array(
                'type' => 'checkbox',
                'label' => __('Vary Cache', 'redco-optimizer'),
                'description' => __('Enable vary cache for mobile optimization', 'redco-optimizer'),
                'default' => false
            ),
            'disable_cookies' => array(
                'type' => 'checkbox',
                'label' => __('Disable Cookies', 'redco-optimizer'),
                'description' => __('Strip cookies from cached content for better performance', 'redco-optimizer'),
                'default' => true
            )
        );
    }

    /**
     * Validate configuration
     */
    public function validate_configuration($config) {
        // Check required fields
        if (empty($config['api_key']) || empty($config['pullzone_id']) || empty($config['cdn_hostname'])) {
            return false;
        }

        // Test API connection
        return $this->test_api_connection($config);
    }

    /**
     * Test API connection
     */
    private function test_api_connection($config = null) {
        $config = $config ?: $this->config;
        
        if (empty($config['api_key']) || empty($config['pullzone_id'])) {
            return false;
        }

        $headers = array(
            'AccessKey' => $config['api_key'],
            'Content-Type' => 'application/json'
        );

        $response = $this->make_api_request(
            $this->api_base . '/pullzone/' . $config['pullzone_id'],
            array('headers' => $headers)
        );

        return $response['success'];
    }

    /**
     * Test connection
     */
    public function test_connection() {
        return $this->test_api_connection();
    }

    /**
     * Rewrite URL for BunnyCDN
     */
    public function rewrite_url($url) {
        if (!$this->is_configured()) {
            return $url;
        }

        $site_url = get_site_url();
        $cdn_hostname = !empty($this->config['custom_hostname']) ? 
            $this->config['custom_hostname'] : 
            $this->config['cdn_hostname'];

        // Only rewrite URLs that belong to this site
        if (strpos($url, $site_url) === 0) {
            $relative_url = str_replace($site_url, '', $url);
            return 'https://' . $cdn_hostname . $relative_url;
        }

        return $url;
    }

    /**
     * Purge cache
     */
    public function purge_cache($urls = array()) {
        if (!$this->is_configured()) {
            return array('success' => false, 'message' => 'BunnyCDN not configured');
        }

        $headers = array(
            'AccessKey' => $this->config['api_key'],
            'Content-Type' => 'application/json'
        );

        if (empty($urls)) {
            // Purge entire pull zone
            $response = $this->make_api_request(
                $this->api_base . '/pullzone/' . $this->config['pullzone_id'] . '/purgeCache',
                array(
                    'method' => 'POST',
                    'headers' => $headers
                )
            );
        } else {
            // Purge specific URLs
            $results = array();
            foreach ($urls as $url) {
                $cdn_url = $this->rewrite_url($url);
                $response = $this->make_api_request(
                    $this->api_base . '/purge?url=' . urlencode($cdn_url),
                    array(
                        'method' => 'POST',
                        'headers' => $headers
                    )
                );
                $results[] = $response;
            }
            
            // Return success if all purges succeeded
            $all_success = array_reduce($results, function($carry, $result) {
                return $carry && $result['success'];
            }, true);
            
            return array(
                'success' => $all_success,
                'message' => $all_success ? 'URLs purged successfully' : 'Some URLs failed to purge',
                'details' => $results
            );
        }

        if ($response['success']) {
            $this->log('Cache purged successfully');
            return array('success' => true, 'message' => 'Cache purged successfully');
        } else {
            $this->log('Cache purge failed: ' . $response['error'], 'error');
            return array('success' => false, 'message' => $response['error']);
        }
    }

    /**
     * Get analytics data
     */
    public function get_analytics($period = '24h') {
        if (!$this->is_configured()) {
            return array('success' => false, 'message' => 'BunnyCDN not configured');
        }

        $headers = array(
            'AccessKey' => $this->config['api_key'],
            'Content-Type' => 'application/json'
        );

        // Calculate date range
        $end_date = current_time('Y-m-d');
        $start_date = date('Y-m-d', strtotime('-' . $this->parse_period_to_days($period) . ' days'));

        $query_params = array(
            'dateFrom' => $start_date,
            'dateTo' => $end_date,
            'pullZone' => $this->config['pullzone_id']
        );

        $url = $this->api_base . '/statistics?' . http_build_query($query_params);

        $response = $this->make_api_request($url, array('headers' => $headers));

        if ($response['success']) {
            return array(
                'success' => true,
                'data' => $this->format_analytics_data($response['data'])
            );
        } else {
            return array('success' => false, 'message' => $response['error']);
        }
    }

    /**
     * Get cache statistics
     */
    public function get_cache_stats() {
        $analytics = $this->get_analytics('24h');
        
        if (!$analytics['success']) {
            return parent::get_cache_stats();
        }

        $data = $analytics['data'];
        
        return array(
            'cache_hit_ratio' => $data['cache_hit_ratio'] ?? 0,
            'bandwidth_saved' => $data['bandwidth_saved'] ?? 0,
            'requests_served' => $data['requests_served'] ?? 0,
            'data_transferred' => $data['data_transferred'] ?? 0
        );
    }

    /**
     * Setup cache rules
     */
    public function setup_cache_rules($rules = array()) {
        if (!$this->is_configured()) {
            return array('success' => false, 'message' => 'BunnyCDN not configured');
        }

        $headers = array(
            'AccessKey' => $this->config['api_key'],
            'Content-Type' => 'application/json'
        );

        // Update pull zone settings
        $pullzone_config = array(
            'CacheControlMaxAge' => intval($this->config['cache_control_max_age'] ?? 86400),
            'OptimizerEnabled' => !empty($this->config['optimizer_enabled']),
            'OptimizerImageQuality' => intval($this->config['optimizer_quality'] ?? 85),
            'VaryCache' => !empty($this->config['vary_cache']),
            'DisableCookies' => !empty($this->config['disable_cookies'])
        );

        $response = $this->make_api_request(
            $this->api_base . '/pullzone/' . $this->config['pullzone_id'],
            array(
                'method' => 'POST',
                'headers' => $headers,
                'body' => json_encode($pullzone_config)
            )
        );

        if ($response['success']) {
            return array('success' => true, 'message' => 'Cache rules updated successfully');
        } else {
            return array('success' => false, 'message' => $response['error']);
        }
    }

    /**
     * Get edge locations
     */
    public function get_edge_locations() {
        // BunnyCDN edge locations (as of 2024)
        return array(
            'North America' => array('New York', 'Los Angeles', 'Miami', 'Chicago', 'Seattle', 'Toronto'),
            'Europe' => array('London', 'Frankfurt', 'Amsterdam', 'Paris', 'Stockholm', 'Prague', 'Warsaw'),
            'Asia Pacific' => array('Singapore', 'Tokyo', 'Sydney', 'Hong Kong', 'Seoul', 'Mumbai'),
            'South America' => array('São Paulo', 'Santiago'),
            'Africa' => array('Johannesburg'),
            'Middle East' => array('Dubai')
        );
    }

    /**
     * Parse period to days
     */
    private function parse_period_to_days($period) {
        $periods = array(
            '1h' => 1,
            '24h' => 1,
            '7d' => 7,
            '30d' => 30
        );

        return isset($periods[$period]) ? $periods[$period] : 1;
    }

    /**
     * Format analytics data
     */
    private function format_analytics_data($raw_data) {
        if (empty($raw_data)) {
            return array(
                'cache_hit_ratio' => 0,
                'bandwidth_saved' => 0,
                'requests_served' => 0,
                'data_transferred' => 0
            );
        }

        $total_requests = 0;
        $cache_hits = 0;
        $total_bandwidth = 0;

        foreach ($raw_data as $day_data) {
            $total_requests += $day_data['Requests'] ?? 0;
            $cache_hits += $day_data['CacheHitRequests'] ?? 0;
            $total_bandwidth += $day_data['BandwidthUsed'] ?? 0;
        }

        $cache_hit_ratio = $total_requests > 0 ? round(($cache_hits / $total_requests) * 100, 2) : 0;
        $bandwidth_saved = $total_bandwidth * ($cache_hit_ratio / 100);

        return array(
            'cache_hit_ratio' => $cache_hit_ratio,
            'bandwidth_saved' => $this->format_bytes($bandwidth_saved),
            'requests_served' => $total_requests,
            'data_transferred' => $this->format_bytes($total_bandwidth)
        );
    }

    /**
     * Optimize for WordPress
     */
    public function optimize_for_wordpress() {
        $results = parent::optimize_for_wordpress();

        // Enable BunnyCDN-specific optimizations
        if ($this->is_configured()) {
            $optimizer_result = $this->setup_cache_rules();
            $results['bunny_optimizer'] = $optimizer_result;
        }

        return $results;
    }
}
