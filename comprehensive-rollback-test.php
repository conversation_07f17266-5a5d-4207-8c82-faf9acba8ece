<?php
/**
 * Comprehensive Rollback Test
 * This script provides a complete test of the rollback functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// Security check
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

echo "<h1>🧪 Comprehensive Rollback Test</h1>\n";

// Step 1: Check current state
$fix_history = get_option('redco_diagnostic_fix_history', array());
$rollback_history = get_option('redco_rollback_history', array());

echo "<h2>📊 Current State</h2>\n";
echo "<div style='background: #f0f0f1; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
echo "<ul>\n";
echo "<li><strong>Fix History Sessions:</strong> " . count($fix_history) . "</li>\n";
echo "<li><strong>Rollback History Entries:</strong> " . count($rollback_history) . "</li>\n";
echo "</ul>\n";
echo "</div>\n";

// Step 2: Create test data if needed
if (empty($fix_history)) {
    echo "<h2>🔧 Creating Test Data</h2>\n";
    
    $test_session = array(
        'session_id' => 'test_session_' . time(),
        'timestamp' => time(),
        'rollback_id' => 'test_rollback_' . time(),
        'backup_id' => 'test_backup_' . time(),
        'message' => 'Test fix session for rollback testing',
        'fixes_applied' => 1,
        'backup_created' => true,
        'details' => array(
            array(
                'issue_id' => 'test_issue_1',
                'success' => true,
                'rollback_id' => 'test_rollback_' . time()
            )
        )
    );
    
    $fix_history[] = $test_session;
    update_option('redco_diagnostic_fix_history', $fix_history);
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>✅ Test Data Created</h3>\n";
    echo "<p>Created test session with rollback ID: <strong>" . $test_session['rollback_id'] . "</strong></p>\n";
    echo "</div>\n";
}

// Step 3: Show available sessions for testing
echo "<h2>🎯 Available Sessions for Testing</h2>\n";

foreach ($fix_history as $index => $session) {
    $timestamp = $session['timestamp'] ?? time();
    $rollback_id = $session['rollback_id'] ?? $session['backup_id'] ?? 'none';
    $message = $session['message'] ?? 'No message';
    
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px 0; border-radius: 3px;'>\n";
    echo "<h4>Session " . ($index + 1) . "</h4>\n";
    echo "<ul>\n";
    echo "<li><strong>Timestamp:</strong> " . date('Y-m-d H:i:s', $timestamp) . " (" . human_time_diff($timestamp) . " ago)</li>\n";
    echo "<li><strong>Rollback ID:</strong> {$rollback_id}</li>\n";
    echo "<li><strong>Message:</strong> {$message}</li>\n";
    echo "</ul>\n";
    
    if ($rollback_id !== 'none') {
        echo "<p>\n";
        echo "<a href='?test_complete=" . urlencode($rollback_id) . "' style='background: #dc3232; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px; margin-right: 10px;'>🧪 Complete Test</a>\n";
        echo "<a href='?test_ajax_only=" . urlencode($rollback_id) . "' style='background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px;'>📡 AJAX Only</a>\n";
        echo "</p>\n";
    }
    echo "</div>\n";
}

// Handle complete test
$test_complete_id = $_GET['test_complete'] ?? '';
if (!empty($test_complete_id)) {
    echo "<h2>🧪 Complete Rollback Test for ID: {$test_complete_id}</h2>\n";
    
    // Step 1: Record initial state
    $initial_fix_history = get_option('redco_diagnostic_fix_history', array());
    $initial_count = count($initial_fix_history);
    
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>📊 Initial State</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>Fix History Sessions:</strong> {$initial_count}</li>\n";
    echo "<li><strong>Test Rollback ID:</strong> {$test_complete_id}</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    // Step 2: Execute AJAX rollback
    echo "<h3>📡 Executing AJAX Rollback</h3>\n";
    
    try {
        // Load the diagnostic class
        require_once('modules/diagnostic-autofix/class-diagnostic-autofix.php');
        $diagnostic = new Redco_Diagnostic_AutoFix();
        
        // Simulate the AJAX request
        $_POST['nonce'] = wp_create_nonce('redco_diagnostic_nonce');
        $_POST['backup_id'] = $test_complete_id;
        
        // Capture the output
        ob_start();
        
        // Call the rollback method
        $reflection = new ReflectionClass($diagnostic);
        $rollback_method = $reflection->getMethod('ajax_rollback_fixes');
        $rollback_method->setAccessible(true);
        
        $rollback_method->invoke($diagnostic);
        $ajax_output = ob_get_clean();
        
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<h4>✅ AJAX Rollback Executed</h4>\n";
        echo "<p><strong>Response Length:</strong> " . strlen($ajax_output) . " characters</p>\n";
        
        // Parse the JSON response
        $response = json_decode($ajax_output, true);
        if ($response) {
            if ($response['success']) {
                echo "<p><strong>Status:</strong> <span style='color: #28a745; font-weight: bold;'>SUCCESS</span></p>\n";
                if (isset($response['data']['message'])) {
                    echo "<p><strong>Message:</strong> " . htmlspecialchars($response['data']['message']) . "</p>\n";
                }
            } else {
                echo "<p><strong>Status:</strong> <span style='color: #dc3232; font-weight: bold;'>FAILED</span></p>\n";
                echo "<p><strong>Error:</strong> " . htmlspecialchars($response['data'] ?? 'Unknown error') . "</p>\n";
            }
        } else {
            echo "<p><strong>Status:</strong> <span style='color: #ffc107; font-weight: bold;'>INVALID JSON</span></p>\n";
            echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; max-height: 200px; overflow-y: auto;'>" . htmlspecialchars($ajax_output) . "</pre>\n";
        }
        echo "</div>\n";
        
        // Step 3: Check database state after rollback
        echo "<h3>📊 Database State After Rollback</h3>\n";
        
        $final_fix_history = get_option('redco_diagnostic_fix_history', array());
        $final_count = count($final_fix_history);
        $sessions_removed = $initial_count - $final_count;
        
        echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
        echo "<h4>📈 Database Changes</h4>\n";
        echo "<ul>\n";
        echo "<li><strong>Sessions Before:</strong> {$initial_count}</li>\n";
        echo "<li><strong>Sessions After:</strong> {$final_count}</li>\n";
        echo "<li><strong>Sessions Removed:</strong> {$sessions_removed}</li>\n";
        echo "</ul>\n";
        
        if ($sessions_removed > 0) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; border-radius: 3px;'>\n";
            echo "<p><strong>✅ SUCCESS:</strong> Database was properly updated! {$sessions_removed} session(s) removed.</p>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; border-radius: 3px;'>\n";
            echo "<p><strong>❌ ISSUE:</strong> No sessions were removed from the database despite successful rollback response.</p>\n";
            echo "</div>\n";
        }
        echo "</div>\n";
        
        // Step 4: Test frontend refresh
        echo "<h3>🔄 Frontend Refresh Test</h3>\n";
        
        // Simulate the AJAX request that loads recent fixes
        $_POST['action'] = 'redco_load_recent_fixes';
        
        ob_start();
        $load_fixes_method = $reflection->getMethod('ajax_load_recent_fixes');
        $load_fixes_method->setAccessible(true);
        $load_fixes_method->invoke($diagnostic);
        $load_fixes_output = ob_get_clean();
        
        $load_fixes_response = json_decode($load_fixes_output, true);
        
        echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
        echo "<h4>📋 Recent Fixes AJAX Response</h4>\n";
        
        if ($load_fixes_response && $load_fixes_response['success']) {
            $html = $load_fixes_response['data']['html'] ?? '';
            $count = $load_fixes_response['data']['count'] ?? 0;
            
            echo "<ul>\n";
            echo "<li><strong>Response Status:</strong> SUCCESS</li>\n";
            echo "<li><strong>Fix Count:</strong> {$count}</li>\n";
            echo "<li><strong>HTML Length:</strong> " . strlen($html) . " characters</li>\n";
            echo "</ul>\n";
            
            // Check if the rolled-back item is still in the HTML
            $rollback_still_present = strpos($html, $test_complete_id) !== false;
            
            if ($rollback_still_present) {
                echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; border-radius: 3px;'>\n";
                echo "<p><strong>❌ FRONTEND ISSUE:</strong> The rolled-back item is still present in the Recent Fixes HTML!</p>\n";
                echo "</div>\n";
            } else {
                echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; border-radius: 3px;'>\n";
                echo "<p><strong>✅ FRONTEND SUCCESS:</strong> The rolled-back item is no longer in the Recent Fixes HTML.</p>\n";
                echo "</div>\n";
            }
        } else {
            echo "<p><strong>Response Status:</strong> FAILED</p>\n";
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; border-radius: 3px;'>\n";
            echo "<p><strong>❌ AJAX ERROR:</strong> Failed to load recent fixes.</p>\n";
            echo "</div>\n";
        }
        echo "</div>\n";
        
        // Step 5: Final summary
        echo "<h3>📋 Test Summary</h3>\n";
        
        $database_success = $sessions_removed > 0;
        $frontend_success = isset($rollback_still_present) && !$rollback_still_present;
        $overall_success = $database_success && $frontend_success;
        
        $summary_color = $overall_success ? '#d4edda' : '#f8d7da';
        $summary_border = $overall_success ? '#c3e6cb' : '#f5c6cb';
        $summary_icon = $overall_success ? '✅' : '❌';
        $summary_status = $overall_success ? 'SUCCESS' : 'FAILED';
        
        echo "<div style='background: {$summary_color}; border: 1px solid {$summary_border}; padding: 15px; border-radius: 5px;'>\n";
        echo "<h4>{$summary_icon} Overall Test Result: {$summary_status}</h4>\n";
        echo "<ul>\n";
        echo "<li><strong>Database Update:</strong> " . ($database_success ? '✅ SUCCESS' : '❌ FAILED') . "</li>\n";
        echo "<li><strong>Frontend Update:</strong> " . ($frontend_success ? '✅ SUCCESS' : '❌ FAILED') . "</li>\n";
        echo "</ul>\n";
        
        if (!$overall_success) {
            echo "<h5>🔧 Recommended Actions:</h5>\n";
            echo "<ul>\n";
            if (!$database_success) {
                echo "<li>Check the error logs for database update issues</li>\n";
                echo "<li>Verify the backup ID matching logic in update_fix_history_for_rollback()</li>\n";
            }
            if (!$frontend_success) {
                echo "<li>Check cache clearing mechanisms</li>\n";
                echo "<li>Verify the Recent Fixes HTML generation logic</li>\n";
            }
            echo "</ul>\n";
        }
        echo "</div>\n";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>❌ Test Failed with Exception</h3>\n";
        echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>\n";
        echo "<p><strong>File:</strong> " . $e->getFile() . "</p>\n";
        echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>\n";
        echo "</div>\n";
    }
    
    echo "<h3>🎯 Next Steps</h3>\n";
    echo "<ol>\n";
    echo "<li>Check the <a href='check-error-logs.php'>error logs</a> for detailed rollback information</li>\n";
    echo "<li>Go to the <a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>Diagnostic & Auto-Fix module</a> to verify the UI</li>\n";
    echo "<li>Run another test to confirm the fix is working consistently</li>\n";
    echo "</ol>\n";
    
    echo "<p><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🚀 Go to Diagnostic Module</a></p>\n";
    echo "<p><a href='comprehensive-rollback-test.php' style='background: #666; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔄 Run Another Test</a></p>\n";
}

echo "<h2>🔧 Additional Tools</h2>\n";
echo "<ul>\n";
echo "<li><a href='check-error-logs.php'>📋 Check Error Logs</a></li>\n";
echo "<li><a href='test-ajax-rollback.php'>🧪 Test AJAX Endpoint</a></li>\n";
echo "<li><a href='debug-rollback-process.php'>🐛 Debug Rollback Process</a></li>\n";
echo "<li><a href='immediate-diagnostic-reset.php'>🗑️ Reset All Data</a></li>\n";
echo "</ul>\n";
?>
