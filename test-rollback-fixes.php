<?php
/**
 * Test Rollback State Management Fixes
 * This script validates that the rollback functionality properly removes items from Recent Fixes
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// Security check
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

echo "<h1>🧪 Test Rollback State Management Fixes</h1>\n";
echo "<p>This script validates the enhanced rollback functionality that should properly remove items from Recent Fixes.</p>\n";

// Check current state
$fix_history = get_option('redco_diagnostic_fix_history', array());
$fixed_issues = get_option('redco_fixed_issues', array());
$diagnostic_stats = get_option('redco_diagnostic_stats', array());

echo "<h2>📊 Current State</h2>\n";
echo "<div style='background: #f0f0f1; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
echo "<ul>\n";
echo "<li><strong>Fix History Sessions:</strong> " . count($fix_history) . "</li>\n";
echo "<li><strong>Fixed Issues:</strong> " . count($fixed_issues) . "</li>\n";
echo "<li><strong>Fixes Applied (Stats):</strong> " . ($diagnostic_stats['fixes_applied'] ?? 0) . "</li>\n";
echo "</ul>\n";
echo "</div>\n";

if (empty($fix_history)) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
    echo "<h3>⚠️ No Test Data Available</h3>\n";
    echo "<p>There are no fix sessions in the history to test rollback with. Please:</p>\n";
    echo "<ol>\n";
    echo "<li>Run the test data creation script: <a href='test-rollback-validation.php?create_test_data=yes'>Create Test Data</a></li>\n";
    echo "<li>Or apply some fixes through the Diagnostic module</li>\n";
    echo "<li>Then return here to test the rollback functionality</li>\n";
    echo "</ol>\n";
    echo "</div>\n";
    exit;
}

echo "<h2>🔧 Available Fix Sessions for Testing</h2>\n";
echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";

foreach ($fix_history as $index => $session) {
    $session_id = $session['session_id'] ?? 'unknown';
    $rollback_id = $session['rollback_id'] ?? $session['backup_id'] ?? 'unknown';
    $timestamp = $session['timestamp'] ?? time();
    $message = $session['message'] ?? 'No message';
    $details_count = isset($session['details']) ? count($session['details']) : 0;
    
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px 0; border-radius: 3px;'>\n";
    echo "<h4>Session " . ($index + 1) . "</h4>\n";
    echo "<ul>\n";
    echo "<li><strong>Session ID:</strong> {$session_id}</li>\n";
    echo "<li><strong>Rollback ID:</strong> {$rollback_id}</li>\n";
    echo "<li><strong>Timestamp:</strong> " . date('Y-m-d H:i:s', $timestamp) . "</li>\n";
    echo "<li><strong>Message:</strong> {$message}</li>\n";
    echo "<li><strong>Fix Details:</strong> {$details_count} items</li>\n";
    echo "</ul>\n";
    
    // Show test button for this session
    echo "<p><a href='?test_rollback=" . urlencode($rollback_id) . "' style='background: #dc3232; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px; font-size: 12px;'>🧪 Test Rollback</a></p>\n";
    echo "</div>\n";
}

echo "</div>\n";

// Handle rollback test
$test_rollback_id = $_GET['test_rollback'] ?? '';
if (!empty($test_rollback_id)) {
    echo "<h2>🧪 Testing Rollback for ID: {$test_rollback_id}</h2>\n";
    
    // Find the session being rolled back
    $target_session = null;
    foreach ($fix_history as $session) {
        $session_rollback_id = $session['rollback_id'] ?? $session['backup_id'] ?? '';
        if ($session_rollback_id === $test_rollback_id) {
            $target_session = $session;
            break;
        }
    }
    
    if (!$target_session) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<p><strong>Error:</strong> Session with rollback ID '{$test_rollback_id}' not found.</p>\n";
        echo "</div>\n";
        exit;
    }
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
    echo "<h3>📋 Session Details</h3>\n";
    echo "<pre>" . print_r($target_session, true) . "</pre>\n";
    echo "</div>\n";
    
    // Simulate the rollback process (database updates only)
    echo "<h3>🔄 Simulating Rollback Process...</h3>\n";
    
    // Step 1: Remove session from fix history
    $updated_history = array();
    $removed_session = null;
    $rolled_back_issue_ids = array();
    
    foreach ($fix_history as $session) {
        $session_rollback_id = $session['rollback_id'] ?? $session['backup_id'] ?? '';
        if ($session_rollback_id === $test_rollback_id) {
            $removed_session = $session;
            // Collect issue IDs
            if (isset($session['details']) && is_array($session['details'])) {
                foreach ($session['details'] as $detail) {
                    if (isset($detail['issue_id']) && $detail['success']) {
                        $rolled_back_issue_ids[] = $detail['issue_id'];
                    }
                }
            }
            continue; // Skip this session
        }
        $updated_history[] = $session;
    }
    
    echo "✅ Session removed from history (was " . count($fix_history) . " sessions, now " . count($updated_history) . ")<br>\n";
    echo "✅ Found " . count($rolled_back_issue_ids) . " issue IDs to remove from fixed issues<br>\n";
    
    // Step 2: Remove fixed issues
    $updated_fixed_issues = $fixed_issues;
    $removed_issues_count = 0;
    foreach ($rolled_back_issue_ids as $issue_id) {
        if (isset($updated_fixed_issues[$issue_id])) {
            unset($updated_fixed_issues[$issue_id]);
            $removed_issues_count++;
        }
    }
    
    echo "✅ Removed {$removed_issues_count} issues from fixed issues list<br>\n";
    
    // Step 3: Update statistics
    $updated_stats = $diagnostic_stats;
    $updated_stats['fixes_applied'] = count($updated_history);
    $updated_stats['last_updated'] = time();
    
    echo "✅ Updated statistics: fixes_applied = " . $updated_stats['fixes_applied'] . "<br>\n";
    
    // Step 4: Apply updates to database
    $history_updated = update_option('redco_diagnostic_fix_history', $updated_history);
    $issues_updated = update_option('redco_fixed_issues', $updated_fixed_issues);
    $stats_updated = update_option('redco_diagnostic_stats', $updated_stats);
    
    // Step 5: Clear caches
    delete_transient('redco_recent_fixes');
    wp_cache_delete('redco_recent_fixes');
    delete_transient('redco_diagnostic_stats');
    wp_cache_delete('redco_diagnostic_stats');
    
    echo "✅ Cleared caches<br>\n";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 15px 0;'>\n";
    echo "<h3>✅ Rollback Simulation Complete</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>Fix History Updated:</strong> " . ($history_updated ? 'Yes' : 'No') . "</li>\n";
    echo "<li><strong>Fixed Issues Updated:</strong> " . ($issues_updated ? 'Yes' : 'No') . "</li>\n";
    echo "<li><strong>Statistics Updated:</strong> " . ($stats_updated ? 'Yes' : 'No') . "</li>\n";
    echo "<li><strong>Sessions Before:</strong> " . count($fix_history) . "</li>\n";
    echo "<li><strong>Sessions After:</strong> " . count($updated_history) . "</li>\n";
    echo "<li><strong>Fixed Issues Before:</strong> " . count($fixed_issues) . "</li>\n";
    echo "<li><strong>Fixed Issues After:</strong> " . count($updated_fixed_issues) . "</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    echo "<h3>🎯 Next Steps</h3>\n";
    echo "<ol>\n";
    echo "<li>Go to the <a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>Diagnostic & Auto-Fix module</a></li>\n";
    echo "<li>Check the Recent Fixes section - it should now show " . count($updated_history) . " items (reduced by 1)</li>\n";
    echo "<li>Verify that the statistics show the correct number of fixes applied</li>\n";
    echo "<li>The rolled-back session should no longer appear in the Recent Fixes list</li>\n";
    echo "</ol>\n";
    
    echo "<p><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🚀 Go to Diagnostic Module</a></p>\n";
    echo "<p><a href='test-rollback-fixes.php' style='background: #666; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔄 Test Another Rollback</a></p>\n";
    
    exit;
}

echo "<h3>🎯 Testing Instructions</h3>\n";
echo "<ol>\n";
echo "<li><strong>Choose a Session:</strong> Click 'Test Rollback' on any session above</li>\n";
echo "<li><strong>Verify Database Changes:</strong> The script will simulate the rollback and show the results</li>\n";
echo "<li><strong>Check UI:</strong> Go to the Diagnostic module and verify the Recent Fixes list is updated</li>\n";
echo "<li><strong>Test Frontend:</strong> Use the actual rollback buttons in the UI to test the complete flow</li>\n";
echo "</ol>\n";

echo "<div style='background: #e2e3e5; border: 1px solid #d6d8db; padding: 15px; border-radius: 5px; margin: 15px 0;'>\n";
echo "<h4>🔍 What to Look For</h4>\n";
echo "<ul>\n";
echo "<li><strong>Immediate DOM Removal:</strong> The rolled-back item should disappear from the list immediately</li>\n";
echo "<li><strong>Database Consistency:</strong> The fix history should be updated in the database</li>\n";
echo "<li><strong>Statistics Update:</strong> The 'Fixes Applied' count should decrease by 1</li>\n";
echo "<li><strong>Cache Clearing:</strong> Fresh data should load without requiring page refresh</li>\n";
echo "<li><strong>No Duplicates:</strong> The rolled-back item should not reappear after refresh</li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "<p><a href='immediate-diagnostic-reset.php' style='background: #dc3232; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🗑️ Reset All Data</a></p>\n";
echo "<p><a href='test-rollback-validation.php?create_test_data=yes' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔧 Create Fresh Test Data</a></p>\n";
?>
