/**
 * Auto-Save Loading Indicator Styles
 * 
 * Provides visual feedback during auto-save operations
 * with accessible and unobtrusive design.
 */

/* Main loading indicator */
.redco-loading-indicator {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    background: rgba(255, 255, 255, 0.95) !important;
    border: 1px solid #ddd !important;
    border-radius: 6px !important;
    padding: 8px 12px !important;
    display: none !important;
    align-items: center !important;
    gap: 8px !important;
    font-size: 13px !important;
    color: #333 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    z-index: 999999 !important;
    backdrop-filter: blur(2px) !important;
    transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out !important;
    transform: translateY(-10px) !important;
    opacity: 0 !important;
    pointer-events: none !important;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, Oxygen-San<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Can<PERSON>ell, "Helvetica Neue", sans-serif !important;
}

/* Show state */
.redco-loading-indicator.show {
    display: flex !important;
    opacity: 1 !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
}

/* Hide state */
.redco-loading-indicator.hide {
    opacity: 0;
    transform: translateY(-10px);
}

/* Spinner container */
.redco-loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
}

/* Spinner SVG animation */
.redco-loading-spinner svg {
    width: 16px;
    height: 16px;
    animation: redco-spin 1s linear infinite;
}

/* Spinner rotation animation */
@keyframes redco-spin {
    from { 
        transform: rotate(0deg); 
    }
    to { 
        transform: rotate(360deg); 
    }
}

/* Loading text */
.redco-loading-text {
    font-weight: 500;
    white-space: nowrap;
    line-height: 1;
}

/* Alternative form-specific loading indicator */
.redco-form-loading {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 6px;
    color: #666;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Ensure form containers have relative positioning */
.redco-module-form {
    position: relative;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .redco-loading-indicator {
        background: rgba(45, 45, 45, 0.95);
        border-color: #555;
        color: #e0e0e0;
    }
    
    .redco-loading-spinner svg circle {
        stroke: #4a9eff;
    }
    
    .redco-form-loading {
        background: rgba(45, 45, 45, 0.9);
        border-color: #555;
        color: #ccc;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .redco-loading-indicator {
        background: #ffffff;
        border: 2px solid #000000;
        color: #000000;
    }
    
    .redco-loading-spinner svg circle {
        stroke: #000000;
        stroke-width: 3;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .redco-loading-indicator {
        transition: opacity 0.1s ease-in-out;
    }
    
    .redco-loading-spinner svg {
        animation: none;
    }
    
    @keyframes redco-spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(0deg); }
    }
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .redco-loading-indicator {
        top: 10px;
        right: 10px;
        padding: 6px 10px;
        font-size: 12px;
    }
    
    .redco-loading-spinner {
        width: 14px;
        height: 14px;
    }
    
    .redco-loading-spinner svg {
        width: 14px;
        height: 14px;
    }
}

/* WordPress admin bar adjustment */
.admin-bar .redco-loading-indicator {
    top: 52px;
}

@media screen and (max-width: 782px) {
    .admin-bar .redco-loading-indicator {
        top: 56px;
    }
}

/* Focus styles for accessibility */
.redco-loading-indicator:focus-within {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

/* Success state (brief green flash) */
.redco-loading-indicator.success {
    background: rgba(70, 180, 80, 0.95);
    color: white;
    border-color: #46b450;
}

.redco-loading-indicator.success .redco-loading-spinner svg circle {
    stroke: white;
}

/* Error state (brief red flash) */
.redco-loading-indicator.error {
    background: rgba(220, 53, 69, 0.95);
    color: white;
    border-color: #dc3545;
}

.redco-loading-indicator.error .redco-loading-spinner svg circle {
    stroke: white;
}

/* Pulse animation for attention */
@keyframes redco-pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.redco-loading-indicator.pulse {
    animation: redco-pulse 1.5s ease-in-out infinite;
}

/* Screen reader only text */
.redco-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Loading indicator variants */
.redco-loading-indicator.compact {
    padding: 4px 8px;
    font-size: 11px;
    gap: 4px;
}

.redco-loading-indicator.compact .redco-loading-spinner {
    width: 12px;
    height: 12px;
}

.redco-loading-indicator.compact .redco-loading-spinner svg {
    width: 12px;
    height: 12px;
}

/* Tooltip-style positioning */
.redco-loading-indicator.tooltip {
    position: absolute;
    top: -40px;
    right: 0;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 10000;
}

.redco-loading-indicator.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    right: 10px;
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.8);
}
