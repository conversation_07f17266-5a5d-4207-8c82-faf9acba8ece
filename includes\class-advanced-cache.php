<?php
/**
 * Advanced Caching System for Redco Optimizer
 * 
 * Multi-layer caching with intelligent cache management
 * 
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Advanced_Cache {
    
    /**
     * Cache layers
     */
    const LAYER_MEMORY = 'memory';
    const LAYER_TRANSIENT = 'transient';
    const LAYER_FILE = 'file';
    const LAYER_OBJECT = 'object';
    
    /**
     * Unified cache groups for all modules
     */
    const GROUP_STATS = 'stats';
    const GROUP_SETTINGS = 'settings';
    const GROUP_API = 'api';
    const GROUP_QUERIES = 'queries';
    const GROUP_PAGES = 'pages';
    const GROUP_ASSETS = 'assets';
    const GROUP_CRITICAL_CSS = 'critical_css';
    const GROUP_OPTIMIZED_FILES = 'optimized_files';
    const GROUP_WEBP_CONVERSION = 'webp_conversion';
    
    /**
     * Memory cache
     */
    private static $memory_cache = array();
    
    /**
     * Cache statistics
     */
    private static $cache_stats = array(
        'hits' => 0,
        'misses' => 0,
        'sets' => 0,
        'deletes' => 0
    );
    
    /**
     * Cache configuration
     */
    private static $config = array(
        'default_expiration' => 3600,
        'max_memory_items' => 100,
        'file_cache_enabled' => true,
        'object_cache_enabled' => true
    );
    
    /**
     * Initialize advanced caching
     */
    public static function init() {
        // Load configuration
        self::load_config();
        
        // Set up cache directories
        self::setup_cache_directories();
        
        // Add cache management hooks
        add_action('wp_loaded', array(__CLASS__, 'setup_object_cache'));
        add_action('redco_clear_cache', array(__CLASS__, 'clear_all_cache'));
        add_action('redco_cache_maintenance', array(__CLASS__, 'cache_maintenance'));
        
        // Schedule cache maintenance
        if (!wp_next_scheduled('redco_cache_maintenance')) {
            wp_schedule_event(time(), 'hourly', 'redco_cache_maintenance');
        }
        
        // Add admin bar cache controls
        if (current_user_can('manage_options')) {
            add_action('admin_bar_menu', array(__CLASS__, 'add_admin_bar_cache_controls'), 100);
        }
    }
    
    /**
     * Get cached value with multi-layer fallback
     * 
     * @param string $key Cache key
     * @param string $group Cache group
     * @param mixed $default Default value if not found
     * @return mixed Cached value or default
     */
    public static function get($key, $group = self::GROUP_STATS, $default = null) {
        $cache_key = self::build_cache_key($key, $group);
        
        // Try memory cache first
        if (isset(self::$memory_cache[$cache_key])) {
            $cache_data = self::$memory_cache[$cache_key];
            if (time() < $cache_data['expires']) {
                self::$cache_stats['hits']++;
                return $cache_data['value'];
            } else {
                unset(self::$memory_cache[$cache_key]);
            }
        }
        
        // Try object cache
        if (self::$config['object_cache_enabled'] && function_exists('wp_cache_get')) {
            $value = wp_cache_get($cache_key, 'redco_optimizer');
            if ($value !== false) {
                // Store in memory cache for faster access
                self::set_memory_cache($cache_key, $value, 300); // 5 minutes in memory
                self::$cache_stats['hits']++;
                return $value;
            }
        }
        
        // Try transient cache
        $transient_key = 'redco_' . $cache_key;
        $value = get_transient($transient_key);
        if ($value !== false) {
            // Store in higher layers for faster access
            self::set_memory_cache($cache_key, $value, 300);
            if (self::$config['object_cache_enabled'] && function_exists('wp_cache_set')) {
                wp_cache_set($cache_key, $value, 'redco_optimizer', 300);
            }
            self::$cache_stats['hits']++;
            return $value;
        }
        
        // Try file cache
        if (self::$config['file_cache_enabled']) {
            $file_value = self::get_file_cache($cache_key);
            if ($file_value !== null) {
                // Store in higher layers
                self::set_memory_cache($cache_key, $file_value, 300);
                set_transient($transient_key, $file_value, 1800); // 30 minutes
                if (self::$config['object_cache_enabled'] && function_exists('wp_cache_set')) {
                    wp_cache_set($cache_key, $file_value, 'redco_optimizer', 300);
                }
                self::$cache_stats['hits']++;
                return $file_value;
            }
        }
        
        self::$cache_stats['misses']++;
        return $default;
    }
    
    /**
     * Set cached value in all appropriate layers
     * 
     * @param string $key Cache key
     * @param mixed $value Value to cache
     * @param int $expiration Expiration time in seconds
     * @param string $group Cache group
     * @return bool Success
     */
    public static function set($key, $value, $expiration = null, $group = self::GROUP_STATS) {
        $expiration = $expiration ?: self::$config['default_expiration'];
        $cache_key = self::build_cache_key($key, $group);
        
        self::$cache_stats['sets']++;
        
        // Set in memory cache
        self::set_memory_cache($cache_key, $value, $expiration);
        
        // Set in object cache
        if (self::$config['object_cache_enabled'] && function_exists('wp_cache_set')) {
            wp_cache_set($cache_key, $value, 'redco_optimizer', $expiration);
        }
        
        // Set in transient cache
        $transient_key = 'redco_' . $cache_key;
        set_transient($transient_key, $value, $expiration);
        
        // Set in file cache for longer persistence
        if (self::$config['file_cache_enabled']) {
            self::set_file_cache($cache_key, $value, $expiration);
        }
        
        return true;
    }
    
    /**
     * Delete cached value from all layers
     * 
     * @param string $key Cache key
     * @param string $group Cache group
     * @return bool Success
     */
    public static function delete($key, $group = self::GROUP_STATS) {
        $cache_key = self::build_cache_key($key, $group);
        
        self::$cache_stats['deletes']++;
        
        // Delete from memory cache
        unset(self::$memory_cache[$cache_key]);
        
        // Delete from object cache
        if (self::$config['object_cache_enabled'] && function_exists('wp_cache_delete')) {
            wp_cache_delete($cache_key, 'redco_optimizer');
        }
        
        // Delete from transient cache
        $transient_key = 'redco_' . $cache_key;
        delete_transient($transient_key);
        
        // Delete from file cache
        if (self::$config['file_cache_enabled']) {
            self::delete_file_cache($cache_key);
        }
        
        return true;
    }
    
    /**
     * Get or set cached value with callback
     * 
     * @param string $key Cache key
     * @param callable $callback Function to generate value if not cached
     * @param int $expiration Expiration time in seconds
     * @param string $group Cache group
     * @return mixed Cached or generated value
     */
    public static function remember($key, $callback, $expiration = null, $group = self::GROUP_STATS) {
        $value = self::get($key, $group);
        
        if ($value === null) {
            $value = call_user_func($callback);
            if ($value !== null) {
                self::set($key, $value, $expiration, $group);
            }
        }
        
        return $value;
    }
    
    /**
     * Clear cache by group
     *
     * @param string $group Cache group to clear
     * @return bool Success
     */
    public static function clear_group($group) {
        // Clear memory cache for group
        foreach (self::$memory_cache as $key => $data) {
            if (strpos($key, $group . '_') === 0) {
                unset(self::$memory_cache[$key]);
            }
        }
        
        // Clear transients for group
        global $wpdb;
        $wpdb->query($wpdb->prepare("
            DELETE FROM {$wpdb->options} 
            WHERE option_name LIKE %s
        ", '_transient_redco_' . $group . '_%'));
        
        // Clear file cache for group
        if (self::$config['file_cache_enabled']) {
            $cache_dir = Redco_Config::get_cache_dir('cache') . '/' . $group;
            if (is_dir($cache_dir)) {
                $files = glob($cache_dir . '/*');
                foreach ($files as $file) {
                    if (is_file($file)) {
                        unlink($file);
                    }
                }
            }
        }
        
        Redco_Error_Handler::info(
            "Cleared cache group: {$group}",
            Redco_Error_Handler::CONTEXT_CACHE
        );
        
        return true;
    }

    /**
     * Alias for clear_group() for backward compatibility
     *
     * @param string $group Cache group to clear
     * @return bool Success
     */
    public static function flush_group($group) {
        return self::clear_group($group);
    }

    /**
     * Clear all cache
     */
    public static function clear_all_cache() {
        // Clear memory cache
        self::$memory_cache = array();
        
        // Clear object cache
        if (self::$config['object_cache_enabled'] && function_exists('wp_cache_flush_group')) {
            wp_cache_flush_group('redco_optimizer');
        }
        
        // Clear all transients
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_redco_%'");
        
        // Clear file cache
        if (self::$config['file_cache_enabled']) {
            $cache_dir = Redco_Config::get_cache_dir('cache');
            if (is_dir($cache_dir)) {
                $files = glob($cache_dir . '/*');
                foreach ($files as $file) {
                    if (is_file($file)) {
                        unlink($file);
                    } elseif (is_dir($file)) {
                        $subfiles = glob($file . '/*');
                        foreach ($subfiles as $subfile) {
                            if (is_file($subfile)) {
                                unlink($subfile);
                            }
                        }
                    }
                }
            }
        }
        
        // TEMPORARILY DISABLED: Cache clearing logging
        /*
        Redco_Error_Handler::info(
            "Cleared all cache layers",
            Redco_Error_Handler::CONTEXT_CACHE
        );
        */
    }
    
    /**
     * Build cache key
     * 
     * @param string $key Base key
     * @param string $group Cache group
     * @return string Full cache key
     */
    private static function build_cache_key($key, $group) {
        return $group . '_' . md5($key);
    }
    
    /**
     * Set memory cache
     * 
     * @param string $cache_key Cache key
     * @param mixed $value Value
     * @param int $expiration Expiration time
     */
    private static function set_memory_cache($cache_key, $value, $expiration) {
        // Limit memory cache size
        if (count(self::$memory_cache) >= self::$config['max_memory_items']) {
            // Remove oldest item
            $oldest_key = array_keys(self::$memory_cache)[0];
            unset(self::$memory_cache[$oldest_key]);
        }
        
        self::$memory_cache[$cache_key] = array(
            'value' => $value,
            'expires' => time() + $expiration
        );
    }
    
    /**
     * Get file cache
     * 
     * @param string $cache_key Cache key
     * @return mixed Cached value or null
     */
    private static function get_file_cache($cache_key) {
        $cache_file = self::get_cache_file_path($cache_key);
        
        if (!file_exists($cache_file)) {
            return null;
        }
        
        $cache_data = file_get_contents($cache_file);
        if ($cache_data === false) {
            return null;
        }
        
        $cache_data = unserialize($cache_data);
        if (!is_array($cache_data) || !isset($cache_data['expires']) || !isset($cache_data['value'])) {
            return null;
        }
        
        if (time() > $cache_data['expires']) {
            unlink($cache_file);
            return null;
        }
        
        return $cache_data['value'];
    }
    
    /**
     * Set file cache
     * 
     * @param string $cache_key Cache key
     * @param mixed $value Value
     * @param int $expiration Expiration time
     */
    private static function set_file_cache($cache_key, $value, $expiration) {
        $cache_file = self::get_cache_file_path($cache_key);
        $cache_dir = dirname($cache_file);
        
        if (!is_dir($cache_dir)) {
            wp_mkdir_p($cache_dir);
        }
        
        $cache_data = array(
            'value' => $value,
            'expires' => time() + $expiration
        );
        
        file_put_contents($cache_file, serialize($cache_data), LOCK_EX);
    }
    
    /**
     * Delete file cache
     * 
     * @param string $cache_key Cache key
     */
    private static function delete_file_cache($cache_key) {
        $cache_file = self::get_cache_file_path($cache_key);
        if (file_exists($cache_file)) {
            unlink($cache_file);
        }
    }
    
    /**
     * Get cache file path
     * 
     * @param string $cache_key Cache key
     * @return string File path
     */
    private static function get_cache_file_path($cache_key) {
        $cache_dir = Redco_Config::get_cache_dir('cache');
        $group = explode('_', $cache_key)[0];
        return $cache_dir . '/' . $group . '/' . $cache_key . '.cache';
    }
    
    /**
     * Load configuration
     */
    private static function load_config() {
        $config = get_option('redco_optimizer_cache_config', array());
        self::$config = wp_parse_args($config, self::$config);
    }
    
    /**
     * Setup unified cache directories for all modules
     */
    private static function setup_cache_directories() {
        $cache_dir = Redco_Config::get_cache_dir('cache');

        $groups = array(
            self::GROUP_STATS,
            self::GROUP_SETTINGS,
            self::GROUP_API,
            self::GROUP_QUERIES,
            self::GROUP_PAGES,
            self::GROUP_ASSETS,
            self::GROUP_CRITICAL_CSS,
            self::GROUP_OPTIMIZED_FILES,
            self::GROUP_WEBP_CONVERSION
        );

        foreach ($groups as $group) {
            $group_dir = $cache_dir . '/' . $group;
            if (!is_dir($group_dir)) {
                wp_mkdir_p($group_dir);
            }
        }
    }

    /**
     * Unified cache clearing for all modules
     */
    public static function clear_module_cache($module_key) {
        switch ($module_key) {
            case 'asset-optimization':
                self::clear_group_cache(self::GROUP_ASSETS);
                self::clear_group_cache(self::GROUP_CRITICAL_CSS);
                self::clear_group_cache(self::GROUP_OPTIMIZED_FILES);
                break;

            case 'page-cache':
                self::clear_group_cache(self::GROUP_PAGES);
                break;

            case 'smart-webp-conversion':
                self::clear_group_cache(self::GROUP_WEBP_CONVERSION);
                break;

            case 'all':
                self::clear_all_cache();
                break;
        }
    }

    /**
     * Clear cache for specific group
     */
    public static function clear_group_cache($group) {
        $cache_dir = Redco_Config::get_cache_dir('cache');
        $group_dir = $cache_dir . '/' . $group;

        if (is_dir($group_dir)) {
            $files = glob($group_dir . '/*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }

        // Clear memory cache for this group
        foreach (self::$memory_cache as $key => $data) {
            if (strpos($key, $group . '_') === 0) {
                unset(self::$memory_cache[$key]);
            }
        }

        // Clear transients for this group
        global $wpdb;
        $wpdb->query($wpdb->prepare(
            "DELETE FROM {$wpdb->options} WHERE option_name LIKE %s",
            '_transient_redco_' . $group . '_%'
        ));
    }

    /**
     * Alias for clear_all_cache() for backward compatibility
     *
     * @return bool Success
     */
    public static function flush_all_cache() {
        self::clear_all_cache();
        return true;
    }

    /**
     * Setup object cache
     */
    public static function setup_object_cache() {
        if (function_exists('wp_cache_add_global_groups')) {
            wp_cache_add_global_groups(array('redco_optimizer'));
        }
    }
    
    /**
     * Cache maintenance
     */
    public static function cache_maintenance() {
        // Clean expired file cache
        if (self::$config['file_cache_enabled']) {
            $cache_dir = Redco_Config::get_cache_dir('cache');
            $files = glob($cache_dir . '/*/*.cache');
            
            $cleaned = 0;
            foreach ($files as $file) {
                $cache_data = file_get_contents($file);
                if ($cache_data !== false) {
                    $cache_data = unserialize($cache_data);
                    if (is_array($cache_data) && isset($cache_data['expires']) && time() > $cache_data['expires']) {
                        unlink($file);
                        $cleaned++;
                    }
                }
            }
            
            if ($cleaned > 0) {
                Redco_Error_Handler::info(
                    "Cache maintenance: cleaned {$cleaned} expired files",
                    Redco_Error_Handler::CONTEXT_CACHE
                );
            }
        }
        
        // Clean expired memory cache
        foreach (self::$memory_cache as $key => $data) {
            if (time() >= $data['expires']) {
                unset(self::$memory_cache[$key]);
            }
        }
    }
    
    /**
     * Get cache statistics
     * 
     * @return array Cache statistics
     */
    public static function get_stats() {
        $stats = self::$cache_stats;
        $stats['memory_items'] = count(self::$memory_cache);
        $stats['hit_ratio'] = $stats['hits'] + $stats['misses'] > 0 
            ? round($stats['hits'] / ($stats['hits'] + $stats['misses']) * 100, 2) 
            : 0;
        
        return $stats;
    }
    
    /**
     * Add admin bar cache controls
     *
     * @param WP_Admin_Bar $wp_admin_bar Admin bar object
     */
    public static function add_admin_bar_cache_controls($wp_admin_bar) {
        $wp_admin_bar->add_node(array(
            'id' => 'redco-cache',
            'title' => 'Redco Cache',
            'href' => '#'
        ));

        $wp_admin_bar->add_node(array(
            'id' => 'redco-cache-clear',
            'parent' => 'redco-cache',
            'title' => 'Clear All Cache',
            'href' => wp_nonce_url(admin_url('admin-post.php?action=redco_clear_cache'), 'redco_clear_cache')
        ));

        $wp_admin_bar->add_node(array(
            'id' => 'redco-cache-stats',
            'parent' => 'redco-cache',
            'title' => 'Cache Stats',
            'href' => admin_url('admin.php?page=redco-optimizer&tab=performance')
        ));
    }

    /**
     * Handle cache clear admin action
     */
    public static function handle_admin_cache_clear() {
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        if (!wp_verify_nonce($_GET['_wpnonce'], 'redco_clear_cache')) {
            wp_die('Security check failed');
        }

        self::clear_all_cache();

        wp_redirect(wp_get_referer() ?: admin_url());
        exit;
    }
}
