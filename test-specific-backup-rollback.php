<?php
/**
 * Test Rollback with Specific Backup ID
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// Security check
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

echo "<h1>🎯 Test Rollback with Specific Backup ID</h1>\n";

$specific_backup_id = 'backup_2025-06-06_07-41-34_68429bae30c91';

echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
echo "<h3>🎯 Target Backup ID</h3>\n";
echo "<p><strong>Backup ID:</strong> <code>{$specific_backup_id}</code></p>\n";
echo "</div>\n";

// Get current fix history
$fix_history = get_option('redco_diagnostic_fix_history', array());

echo "<h2>📊 Current State</h2>\n";
echo "<div style='background: #f0f0f1; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
echo "<ul>\n";
echo "<li><strong>Fix History Sessions:</strong> " . count($fix_history) . "</li>\n";
echo "</ul>\n";
echo "</div>\n";

// Check if the specific backup ID exists
$backup_found = false;
$matching_session_index = -1;

foreach ($fix_history as $index => $session) {
    $session_rollback_id = $session['rollback_id'] ?? '';
    $session_backup_id = $session['backup_id'] ?? '';
    
    if ($session_rollback_id === $specific_backup_id || $session_backup_id === $specific_backup_id) {
        $backup_found = true;
        $matching_session_index = $index;
        break;
    }
}

if (!$backup_found) {
    echo "<h2>🔧 Creating Test Session with Specific Backup ID</h2>\n";
    
    // Create a test session with the specific backup ID
    $test_session = array(
        'session_id' => 'test_session_' . time(),
        'timestamp' => time(),
        'rollback_id' => $specific_backup_id,
        'backup_id' => $specific_backup_id,
        'message' => 'Test session for specific backup ID rollback',
        'fixes_applied' => 2,
        'backup_created' => true,
        'details' => array(
            array(
                'issue_id' => 'test_issue_security_headers',
                'success' => true,
                'rollback_id' => $specific_backup_id,
                'message' => 'Security headers added to .htaccess'
            ),
            array(
                'issue_id' => 'test_issue_compression',
                'success' => true,
                'rollback_id' => $specific_backup_id,
                'message' => 'Compression enabled'
            )
        )
    );
    
    $fix_history[] = $test_session;
    $updated = update_option('redco_diagnostic_fix_history', $fix_history);
    
    if ($updated) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>✅ Test Session Created</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Session ID:</strong> " . $test_session['session_id'] . "</li>\n";
        echo "<li><strong>Rollback ID:</strong> " . $test_session['rollback_id'] . "</li>\n";
        echo "<li><strong>Backup ID:</strong> " . $test_session['backup_id'] . "</li>\n";
        echo "<li><strong>Fixes Applied:</strong> " . $test_session['fixes_applied'] . "</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        $backup_found = true;
        $matching_session_index = count($fix_history) - 1;
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<p>❌ Failed to create test session</p>\n";
        echo "</div>\n";
        exit;
    }
}

if ($backup_found) {
    echo "<h2>✅ Backup ID Found - Ready for Testing</h2>\n";
    
    $session = $fix_history[$matching_session_index];
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>📋 Session Details</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>Session Index:</strong> {$matching_session_index}</li>\n";
    echo "<li><strong>Timestamp:</strong> " . date('Y-m-d H:i:s', $session['timestamp'] ?? time()) . "</li>\n";
    echo "<li><strong>Message:</strong> " . ($session['message'] ?? 'No message') . "</li>\n";
    echo "<li><strong>Fixes Applied:</strong> " . ($session['fixes_applied'] ?? 0) . "</li>\n";
    echo "<li><strong>Details Count:</strong> " . (isset($session['details']) ? count($session['details']) : 0) . "</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
}

// Test rollback functionality
if (isset($_GET['execute_rollback'])) {
    echo "<h2>🧪 Executing Rollback Test</h2>\n";
    
    $initial_count = count($fix_history);
    
    try {
        // Load the diagnostic class
        require_once('modules/diagnostic-autofix/class-diagnostic-autofix.php');
        $diagnostic = new Redco_Diagnostic_AutoFix();
        
        // Set up the POST data
        $_POST = array(
            'action' => 'redco_rollback_fixes',
            'backup_id' => $specific_backup_id,
            'nonce' => wp_create_nonce('redco_diagnostic_nonce')
        );
        
        echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>📡 AJAX Request Details</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Action:</strong> redco_rollback_fixes</li>\n";
        echo "<li><strong>Backup ID:</strong> {$specific_backup_id}</li>\n";
        echo "<li><strong>Nonce:</strong> " . $_POST['nonce'] . "</li>\n";
        echo "<li><strong>Initial Session Count:</strong> {$initial_count}</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        // Capture the output
        ob_start();
        $diagnostic->ajax_rollback_fixes();
        $output = ob_get_clean();
        
        echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>📤 Raw AJAX Output</h3>\n";
        echo "<p><strong>Length:</strong> " . strlen($output) . " characters</p>\n";
        echo "<pre style='background: #ffffff; padding: 10px; border: 1px solid #ccc; border-radius: 3px; max-height: 300px; overflow-y: auto; font-size: 12px;'>" . htmlspecialchars($output) . "</pre>\n";
        echo "</div>\n";
        
        // Parse JSON response
        $response = json_decode($output, true);
        
        if ($response && isset($response['success'])) {
            if ($response['success']) {
                echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
                echo "<h3>✅ Rollback AJAX Success</h3>\n";
                echo "<p><strong>Message:</strong> " . htmlspecialchars($response['data']['message'] ?? 'No message') . "</p>\n";
                
                if (isset($response['data']['files_restored'])) {
                    echo "<p><strong>Files Restored:</strong> " . $response['data']['files_restored'] . "</p>\n";
                }
                if (isset($response['data']['options_restored'])) {
                    echo "<p><strong>Options Restored:</strong> " . $response['data']['options_restored'] . "</p>\n";
                }
                echo "</div>\n";
            } else {
                echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
                echo "<h3>❌ Rollback AJAX Failed</h3>\n";
                echo "<p><strong>Error:</strong> " . htmlspecialchars($response['data'] ?? 'Unknown error') . "</p>\n";
                echo "</div>\n";
            }
        } else {
            echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>\n";
            echo "<h3>⚠️ Invalid JSON Response</h3>\n";
            echo "<p>The response was not valid JSON or had an unexpected format.</p>\n";
            echo "</div>\n";
        }
        
        // Check database state after rollback
        $updated_fix_history = get_option('redco_diagnostic_fix_history', array());
        $final_count = count($updated_fix_history);
        $sessions_removed = $initial_count - $final_count;
        
        echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px; margin-top: 15px;'>\n";
        echo "<h3>📊 Database State After Rollback</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Sessions Before:</strong> {$initial_count}</li>\n";
        echo "<li><strong>Sessions After:</strong> {$final_count}</li>\n";
        echo "<li><strong>Sessions Removed:</strong> {$sessions_removed}</li>\n";
        echo "</ul>\n";
        
        if ($sessions_removed > 0) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; border-radius: 3px;'>\n";
            echo "<p><strong>✅ DATABASE SUCCESS:</strong> {$sessions_removed} session(s) removed from fix history!</p>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; border-radius: 3px;'>\n";
            echo "<p><strong>❌ DATABASE ISSUE:</strong> No sessions were removed from the database.</p>\n";
            echo "</div>\n";
        }
        
        // Check if the specific backup ID still exists
        $still_exists = false;
        foreach ($updated_fix_history as $session) {
            $session_rollback_id = $session['rollback_id'] ?? '';
            $session_backup_id = $session['backup_id'] ?? '';
            
            if ($session_rollback_id === $specific_backup_id || $session_backup_id === $specific_backup_id) {
                $still_exists = true;
                break;
            }
        }
        
        if ($still_exists) {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; border-radius: 3px;'>\n";
            echo "<p><strong>❌ ROLLBACK ISSUE:</strong> The backup ID still exists in the fix history after rollback!</p>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; border-radius: 3px;'>\n";
            echo "<p><strong>✅ ROLLBACK SUCCESS:</strong> The backup ID was successfully removed from the fix history!</p>\n";
            echo "</div>\n";
        }
        
        echo "</div>\n";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>❌ Exception During Test</h3>\n";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
        echo "<p><strong>File:</strong> " . $e->getFile() . "</p>\n";
        echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>\n";
        echo "</div>\n";
    }
    
    echo "<h3>🎯 Next Steps</h3>\n";
    echo "<ol>\n";
    echo "<li>Check the <a href='check-error-logs.php'>error logs</a> for detailed rollback information</li>\n";
    echo "<li>Go to the <a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>Diagnostic & Auto-Fix module</a> to verify the UI</li>\n";
    echo "<li>Run the test again to confirm consistency</li>\n";
    echo "</ol>\n";
    
    echo "<p><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🚀 Go to Diagnostic Module</a></p>\n";
    echo "<p><a href='test-specific-backup-rollback.php' style='background: #666; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔄 Run Another Test</a></p>\n";
    
} else {
    echo "<h2>🧪 Ready to Test Rollback</h2>\n";
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>🎯 Test Instructions</h3>\n";
    echo "<p>Click the button below to execute the rollback test with the specific backup ID:</p>\n";
    echo "<p><a href='?execute_rollback=1' style='background: #dc3232; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🧪 Execute Rollback Test</a></p>\n";
    echo "</div>\n";
}

echo "<h2>🔧 Additional Tools</h2>\n";
echo "<ul>\n";
echo "<li><a href='check-error-logs.php'>📋 Check Error Logs</a></li>\n";
echo "<li><a href='simple-rollback-test.php'>🧪 Simple Rollback Test</a></li>\n";
echo "<li><a href='comprehensive-rollback-test.php'>🔬 Comprehensive Test</a></li>\n";
echo "</ul>\n";
?>
