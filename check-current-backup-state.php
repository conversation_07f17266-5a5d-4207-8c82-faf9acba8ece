<?php
/**
 * Check current backup state and fix any remaining issues
 */

require_once('d:/xampp/htdocs/wordpress/wp-config.php');

echo "=== CHECKING CURRENT BACKUP STATE ===\n";

// Check fix history
$fix_history = get_option('redco_diagnostic_fix_history', array());
echo "Fix history sessions: " . count($fix_history) . "\n";

// Check for problematic backup IDs
$problematic_ids = array();
$valid_ids = array();

foreach ($fix_history as $index => $session) {
    if (isset($session['rollback_id']) && !empty($session['rollback_id'])) {
        $rollback_id = $session['rollback_id'];
        
        if (strpos($rollback_id, '_migrated') !== false || preg_match('/^backup_\d{10}_/', $rollback_id)) {
            $problematic_ids[] = array(
                'index' => $index,
                'id' => $rollback_id,
                'timestamp' => $session['timestamp'] ?? 0
            );
        } else {
            $valid_ids[] = $rollback_id;
        }
    }
}

echo "Valid backup IDs: " . count($valid_ids) . "\n";
echo "Problematic backup IDs: " . count($problematic_ids) . "\n";

if (!empty($problematic_ids)) {
    echo "\nProblematic backup IDs found:\n";
    foreach ($problematic_ids as $problem) {
        echo "  - {$problem['id']} (timestamp: {$problem['timestamp']})\n";
    }
    
    // Get actual backup directories
    $backup_dir = 'D:/xampp/htdocs/wordpress/wp-content/uploads/redco-backups/';
    $actual_backups = glob($backup_dir . 'backup_*');
    $actual_backup_names = array_map('basename', $actual_backups);
    
    echo "\nActual backup directories: " . count($actual_backup_names) . "\n";
    
    // Create timestamp mapping
    $timestamp_to_backup = array();
    foreach ($actual_backup_names as $backup_name) {
        if (preg_match('/backup_(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})_([a-f0-9]+)/', $backup_name, $matches)) {
            $date_time = $matches[1];
            $date_part = substr($date_time, 0, 10);
            $time_part = substr($date_time, 11);
            $date_part = str_replace('-', ':', $date_part);
            $time_part = str_replace('-', ':', $time_part);
            $timestamp = strtotime($date_part . ' ' . $time_part);
            if ($timestamp) {
                $timestamp_to_backup[$timestamp] = $backup_name;
            }
        }
    }
    
    echo "Timestamp mappings created: " . count($timestamp_to_backup) . "\n";
    
    // Fix problematic IDs
    $updated = false;
    foreach ($problematic_ids as $problem) {
        $session_timestamp = $problem['timestamp'];
        $closest_backup = null;
        $min_diff = PHP_INT_MAX;
        
        foreach ($timestamp_to_backup as $backup_timestamp => $backup_name) {
            $diff = abs($backup_timestamp - $session_timestamp);
            if ($diff < $min_diff) {
                $min_diff = $diff;
                $closest_backup = $backup_name;
            }
        }
        
        if ($closest_backup && $min_diff < 3600) {
            echo "Fixing: {$problem['id']} -> $closest_backup (diff: {$min_diff}s)\n";
            $fix_history[$problem['index']]['rollback_id'] = $closest_backup;
            $fix_history[$problem['index']]['original_rollback_id'] = $problem['id'];
            $updated = true;
        }
    }
    
    if ($updated) {
        echo "\nUpdating fix history...\n";
        update_option('redco_diagnostic_fix_history', $fix_history);
        echo "✅ Fix history updated.\n";
        
        // Clear any caches
        wp_cache_flush();
        echo "✅ Caches cleared.\n";
    }
}

// Verify all backup IDs are now valid
echo "\n=== FINAL VALIDATION ===\n";
$updated_history = get_option('redco_diagnostic_fix_history', array());
$backup_dir = 'D:/xampp/htdocs/wordpress/wp-content/uploads/redco-backups/';

$final_valid = 0;
$final_invalid = 0;

foreach ($updated_history as $session) {
    if (isset($session['rollback_id']) && !empty($session['rollback_id'])) {
        $rollback_id = $session['rollback_id'];
        $backup_path = $backup_dir . $rollback_id;
        
        if (is_dir($backup_path) && file_exists($backup_path . '/backup_data.json')) {
            $final_valid++;
        } else {
            $final_invalid++;
            echo "❌ Invalid: $rollback_id\n";
        }
    }
}

echo "Final valid backup IDs: $final_valid\n";
echo "Final invalid backup IDs: $final_invalid\n";

if ($final_invalid === 0) {
    echo "\n✅ ALL BACKUP IDS ARE NOW VALID!\n";
} else {
    echo "\n❌ Some backup IDs are still invalid.\n";
}

// Check for any WordPress transients or caches that might be interfering
echo "\n=== CHECKING FOR CACHED DATA ===\n";

$diagnostic_transients = array(
    'redco_diagnostic_results',
    'redco_diagnostic_stats', 
    'redco_scan_progress',
    'redco_fixed_issues'
);

foreach ($diagnostic_transients as $transient) {
    if (get_transient($transient) !== false) {
        echo "Found cached transient: $transient\n";
        delete_transient($transient);
        echo "Cleared transient: $transient\n";
    }
}

// Check for any options that might contain old backup references
$diagnostic_options = array(
    'redco_fixed_issues',
    'redco_diagnostic_results',
    'redco_diagnostic_stats'
);

foreach ($diagnostic_options as $option) {
    $value = get_option($option);
    if ($value !== false) {
        echo "Found option: $option\n";
        if (is_string($value) && strpos($value, '_migrated') !== false) {
            echo "Option contains migrated references, clearing...\n";
            delete_option($option);
        }
    }
}

echo "\n=== STATE CHECK COMPLETE ===\n";
