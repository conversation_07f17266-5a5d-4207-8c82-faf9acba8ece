<?php
/**
 * Lazy Load Module for Redco Optimizer
 *
 * Implements lazy loading for images to improve page load performance.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Lazy_Load {

    /**
     * Module settings
     */
    private $settings = array();

    /**
     * Constructor
     */
    public function __construct() {
        if (redco_is_module_enabled('lazy-load')) {
            $this->init();
        }
    }

    /**
     * Initialize the module
     */
    private function init() {
        // Load settings
        $this->load_settings();

        // Initialize hooks
        $this->init_hooks();
    }

    /**
     * Load module settings with shared optimization
     */
    private function load_settings() {
        $defaults = Redco_Config::get_module_defaults('lazy-load');

        // Load settings with proper fallback to defaults
        $saved_settings = redco_get_module_option('lazy-load', 'settings', array());

        // Merge with defaults to ensure all keys exist
        $this->settings = wp_parse_args($saved_settings, $defaults);

        // Also load individual settings for backward compatibility
        $this->settings['exclude_featured'] = redco_get_module_option('lazy-load', 'exclude_featured', $this->settings['exclude_featured']);
        $this->settings['exclude_woocommerce'] = redco_get_module_option('lazy-load', 'exclude_woocommerce', $this->settings['exclude_woocommerce']);
        $this->settings['exclude_first_images'] = redco_get_module_option('lazy-load', 'exclude_first_images', $this->settings['exclude_first_images']);
        $this->settings['threshold'] = redco_get_module_option('lazy-load', 'threshold', $this->settings['threshold']);
        $this->settings['placeholder'] = redco_get_module_option('lazy-load', 'placeholder', $this->settings['placeholder']);

        // Use shared resource loading optimizer for optimal thresholds
        if (class_exists('Redco_Resource_Loading_Optimizer')) {
            $optimal_threshold = Redco_Resource_Loading_Optimizer::get_optimal_threshold('lazy_load', 'frontend');

            // Only override if user hasn't customized the setting
            if (!isset($this->settings['threshold_customized']) || !$this->settings['threshold_customized']) {
                $this->settings['threshold'] = $optimal_threshold;
            }
        }
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Don't apply lazy loading in admin or for feeds
        if (is_admin() || is_feed()) {
            return;
        }

        // Add lazy loading to content
        add_filter('the_content', array($this, 'add_lazy_loading'), 999);
        add_filter('post_thumbnail_html', array($this, 'add_lazy_loading_to_thumbnail'), 999, 5);
        add_filter('wp_get_attachment_image', array($this, 'add_lazy_loading'), 999);

        // Enqueue scripts
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));

        // Add inline styles
        add_action('wp_head', array($this, 'add_inline_styles'));
    }

    /**
     * Add lazy loading to content images
     */
    public function add_lazy_loading($content) {
        // Skip if content is empty
        if (empty($content)) {
            return $content;
        }

        // Skip for AMP pages
        if (function_exists('is_amp_endpoint') && is_amp_endpoint()) {
            return $content;
        }

        // Find all img tags
        preg_match_all('/<img[^>]+>/i', $content, $matches);

        if (empty($matches[0])) {
            return $content;
        }

        $images_processed = 0;
        $images_found = 0;

        foreach ($matches[0] as $img_tag) {
            // CRITICAL FIX: Ensure img_tag is valid to prevent null parameter warnings
            if (!$img_tag || !is_string($img_tag) || empty($img_tag)) {
                continue;
            }

            $images_found++;

            // Skip first N images to prevent LCP issues
            $exclude_first_images = isset($this->settings['exclude_first_images']) ? $this->settings['exclude_first_images'] : 2;
            if ($images_found <= $exclude_first_images) {
                continue;
            }

            // Skip if already has loading attribute
            if (strpos($img_tag, 'loading=') !== false) {
                continue;
            }

            // Skip if has data-src (already lazy loaded)
            if (strpos($img_tag, 'data-src=') !== false) {
                continue;
            }

            // Skip if should be excluded
            if ($this->should_exclude_image($img_tag)) {
                continue;
            }

            $new_img_tag = $this->convert_img_to_lazy($img_tag);
            if ($new_img_tag && is_string($new_img_tag)) {
                $content = str_replace($img_tag, $new_img_tag, $content);
                $images_processed++;
            }
        }

        // Update statistics using unified cache system with reduced frequency
        if ($images_processed > 0 && rand(1, 10) === 1) { // Only update stats 10% of the time
            $this->update_stats_cached($images_processed, $images_processed * 102400); // Estimate 100KB saved per image
        }

        return $content;
    }

    /**
     * Add lazy loading to post thumbnails
     */
    public function add_lazy_loading_to_thumbnail($html, $post_id, $post_thumbnail_id, $size, $attr) {
        // Skip if featured images are excluded
        $exclude_featured = isset($this->settings['exclude_featured']) ? $this->settings['exclude_featured'] : false;
        if ($exclude_featured) {
            return $html;
        }

        return $this->add_lazy_loading($html);
    }

    /**
     * Convert img tag to lazy loading
     */
    private function convert_img_to_lazy($img_tag) {
        // Extract src attribute
        preg_match('/src=["\']([^"\']+)["\']/', $img_tag, $src_matches);

        if (empty($src_matches[1])) {
            return $img_tag;
        }

        $src = $src_matches[1];

        // Replace src with data-src and add placeholder
        $placeholder = isset($this->settings['placeholder']) ? $this->settings['placeholder'] : 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1 1"%3E%3C/svg%3E';
        $new_img_tag = str_replace(
            'src="' . $src . '"',
            'src="' . $placeholder . '" data-src="' . $src . '"',
            $img_tag
        );

        // Handle srcset if present
        if (strpos($img_tag, 'srcset=') !== false) {
            preg_match('/srcset=["\']([^"\']+)["\']/', $img_tag, $srcset_matches);
            if (!empty($srcset_matches[1])) {
                $srcset = $srcset_matches[1];
                $new_img_tag = str_replace(
                    'srcset="' . $srcset . '"',
                    'data-srcset="' . $srcset . '"',
                    $new_img_tag
                );
            }
        }

        // Add lazy loading class
        if (strpos($new_img_tag, 'class=') !== false) {
            $new_img_tag = preg_replace('/class=["\']([^"\']*)["\']/', 'class="$1 redco-lazy"', $new_img_tag);
        } else {
            $new_img_tag = str_replace('<img ', '<img class="redco-lazy" ', $new_img_tag);
        }

        // Add loading attribute for native lazy loading fallback
        $new_img_tag = str_replace('<img ', '<img loading="lazy" ', $new_img_tag);

        return $new_img_tag;
    }

    /**
     * Check if image should be excluded from lazy loading
     */
    private function should_exclude_image($img_tag) {
        // Exclude WooCommerce images if setting is enabled
        $exclude_woocommerce = isset($this->settings['exclude_woocommerce']) ? $this->settings['exclude_woocommerce'] : false;
        if ($exclude_woocommerce && redco_is_woocommerce_active()) {
            if (strpos($img_tag, 'woocommerce') !== false ||
                strpos($img_tag, 'product') !== false ||
                strpos($img_tag, 'shop') !== false) {
                return true;
            }
        }

        // Exclude images with specific classes
        $exclude_classes = array(
            'skip-lazy',
            'no-lazy',
            'lazy-ignore'
        );

        foreach ($exclude_classes as $class) {
            if (strpos($img_tag, $class) !== false) {
                return true;
            }
        }

        // Exclude very small images (likely icons or tracking pixels)
        preg_match('/width=["\'](\d+)["\']/', $img_tag, $width_matches);
        preg_match('/height=["\'](\d+)["\']/', $img_tag, $height_matches);

        if (!empty($width_matches[1]) && $width_matches[1] < 50) {
            return true;
        }

        if (!empty($height_matches[1]) && $height_matches[1] < 50) {
            return true;
        }

        return false;
    }

    /**
     * Enqueue lazy loading scripts
     */
    public function enqueue_scripts() {
        // Check if Intersection Observer is supported (modern browsers)
        wp_enqueue_script(
            'redco-lazy-load',
            REDCO_OPTIMIZER_PLUGIN_URL . 'assets/js/lazy-load.js',
            array(),
            REDCO_OPTIMIZER_VERSION,
            true
        );

        // Localize script with settings
        $threshold = isset($this->settings['threshold']) ? $this->settings['threshold'] : 200;
        $placeholder = isset($this->settings['placeholder']) ? $this->settings['placeholder'] : 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1 1"%3E%3C/svg%3E';

        wp_localize_script('redco-lazy-load', 'redcoLazyLoad', array(
            'threshold' => $threshold,
            'placeholder' => $placeholder
        ));
    }

    /**
     * Add inline styles for lazy loading
     */
    public function add_inline_styles() {
        ?>
        <style>
        .redco-lazy {
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .redco-lazy.loaded {
            opacity: 1;
        }

        .redco-lazy-loading {
            background: #f0f0f0 url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40"><circle cx="20" cy="20" r="18" fill="none" stroke="%23ddd" stroke-width="2"/><circle cx="20" cy="20" r="18" fill="none" stroke="%23007cba" stroke-width="2" stroke-linecap="round" stroke-dasharray="28" stroke-dashoffset="28"><animate attributeName="stroke-dashoffset" dur="1s" values="28;0;28" repeatCount="indefinite"/></circle></svg>') center center no-repeat;
            background-size: 40px 40px;
        }

        /* Prevent layout shift */
        .redco-lazy[data-src] {
            background: #f8f9fa;
        }
        </style>
        <?php
    }

    /**
     * Get comprehensive lazy loading statistics
     */
    public function get_stats() {
        $stats = get_option('redco_lazy_load_stats', array(
            'images_processed' => 0,
            'bytes_saved' => 0,
            'pages_optimized' => 0,
            'last_processed' => 0,
            'average_images_per_page' => 0,
            'total_page_views' => 0
        ));

        // Calculate additional metrics
        $average_images = $stats['pages_optimized'] > 0 ?
            round($stats['images_processed'] / $stats['pages_optimized'], 1) : 0;

        // Estimate bandwidth saved (average image size ~100KB)
        $estimated_bandwidth_saved = $stats['images_processed'] * 102400; // 100KB per image

        return array(
            'images_processed' => $stats['images_processed'],
            'bytes_saved' => max($stats['bytes_saved'], $estimated_bandwidth_saved),
            'bytes_saved_formatted' => redco_format_bytes(max($stats['bytes_saved'], $estimated_bandwidth_saved)),
            'pages_optimized' => $stats['pages_optimized'],
            'average_images_per_page' => $average_images,
            'last_processed' => $stats['last_processed'],
            'enabled' => redco_is_module_enabled('lazy-load'),
            'exclude_first_images' => isset($this->settings['exclude_first_images']) ? $this->settings['exclude_first_images'] : 2,
            'threshold' => isset($this->settings['threshold']) ? $this->settings['threshold'] : 200,
            'performance_impact' => $this->calculate_performance_impact($stats),
            'settings_optimized' => $this->check_settings_optimization()
        );
    }

    /**
     * Calculate performance impact
     */
    private function calculate_performance_impact($stats) {
        if ($stats['images_processed'] === 0) {
            return array(
                'status' => 'no_data',
                'message' => 'No images processed yet',
                'estimated_load_time_saved' => 0
            );
        }

        // Estimate load time saved (average image load time ~200ms)
        $time_saved_per_image = 200; // milliseconds
        $total_time_saved = $stats['images_processed'] * $time_saved_per_image;

        $status = 'good';
        if ($stats['images_processed'] < 10) {
            $status = 'fair';
        }

        return array(
            'status' => $status,
            'message' => "Lazy loading is working well with {$stats['images_processed']} images optimized",
            'estimated_load_time_saved' => $total_time_saved,
            'load_time_saved_formatted' => $this->format_time_saved($total_time_saved),
            'bandwidth_saved_per_page' => $stats['pages_optimized'] > 0 ?
                round($stats['bytes_saved'] / $stats['pages_optimized']) : 0
        );
    }

    /**
     * Check if settings are optimized
     */
    private function check_settings_optimization() {
        $recommendations = array();

        $exclude_first_images = isset($this->settings['exclude_first_images']) ? $this->settings['exclude_first_images'] : 2;
        $threshold = isset($this->settings['threshold']) ? $this->settings['threshold'] : 200;

        if ($exclude_first_images < 2) {
            $recommendations[] = 'Consider excluding first 2 images to improve LCP';
        }

        if ($threshold > 300) {
            $recommendations[] = 'Consider reducing threshold to 200px for better performance';
        }

        return array(
            'optimized' => empty($recommendations),
            'recommendations' => $recommendations
        );
    }

    /**
     * Format time saved for display
     */
    private function format_time_saved($milliseconds) {
        if ($milliseconds < 1000) {
            return $milliseconds . 'ms';
        } elseif ($milliseconds < 60000) {
            return round($milliseconds / 1000, 1) . 's';
        } else {
            return round($milliseconds / 60000, 1) . 'm';
        }
    }

    /**
     * Update statistics with enhanced tracking
     */
    public function update_stats($images_count, $bytes_saved = 0) {
        $current_stats = get_option('redco_lazy_load_stats', array(
            'images_processed' => 0,
            'bytes_saved' => 0,
            'pages_optimized' => 0,
            'last_processed' => 0,
            'average_images_per_page' => 0,
            'total_page_views' => 0
        ));

        // Use cached update system to reduce database writes
        $this->update_stats_cached($images_count, $bytes_saved);
    }

    /**
     * Update statistics using unified cache system with batching
     */
    private function update_stats_cached($images_count, $bytes_saved = 0) {
        $cache_key = 'lazy_load_stats_buffer';

        // Get buffered stats from cache
        $buffered_stats = Redco_Advanced_Cache::get($cache_key, Redco_Advanced_Cache::GROUP_STATS);
        if ($buffered_stats === false) {
            $buffered_stats = array(
                'images_processed' => 0,
                'bytes_saved' => 0,
                'pages_optimized' => 0
            );
        }

        // Add to buffer
        $buffered_stats['images_processed'] += $images_count;
        $buffered_stats['bytes_saved'] += $bytes_saved;
        $buffered_stats['pages_optimized']++;

        // Store back in cache for 5 minutes
        Redco_Advanced_Cache::set($cache_key, $buffered_stats, 300, Redco_Advanced_Cache::GROUP_STATS);

        // Flush to database every 20 page views or randomly (5% chance)
        if ($buffered_stats['pages_optimized'] >= 20 || rand(1, 20) === 1) {
            $this->flush_stats_buffer($buffered_stats);
            Redco_Advanced_Cache::delete($cache_key, Redco_Advanced_Cache::GROUP_STATS);
        }
    }

    /**
     * Flush buffered statistics to database
     */
    private function flush_stats_buffer($buffered_stats) {
        $current_stats = get_option('redco_lazy_load_stats', array(
            'images_processed' => 0,
            'bytes_saved' => 0,
            'pages_optimized' => 0,
            'last_processed' => 0,
            'average_images_per_page' => 0,
            'total_page_views' => 0
        ));

        // Update counters
        $current_stats['images_processed'] += $buffered_stats['images_processed'];
        $current_stats['bytes_saved'] += $buffered_stats['bytes_saved'];
        $current_stats['pages_optimized'] += $buffered_stats['pages_optimized'];
        $current_stats['last_processed'] = time();

        // Calculate average
        if ($current_stats['pages_optimized'] > 0) {
            $current_stats['average_images_per_page'] =
                round($current_stats['images_processed'] / $current_stats['pages_optimized'], 1);
        }

        update_option('redco_lazy_load_stats', $current_stats);
    }
}

// Initialize the module only if enabled and after init hook
function redco_init_lazy_load() {
    if (redco_is_module_enabled('lazy-load')) {
        new Redco_Lazy_Load();
    }
}
add_action('init', 'redco_init_lazy_load', 10);
