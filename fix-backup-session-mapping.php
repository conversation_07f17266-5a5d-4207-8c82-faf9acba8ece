<?php
/**
 * Fix backup session mapping between fix history and actual backup directories
 * This resolves the issue where session records have timestamp-based backup IDs
 * but actual backups have date-time-based directory names
 */

require_once('d:/xampp/htdocs/wordpress/wp-config.php');

echo "=== FIXING BACKUP SESSION MAPPING ===\n";

// UNIFIED: Use centralized backup directory function
require_once('includes/helpers.php');
$backup_dir = redco_get_unified_backup_dir();

// Get fix history
$fix_history = get_option('redco_diagnostic_fix_history', array());
echo "Found " . count($fix_history) . " fix history sessions.\n";

// Get actual backup directories
$actual_backups = glob($backup_dir . 'backup_*');
$actual_backup_names = array_map('basename', $actual_backups);
echo "Found " . count($actual_backup_names) . " actual backup directories.\n";

// Create timestamp to backup directory mapping
$timestamp_to_backup = array();
foreach ($actual_backup_names as $backup_name) {
    // Extract timestamp from backup name (format: backup_2025-06-06_03-36-54_684262568b18f)
    if (preg_match('/backup_(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})_([a-f0-9]+)/', $backup_name, $matches)) {
        $date_time = $matches[1];
        $unique_id = $matches[2];
        
        // Convert to timestamp for comparison
        // Format: 2025-06-06_03-36-54 -> 2025:06:06 03:36:54
        $date_part = substr($date_time, 0, 10); // 2025-06-06
        $time_part = substr($date_time, 11);    // 03-36-54
        $date_part = str_replace('-', ':', $date_part);
        $time_part = str_replace('-', ':', $time_part);
        $timestamp = strtotime($date_part . ' ' . $time_part);
        if ($timestamp) {
            $timestamp_to_backup[$timestamp] = $backup_name;
        }
    }
}

echo "Created timestamp mapping for " . count($timestamp_to_backup) . " backups.\n";

$updated_sessions = 0;
$problematic_sessions = array();

// Process each fix history session
foreach ($fix_history as $index => &$session) {
    if (!isset($session['rollback_id']) || empty($session['rollback_id'])) {
        continue;
    }
    
    $rollback_id = $session['rollback_id'];
    
    // Check if this is a problematic backup ID (timestamp-based or migrated)
    if (strpos($rollback_id, '_migrated') !== false || preg_match('/^backup_\d{10}_/', $rollback_id)) {
        echo "\nProcessing problematic session: $rollback_id\n";
        
        // Extract timestamp from the backup ID
        $session_timestamp = $session['timestamp'] ?? 0;
        
        if ($session_timestamp > 0) {
            // Find the closest actual backup by timestamp
            $closest_backup = null;
            $min_diff = PHP_INT_MAX;
            
            foreach ($timestamp_to_backup as $backup_timestamp => $backup_name) {
                $diff = abs($backup_timestamp - $session_timestamp);
                if ($diff < $min_diff) {
                    $min_diff = $diff;
                    $closest_backup = $backup_name;
                }
            }
            
            if ($closest_backup && $min_diff < 3600) { // Within 1 hour
                echo "  📍 Mapping to closest backup: $closest_backup (diff: {$min_diff}s)\n";
                $session['rollback_id'] = $closest_backup;
                $session['original_rollback_id'] = $rollback_id; // Keep original for reference
                $updated_sessions++;
            } else {
                echo "  ❌ No suitable backup found for session timestamp: $session_timestamp\n";
                $problematic_sessions[] = array(
                    'index' => $index,
                    'rollback_id' => $rollback_id,
                    'timestamp' => $session_timestamp,
                    'reason' => 'No matching backup found'
                );
            }
        } else {
            echo "  ❌ Session has no valid timestamp\n";
            $problematic_sessions[] = array(
                'index' => $index,
                'rollback_id' => $rollback_id,
                'timestamp' => $session_timestamp,
                'reason' => 'No valid timestamp'
            );
        }
    }
}

// Update the fix history
if ($updated_sessions > 0) {
    echo "\nUpdating fix history with $updated_sessions corrected sessions...\n";
    update_option('redco_diagnostic_fix_history', $fix_history);
    echo "✅ Fix history updated successfully.\n";
} else {
    echo "\nNo sessions needed updating.\n";
}

// Report problematic sessions
if (!empty($problematic_sessions)) {
    echo "\n⚠️  Problematic sessions that couldn't be fixed:\n";
    foreach ($problematic_sessions as $problem) {
        echo "  - Index {$problem['index']}: {$problem['rollback_id']} ({$problem['reason']})\n";
    }
    
    // Option to remove problematic sessions
    echo "\nWould you like to remove these problematic sessions? (They can't be rolled back anyway)\n";
    echo "This will clean up the fix history and prevent rollback errors.\n";
    
    // For automated fixing, remove them
    $cleaned_history = array();
    foreach ($fix_history as $index => $session) {
        $is_problematic = false;
        foreach ($problematic_sessions as $problem) {
            if ($problem['index'] === $index) {
                $is_problematic = true;
                break;
            }
        }
        
        if (!$is_problematic) {
            $cleaned_history[] = $session;
        }
    }
    
    if (count($cleaned_history) < count($fix_history)) {
        $removed_count = count($fix_history) - count($cleaned_history);
        echo "Removing $removed_count problematic sessions...\n";
        update_option('redco_diagnostic_fix_history', $cleaned_history);
        echo "✅ Problematic sessions removed from fix history.\n";
    }
}

echo "\n=== MAPPING SUMMARY ===\n";
echo "Total sessions processed: " . count($fix_history) . "\n";
echo "Sessions updated: $updated_sessions\n";
echo "Problematic sessions removed: " . count($problematic_sessions) . "\n";

// Test the fix
echo "\n=== TESTING ROLLBACK VALIDATION ===\n";
$updated_history = get_option('redco_diagnostic_fix_history', array());
$valid_rollbacks = 0;
$invalid_rollbacks = 0;

foreach ($updated_history as $session) {
    if (isset($session['rollback_id']) && !empty($session['rollback_id'])) {
        $rollback_id = $session['rollback_id'];
        $backup_path = $backup_dir . $rollback_id;
        
        if (is_dir($backup_path) && file_exists($backup_path . '/backup_data.json')) {
            $valid_rollbacks++;
        } else {
            $invalid_rollbacks++;
            echo "  ❌ Invalid rollback ID: $rollback_id\n";
        }
    }
}

echo "Valid rollback IDs: $valid_rollbacks\n";
echo "Invalid rollback IDs: $invalid_rollbacks\n";

if ($invalid_rollbacks === 0) {
    echo "\n✅ All rollback IDs are now valid!\n";
    echo "Rollback operations should work correctly.\n";
} else {
    echo "\n⚠️  Some rollback IDs are still invalid.\n";
    echo "These sessions may need manual cleanup.\n";
}

echo "\n=== SESSION MAPPING FIX COMPLETE ===\n";
