<?php
/**
 * Simple Rollback Test - Isolate the JSON issue
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// Security check
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

echo "<h1>🔍 Simple Rollback Test - JSON Issue Debug</h1>\n";

// Get current fix history
$fix_history = get_option('redco_diagnostic_fix_history', array());

if (empty($fix_history)) {
    // Create a simple test session
    $test_session = array(
        'session_id' => 'simple_test_' . time(),
        'timestamp' => time(),
        'rollback_id' => 'simple_rollback_' . time(),
        'backup_id' => 'simple_backup_' . time(),
        'message' => 'Simple test session',
        'fixes_applied' => 1,
        'backup_created' => true,
        'details' => array(
            array(
                'issue_id' => 'simple_test_issue',
                'success' => true
            )
        )
    );
    
    $fix_history[] = $test_session;
    update_option('redco_diagnostic_fix_history', $fix_history);
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
    echo "<p>✅ Created test session with rollback ID: <strong>" . $test_session['rollback_id'] . "</strong></p>\n";
    echo "</div>\n";
}

// Use the specific backup ID provided by the user
$test_rollback_id = 'backup_2025-06-06_07-41-34_68429bae30c91';

echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
echo "<p>🎯 Using specific backup ID: <strong>{$test_rollback_id}</strong></p>\n";
echo "</div>\n";

echo "<h2>🧪 Testing Rollback ID: {$test_rollback_id}</h2>\n";

// Check if this backup ID exists in the current fix history
$backup_found = false;
$matching_session = null;

foreach ($fix_history as $session) {
    $session_rollback_id = $session['rollback_id'] ?? '';
    $session_backup_id = $session['backup_id'] ?? '';

    if ($session_rollback_id === $test_rollback_id || $session_backup_id === $test_rollback_id) {
        $backup_found = true;
        $matching_session = $session;
        break;
    }
}

if ($backup_found) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>✅ Backup ID Found in Fix History</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>Session Timestamp:</strong> " . date('Y-m-d H:i:s', $matching_session['timestamp'] ?? time()) . "</li>\n";
    echo "<li><strong>Message:</strong> " . ($matching_session['message'] ?? 'No message') . "</li>\n";
    echo "<li><strong>Fixes Applied:</strong> " . ($matching_session['fixes_applied'] ?? 0) . "</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>⚠️ Backup ID Not Found in Current Fix History</h3>\n";
    echo "<p>The backup ID <strong>{$test_rollback_id}</strong> was not found in the current fix history.</p>\n";
    echo "<p>This test will still proceed to see how the rollback system handles non-existent backup IDs.</p>\n";
    echo "</div>\n";
}

// Test the rollback AJAX endpoint directly
if (isset($_GET['test_rollback'])) {
    echo "<h3>📡 Testing AJAX Rollback Endpoint</h3>\n";
    
    try {
        // Enable error reporting to catch any PHP errors
        error_reporting(E_ALL);
        ini_set('display_errors', 1);
        
        // Load the diagnostic class
        require_once('modules/diagnostic-autofix/class-diagnostic-autofix.php');
        
        if (!class_exists('Redco_Diagnostic_AutoFix')) {
            throw new Exception('Redco_Diagnostic_AutoFix class not found');
        }
        
        $diagnostic = new Redco_Diagnostic_AutoFix();
        
        // Set up the POST data exactly as the frontend would
        $_POST = array(
            'action' => 'redco_rollback_fixes',
            'backup_id' => $test_rollback_id,
            'nonce' => wp_create_nonce('redco_diagnostic_nonce')
        );
        
        echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
        echo "<h4>📋 Request Details</h4>\n";
        echo "<ul>\n";
        echo "<li><strong>Action:</strong> redco_rollback_fixes</li>\n";
        echo "<li><strong>Backup ID:</strong> {$test_rollback_id}</li>\n";
        echo "<li><strong>Nonce:</strong> " . $_POST['nonce'] . "</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        // Capture ALL output (including any errors)
        ob_start();
        
        // Call the rollback method
        if (method_exists($diagnostic, 'ajax_rollback_fixes')) {
            $diagnostic->ajax_rollback_fixes();
        } else {
            throw new Exception('ajax_rollback_fixes method not found');
        }
        
        $output = ob_get_clean();
        
        echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px;'>\n";
        echo "<h4>📤 Raw Output</h4>\n";
        echo "<p><strong>Length:</strong> " . strlen($output) . " characters</p>\n";
        echo "<pre style='background: #ffffff; padding: 10px; border: 1px solid #ccc; border-radius: 3px; max-height: 300px; overflow-y: auto; font-size: 12px;'>" . htmlspecialchars($output) . "</pre>\n";
        echo "</div>\n";
        
        // Try to parse as JSON
        $json_data = json_decode($output, true);
        $json_error = json_last_error();
        
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-top: 10px;'>\n";
        echo "<h4>🔍 JSON Analysis</h4>\n";
        
        if ($json_error === JSON_ERROR_NONE && $json_data !== null) {
            echo "<p><strong>Status:</strong> <span style='color: #28a745; font-weight: bold;'>Valid JSON</span></p>\n";
            echo "<p><strong>Success:</strong> " . ($json_data['success'] ? 'true' : 'false') . "</p>\n";
            
            if ($json_data['success']) {
                echo "<p><strong>Message:</strong> " . htmlspecialchars($json_data['data']['message'] ?? 'No message') . "</p>\n";
                
                // Check if database was actually updated
                $updated_history = get_option('redco_diagnostic_fix_history', array());
                $sessions_removed = count($fix_history) - count($updated_history);
                
                echo "<p><strong>Database Update:</strong> ";
                if ($sessions_removed > 0) {
                    echo "<span style='color: #28a745; font-weight: bold;'>SUCCESS ({$sessions_removed} sessions removed)</span>";
                } else {
                    echo "<span style='color: #dc3232; font-weight: bold;'>FAILED (no sessions removed)</span>";
                }
                echo "</p>\n";
                
            } else {
                echo "<p><strong>Error:</strong> " . htmlspecialchars($json_data['data'] ?? 'Unknown error') . "</p>\n";
            }
        } else {
            echo "<p><strong>Status:</strong> <span style='color: #dc3232; font-weight: bold;'>Invalid JSON</span></p>\n";
            echo "<p><strong>JSON Error:</strong> " . json_last_error_msg() . "</p>\n";
            
            // Check if output starts with HTML or PHP error
            $first_100_chars = substr(trim($output), 0, 100);
            echo "<p><strong>First 100 characters:</strong> <code>" . htmlspecialchars($first_100_chars) . "</code></p>\n";
            
            if (strpos($output, '<') === 0) {
                echo "<p><strong>Issue:</strong> Output appears to be HTML, not JSON</p>\n";
            } elseif (strpos($output, 'Fatal error') !== false || strpos($output, 'Parse error') !== false) {
                echo "<p><strong>Issue:</strong> PHP error detected in output</p>\n";
            } elseif (empty(trim($output))) {
                echo "<p><strong>Issue:</strong> No output generated (possible silent failure)</p>\n";
            }
        }
        echo "</div>\n";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<h4>❌ Exception Caught</h4>\n";
        echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
        echo "<p><strong>File:</strong> " . $e->getFile() . "</p>\n";
        echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>\n";
        echo "</div>\n";
    }
    
    echo "<h3>🎯 Next Steps</h3>\n";
    echo "<ol>\n";
    echo "<li>If JSON is invalid, check for PHP errors in the rollback method</li>\n";
    echo "<li>If JSON is valid but database isn't updated, check the session matching logic</li>\n";
    echo "<li>Check the <a href='check-error-logs.php'>error logs</a> for detailed information</li>\n";
    echo "</ol>\n";
    
} else {
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>🎯 Ready to Test</h3>\n";
    echo "<p>Click the button below to test the rollback AJAX endpoint:</p>\n";
    echo "<p><a href='?test_rollback=1' style='background: #dc3232; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🧪 Test Rollback AJAX</a></p>\n";
    echo "</div>\n";
}

echo "<h2>🔧 Additional Tools</h2>\n";
echo "<ul>\n";
echo "<li><a href='check-error-logs.php'>📋 Check Error Logs</a></li>\n";
echo "<li><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>🚀 Go to Diagnostic Module</a></li>\n";
echo "</ul>\n";
?>
