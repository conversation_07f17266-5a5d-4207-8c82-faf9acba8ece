<?php
/**
 * Module Consolidation Cleanup
 * 
 * Handles cleanup of old module data when consolidating modules
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Module_Consolidation_Cleanup {

    /**
     * Old modules that were consolidated into wordpress-core-tweaks
     */
    private $old_modules = array(
        'emoji-stripper',
        'query-string-remover',
        'asset-version-remover',
        'autosave-reducer'
    );

    /**
     * Old CSS/JS modules that were consolidated into asset-optimization
     */
    private $old_asset_modules = array(
        'css-js-minifier',
        'critical-resource-optimizer'
    );

    /**
     * Constructor
     */
    public function __construct() {
        // Run cleanup on admin init
        add_action('admin_init', array($this, 'maybe_run_cleanup'), 5);
    }

    /**
     * Check if cleanup should run
     */
    public function maybe_run_cleanup() {
        // Only run once
        if (get_option('redco_module_consolidation_cleanup_done', false)) {
            return;
        }

        // Check if any old modules are still enabled
        $this->cleanup_old_module_data();
        $this->cleanup_old_asset_modules();

        // Mark cleanup as done
        update_option('redco_module_consolidation_cleanup_done', true);
    }

    /**
     * Clean up old module data
     */
    private function cleanup_old_module_data() {
        // Get current options
        $options = get_option('redco_optimizer_options', array());
        $modules_enabled = isset($options['modules_enabled']) ? $options['modules_enabled'] : array();

        // Check if any old modules are enabled and migrate their settings
        $old_modules_were_enabled = false;
        foreach ($this->old_modules as $old_module) {
            if (in_array($old_module, $modules_enabled)) {
                $old_modules_were_enabled = true;
                $this->migrate_module_settings($old_module);
                
                // Remove from enabled modules
                $modules_enabled = array_diff($modules_enabled, array($old_module));
            }
        }

        // If any old modules were enabled, enable the new unified module
        if ($old_modules_were_enabled && !in_array('wordpress-core-tweaks', $modules_enabled)) {
            $modules_enabled[] = 'wordpress-core-tweaks';
        }

        // Update enabled modules list
        $options['modules_enabled'] = $modules_enabled;
        update_option('redco_optimizer_options', $options);

        // Clean up old module option entries
        $this->cleanup_old_module_options();
    }

    /**
     * Migrate settings from old modules to new unified module
     */
    private function migrate_module_settings($old_module) {
        $old_settings = get_option('redco_optimizer_' . str_replace('-', '_', $old_module), array());
        
        if (empty($old_settings)) {
            return;
        }

        switch ($old_module) {
            case 'emoji-stripper':
                $this->migrate_emoji_settings($old_settings);
                break;
                
            case 'query-string-remover':
            case 'asset-version-remover':
                $this->migrate_version_removal_settings($old_settings, $old_module);
                break;
                
            case 'autosave-reducer':
                $this->migrate_autosave_settings($old_settings);
                break;
        }
    }

    /**
     * Migrate emoji stripper settings
     */
    private function migrate_emoji_settings($old_settings) {
        $mapping = array(
            'remove_frontend' => 'emoji_remove_frontend',
            'remove_admin' => 'emoji_remove_admin',
            'remove_feeds' => 'emoji_remove_feeds',
            'remove_emails' => 'emoji_remove_emails'
        );

        foreach ($mapping as $old_key => $new_key) {
            if (isset($old_settings[$old_key])) {
                redco_update_module_option('wordpress-core-tweaks', $new_key, $old_settings[$old_key]);
            }
        }
    }

    /**
     * Migrate version removal settings
     */
    private function migrate_version_removal_settings($old_settings, $module_type) {
        if ($module_type === 'query-string-remover') {
            $mapping = array(
                'remove_css_versions' => 'remove_css_versions',
                'remove_js_versions' => 'remove_js_versions',
                'exclude_handles' => 'exclude_handles'
            );
        } else { // asset-version-remover
            $mapping = array(
                'remove_css_versions' => 'remove_css_versions',
                'remove_js_versions' => 'remove_js_versions',
                'remove_theme_versions' => 'remove_theme_versions',
                'remove_plugin_versions' => 'remove_plugin_versions',
                'remove_wp_version' => 'remove_wp_version',
                'exclude_handles' => 'exclude_handles'
            );
        }

        foreach ($mapping as $old_key => $new_key) {
            if (isset($old_settings[$old_key])) {
                redco_update_module_option('wordpress-core-tweaks', $new_key, $old_settings[$old_key]);
            }
        }
    }

    /**
     * Migrate autosave reducer settings
     */
    private function migrate_autosave_settings($old_settings) {
        $mapping = array(
            'autosave_interval' => 'autosave_interval',
            'disable_autosave' => 'disable_autosave',
            'autosave_post_types' => 'autosave_post_types',
            'limit_revisions' => 'limit_revisions',
            'max_revisions' => 'max_revisions'
        );

        foreach ($mapping as $old_key => $new_key) {
            if (isset($old_settings[$old_key])) {
                redco_update_module_option('wordpress-core-tweaks', $new_key, $old_settings[$old_key]);
            }
        }
    }

    /**
     * Clean up old asset modules
     */
    private function cleanup_old_asset_modules() {
        $options = get_option('redco_optimizer_options', array());
        $modules_enabled = isset($options['modules_enabled']) ? $options['modules_enabled'] : array();

        $old_asset_modules_were_enabled = false;
        foreach ($this->old_asset_modules as $old_module) {
            if (in_array($old_module, $modules_enabled)) {
                $old_asset_modules_were_enabled = true;

                // Remove from enabled modules
                $modules_enabled = array_diff($modules_enabled, array($old_module));
            }
        }

        // If old asset modules were enabled, enable asset-optimization
        if ($old_asset_modules_were_enabled && !in_array('asset-optimization', $modules_enabled)) {
            $modules_enabled[] = 'asset-optimization';
        }

        // Update options
        if ($old_asset_modules_were_enabled) {
            $options['modules_enabled'] = $modules_enabled;
            update_option('redco_optimizer_options', $options);
        }

        // Clean up old asset module options
        foreach ($this->old_asset_modules as $old_module) {
            $option_name = 'redco_optimizer_' . str_replace('-', '_', $old_module);
            delete_option($option_name);
        }
    }

    /**
     * Clean up old module database options
     */
    private function cleanup_old_module_options() {
        foreach ($this->old_modules as $old_module) {
            $option_name = 'redco_optimizer_' . str_replace('-', '_', $old_module);
            delete_option($option_name);
        }
    }

    /**
     * Manual cleanup method for admin use
     */
    public function force_cleanup() {
        delete_option('redco_module_consolidation_cleanup_done');
        $this->cleanup_old_module_data();
        update_option('redco_module_consolidation_cleanup_done', true);
        
        return array(
            'success' => true,
            'message' => 'Module consolidation cleanup completed successfully.'
        );
    }
}

// Initialize cleanup
new Redco_Module_Consolidation_Cleanup();
