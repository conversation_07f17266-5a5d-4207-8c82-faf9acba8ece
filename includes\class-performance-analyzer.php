<?php
/**
 * Performance Analyzer for Redco Optimizer
 *
 * Analyzes and diagnoses performance issues caused by the plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Optimizer_Performance_Analyzer {

    /**
     * Performance metrics
     */
    private $metrics = array();

    /**
     * Initialize analyzer
     */
    public function init() {
        add_action('wp_ajax_redco_analyze_performance', array($this, 'ajax_analyze_performance'));
        add_action('wp_ajax_nopriv_redco_analyze_performance', array($this, 'ajax_analyze_performance'));

        // Hook early to measure plugin impact
        add_action('init', array($this, 'start_performance_measurement'), 1);
        add_action('wp_footer', array($this, 'end_performance_measurement'), 999);
    }

    /**
     * Start performance measurement
     */
    public function start_performance_measurement() {
        if (!is_admin() && !wp_doing_ajax()) {
            $this->metrics['start_time'] = microtime(true);
            $this->metrics['start_memory'] = memory_get_usage(true);
            $this->metrics['start_queries'] = get_num_queries();
        }
    }

    /**
     * End performance measurement
     */
    public function end_performance_measurement() {
        if (!is_admin() && !wp_doing_ajax() && isset($this->metrics['start_time'])) {
            $this->metrics['end_time'] = microtime(true);
            $this->metrics['end_memory'] = memory_get_usage(true);
            $this->metrics['end_queries'] = get_num_queries();

            $this->metrics['execution_time'] = $this->metrics['end_time'] - $this->metrics['start_time'];
            $this->metrics['memory_usage'] = $this->metrics['end_memory'] - $this->metrics['start_memory'];
            $this->metrics['query_count'] = $this->metrics['end_queries'] - $this->metrics['start_queries'];

            // Store metrics for analysis
            $this->store_metrics();
        }
    }

    /**
     * AJAX handler for performance analysis
     */
    public function ajax_analyze_performance() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_die('Security check failed');
        }

        $analysis = $this->run_comprehensive_analysis();

        wp_send_json_success(array(
            'analysis' => $analysis,
            'recommendations' => $this->get_performance_recommendations($analysis)
        ));
    }

    /**
     * Run comprehensive performance analysis
     */
    public function run_comprehensive_analysis() {
        $analysis = array();

        // 1. Frontend Asset Analysis
        $analysis['frontend_assets'] = $this->analyze_frontend_assets();

        // 2. Database Query Analysis
        $analysis['database_queries'] = $this->analyze_database_queries();

        // 3. Module Impact Analysis
        $analysis['module_impact'] = $this->analyze_module_impact();

        // 4. Resource Loading Analysis
        $analysis['resource_loading'] = $this->analyze_resource_loading();

        // 5. Cache Effectiveness Analysis
        $analysis['cache_effectiveness'] = $this->analyze_cache_effectiveness();

        // 6. External Resource Analysis
        $analysis['external_resources'] = $this->analyze_external_resources();

        // 7. JavaScript Performance Analysis
        $analysis['javascript_performance'] = $this->analyze_javascript_performance();

        return $analysis;
    }

    /**
     * Analyze frontend assets
     */
    private function analyze_frontend_assets() {
        global $wp_scripts, $wp_styles;

        $issues = array();
        $assets = array('scripts' => array(), 'styles' => array());

        // Check if admin assets are loading on frontend
        if (!is_admin()) {
            // Check scripts
            if (isset($wp_scripts->queue)) {
                foreach ($wp_scripts->queue as $handle) {
                    if (isset($wp_scripts->registered[$handle])) {
                        $script = $wp_scripts->registered[$handle];
                        if (strpos($script->src, 'redco-optimizer') !== false) {
                            $assets['scripts'][] = array(
                                'handle' => $handle,
                                'src' => $script->src,
                                'size' => $this->get_file_size_from_url($script->src),
                                'is_admin' => strpos($handle, 'admin') !== false || strpos($script->src, 'admin') !== false
                            );

                            if (strpos($handle, 'admin') !== false || strpos($script->src, 'admin') !== false) {
                                $issues[] = "Admin script '{$handle}' is loading on frontend";
                            }
                        }
                    }
                }
            }

            // Check styles
            if (isset($wp_styles->queue)) {
                foreach ($wp_styles->queue as $handle) {
                    if (isset($wp_styles->registered[$handle])) {
                        $style = $wp_styles->registered[$handle];
                        if (strpos($style->src, 'redco-optimizer') !== false) {
                            $assets['styles'][] = array(
                                'handle' => $handle,
                                'src' => $style->src,
                                'size' => $this->get_file_size_from_url($style->src),
                                'is_admin' => strpos($handle, 'admin') !== false || strpos($style->src, 'admin') !== false
                            );

                            if (strpos($handle, 'admin') !== false || strpos($style->src, 'admin') !== false) {
                                $issues[] = "Admin style '{$handle}' is loading on frontend";
                            }
                        }
                    }
                }
            }
        }

        return array(
            'assets' => $assets,
            'issues' => $issues,
            'total_scripts' => count($assets['scripts']),
            'total_styles' => count($assets['styles'])
        );
    }

    /**
     * Analyze database queries
     */
    private function analyze_database_queries() {
        global $wpdb;

        $issues = array();
        $query_analysis = array();

        // Get recent metrics
        $recent_metrics = get_option('redco_performance_metrics', array());

        if (!empty($recent_metrics)) {
            $avg_queries = array_sum(array_column($recent_metrics, 'query_count')) / count($recent_metrics);
            $avg_time = array_sum(array_column($recent_metrics, 'execution_time')) / count($recent_metrics);

            $query_analysis['average_queries'] = $avg_queries;
            $query_analysis['average_execution_time'] = $avg_time;

            if ($avg_queries > 50) {
                $issues[] = "High number of database queries detected (avg: {$avg_queries})";
            }

            if ($avg_time > 2.0) {
                $issues[] = "Slow page execution time detected (avg: {$avg_time}s)";
            }
        }

        // Check for slow queries if query log is available
        if (defined('SAVEQUERIES') && SAVEQUERIES && isset($wpdb->queries)) {
            $slow_queries = array();
            foreach ($wpdb->queries as $query) {
                if ($query[1] > 0.1) { // Queries taking more than 100ms
                    $slow_queries[] = array(
                        'query' => substr($query[0], 0, 100) . '...',
                        'time' => $query[1],
                        'stack' => $query[2]
                    );
                }
            }

            if (!empty($slow_queries)) {
                $query_analysis['slow_queries'] = $slow_queries;
                $issues[] = count($slow_queries) . " slow queries detected";
            }
        }

        return array(
            'analysis' => $query_analysis,
            'issues' => $issues
        );
    }

    /**
     * Analyze module impact
     */
    private function analyze_module_impact() {
        $enabled_modules = get_option('redco_optimizer_options', array())['modules_enabled'] ?? array();
        $issues = array();
        $module_analysis = array();

        foreach ($enabled_modules as $module) {
            $module_analysis[$module] = array(
                'enabled' => true,
                'potential_issues' => array()
            );

            // Check for specific module issues
            switch ($module) {
                case 'css-js-minifier':
                    if ($this->is_minification_causing_errors()) {
                        $issues[] = "CSS/JS minification may be causing JavaScript errors";
                        $module_analysis[$module]['potential_issues'][] = 'JavaScript errors detected';
                    }
                    break;

                case 'lazy-load':
                    if ($this->is_lazy_load_affecting_lcp()) {
                        $issues[] = "Lazy loading may be affecting Largest Contentful Paint (LCP)";
                        $module_analysis[$module]['potential_issues'][] = 'LCP impact detected';
                    }
                    break;

                case 'page-cache':
                    if (!$this->is_cache_working_effectively()) {
                        $issues[] = "Page cache is not working effectively";
                        $module_analysis[$module]['potential_issues'][] = 'Cache ineffective';
                    }
                    break;
            }
        }

        return array(
            'enabled_modules' => $enabled_modules,
            'module_analysis' => $module_analysis,
            'issues' => $issues
        );
    }

    /**
     * Analyze resource loading
     */
    private function analyze_resource_loading() {
        $issues = array();
        $analysis = array();

        // Check for render-blocking resources
        global $wp_scripts, $wp_styles;

        $render_blocking = array();

        // Check for scripts in head without defer/async
        if (isset($wp_scripts->queue)) {
            foreach ($wp_scripts->queue as $handle) {
                if (isset($wp_scripts->registered[$handle])) {
                    $script = $wp_scripts->registered[$handle];
                    if (strpos($script->src, 'redco-optimizer') !== false && !$script->extra) {
                        $render_blocking[] = "Script: {$handle}";
                    }
                }
            }
        }

        // Check for CSS without media queries
        if (isset($wp_styles->queue)) {
            foreach ($wp_styles->queue as $handle) {
                if (isset($wp_styles->registered[$handle])) {
                    $style = $wp_styles->registered[$handle];
                    if (strpos($style->src, 'redco-optimizer') !== false && $style->args === 'all') {
                        $render_blocking[] = "Style: {$handle}";
                    }
                }
            }
        }

        if (!empty($render_blocking)) {
            $issues[] = "Render-blocking resources detected: " . implode(', ', $render_blocking);
        }

        $analysis['render_blocking'] = $render_blocking;

        return array(
            'analysis' => $analysis,
            'issues' => $issues
        );
    }

    /**
     * Analyze cache effectiveness
     */
    private function analyze_cache_effectiveness() {
        $cache_stats = get_option('redco_page_cache_stats', array());
        $issues = array();
        $analysis = array();

        if (!empty($cache_stats)) {
            $hit_rate = ($cache_stats['hits'] / ($cache_stats['hits'] + $cache_stats['misses'])) * 100;
            $analysis['hit_rate'] = $hit_rate;

            if ($hit_rate < 70) {
                $issues[] = "Low cache hit rate: {$hit_rate}%";
            }
        } else {
            $issues[] = "No cache statistics available";
        }

        return array(
            'analysis' => $analysis,
            'issues' => $issues
        );
    }

    /**
     * Analyze external resources
     */
    private function analyze_external_resources() {
        global $wp_scripts;

        $issues = array();
        $external_resources = array();

        // Check for external scripts (like Chart.js CDN)
        if (isset($wp_scripts->queue)) {
            foreach ($wp_scripts->queue as $handle) {
                if (isset($wp_scripts->registered[$handle])) {
                    $script = $wp_scripts->registered[$handle];
                    // Ensure src is not null before using strpos
                    $src = $script->src ?? '';
                    $handle_str = (string) $handle;

                    if (!empty($src) && strpos($src, 'http') === 0 && strpos($src, home_url()) === false) {
                        $external_resources[] = array(
                            'handle' => $handle,
                            'src' => $src,
                            'is_redco' => strpos($handle_str, 'redco') !== false || strpos($handle_str, 'chart-js') !== false
                        );

                        if (strpos($handle_str, 'chart-js') !== false) {
                            $issues[] = "External CDN resource (Chart.js) may impact performance";
                        }
                    }
                }
            }
        }

        return array(
            'external_resources' => $external_resources,
            'issues' => $issues
        );
    }

    /**
     * Analyze JavaScript performance
     */
    private function analyze_javascript_performance() {
        $issues = array();
        $analysis = array();

        // Check for large JavaScript files
        $js_files = array(
            'admin-scripts.js' => REDCO_OPTIMIZER_PLUGIN_DIR . 'assets/js/admin-scripts.js',
            'lazy-load.js' => REDCO_OPTIMIZER_PLUGIN_DIR . 'assets/js/lazy-load.js'
        );

        $large_files = array();
        foreach ($js_files as $name => $path) {
            if (file_exists($path)) {
                $size = filesize($path);
                if ($size > 50000) { // 50KB threshold
                    $large_files[] = array(
                        'file' => $name,
                        'size' => $size,
                        'size_formatted' => size_format($size)
                    );

                    if ($name === 'admin-scripts.js' && $size > 100000) {
                        $issues[] = "Large admin JavaScript file may be loading on frontend";
                    }
                }
            }
        }

        $analysis['large_files'] = $large_files;

        return array(
            'analysis' => $analysis,
            'issues' => $issues
        );
    }

    /**
     * Helper methods for specific checks
     */
    private function is_minification_causing_errors() {
        // Check for JavaScript errors in error log or console
        // This is a simplified check - in reality, you'd need more sophisticated error detection
        return false; // Placeholder
    }

    private function is_lazy_load_affecting_lcp() {
        // Check if lazy loading is applied to above-the-fold images
        // This would require more complex analysis
        return false; // Placeholder
    }

    private function is_cache_working_effectively() {
        $cache_stats = get_option('redco_page_cache_stats', array());
        if (empty($cache_stats)) {
            return false;
        }

        $hit_rate = ($cache_stats['hits'] / ($cache_stats['hits'] + $cache_stats['misses'])) * 100;
        return $hit_rate > 70;
    }

    private function get_file_size_from_url($url) {
        $path = str_replace(REDCO_OPTIMIZER_PLUGIN_URL, REDCO_OPTIMIZER_PLUGIN_DIR, $url);
        return file_exists($path) ? filesize($path) : 0;
    }

    /**
     * Store performance metrics
     */
    private function store_metrics() {
        $stored_metrics = get_option('redco_performance_metrics', array());

        // Keep only last 10 measurements
        if (count($stored_metrics) >= 10) {
            array_shift($stored_metrics);
        }

        $stored_metrics[] = array(
            'timestamp' => time(),
            'execution_time' => $this->metrics['execution_time'],
            'memory_usage' => $this->metrics['memory_usage'],
            'query_count' => $this->metrics['query_count']
        );

        update_option('redco_performance_metrics', $stored_metrics);
    }

    /**
     * Get performance recommendations
     */
    public function get_performance_recommendations($analysis) {
        $recommendations = array();

        // Analyze all issues and provide specific recommendations
        foreach ($analysis as $category => $data) {
            if (isset($data['issues']) && !empty($data['issues'])) {
                foreach ($data['issues'] as $issue) {
                    $recommendations[] = $this->get_recommendation_for_issue($issue, $category);
                }
            }
        }

        return array_filter($recommendations);
    }

    private function get_recommendation_for_issue($issue, $category) {
        $recommendations_map = array(
            'Admin script' => 'Ensure admin scripts are only loaded in admin area using proper hooks',
            'Admin style' => 'Ensure admin styles are only loaded in admin area using proper hooks',
            'External CDN' => 'Consider hosting Chart.js locally or loading it conditionally',
            'High number of database queries' => 'Implement query optimization and caching',
            'Slow page execution' => 'Profile and optimize slow code paths',
            'JavaScript errors' => 'Review minification settings and test thoroughly',
            'LCP impact' => 'Exclude above-the-fold images from lazy loading',
            'Low cache hit rate' => 'Review cache configuration and exclusion rules',
            'Render-blocking' => 'Add defer/async attributes to non-critical scripts'
        );

        foreach ($recommendations_map as $keyword => $recommendation) {
            // Ensure issue is not null before using strpos
            $issue_str = (string) $issue;
            if (!empty($issue_str) && strpos($issue_str, $keyword) !== false) {
                return array(
                    'issue' => $issue,
                    'category' => $category,
                    'recommendation' => $recommendation,
                    'priority' => $this->get_priority_for_issue($issue)
                );
            }
        }

        return null;
    }

    private function get_priority_for_issue($issue) {
        $high_priority_keywords = array('Admin script', 'Admin style', 'Render-blocking', 'JavaScript errors');
        $medium_priority_keywords = array('External CDN', 'LCP impact', 'Slow page execution');

        // Ensure issue is not null before using strpos
        $issue_str = (string) $issue;

        foreach ($high_priority_keywords as $keyword) {
            if (!empty($issue_str) && strpos($issue_str, $keyword) !== false) {
                return 'high';
            }
        }

        foreach ($medium_priority_keywords as $keyword) {
            if (!empty($issue_str) && strpos($issue_str, $keyword) !== false) {
                return 'medium';
            }
        }

        return 'low';
    }
}
