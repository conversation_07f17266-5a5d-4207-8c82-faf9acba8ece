<?php
/**
 * Uninstall script for Redco Optimizer
 * 
 * This file is executed when the plugin is deleted from WordPress admin.
 * It cleans up all plugin data, options, and temporary files.
 */

// Prevent direct access
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

/**
 * Clean up plugin options
 */
function redco_optimizer_cleanup_options() {
    // Remove main plugin options
    delete_option('redco_optimizer_options');
    
    // Remove module-specific options
    $modules = array(
        'page_cache',
        'lazy_load',
        'asset_optimization',
        'database_cleanup',
        'heartbeat_control',
        'wordpress_core_tweaks',
        'smart_webp_conversion',
        'cdn_integration',
        'diagnostic_autofix'
    );
    
    foreach ($modules as $module) {
        delete_option('redco_optimizer_' . $module);
    }
    
    // Remove any transients
    delete_transient('redco_optimizer_cache');
    delete_transient('redco_optimizer_db_stats');
}

/**
 * Clean up plugin files and cache
 */
function redco_optimizer_cleanup_files() {
    $upload_dir = wp_upload_dir();
    $cache_dir = $upload_dir['basedir'] . '/redco-optimizer-cache/';
    
    // Remove cache directory if it exists
    if (is_dir($cache_dir)) {
        redco_optimizer_remove_directory($cache_dir);
    }
}

/**
 * Recursively remove directory
 */
function redco_optimizer_remove_directory($dir) {
    if (!is_dir($dir)) {
        return false;
    }
    
    $files = array_diff(scandir($dir), array('.', '..'));
    
    foreach ($files as $file) {
        $path = $dir . '/' . $file;
        if (is_dir($path)) {
            redco_optimizer_remove_directory($path);
        } else {
            unlink($path);
        }
    }
    
    return rmdir($dir);
}

/**
 * Clean up scheduled events
 */
function redco_optimizer_cleanup_cron() {
    // Remove any scheduled cron events
    wp_clear_scheduled_hook('redco_optimizer_database_cleanup');
    wp_clear_scheduled_hook('redco_optimizer_cache_cleanup');
}

// Execute cleanup
redco_optimizer_cleanup_options();
redco_optimizer_cleanup_files();
redco_optimizer_cleanup_cron();

// Allow modules to perform their own cleanup
do_action('redco_optimizer_uninstall');
