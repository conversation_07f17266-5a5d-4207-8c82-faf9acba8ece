<?php
/**
 * Clear any cached backup references that might contain old backup IDs
 */

require_once('d:/xampp/htdocs/wordpress/wp-config.php');

echo "=== CLEARING CACHED BACKUP REFERENCES ===\n";

// Check diagnostic results
$diagnostic_results = get_option('redco_diagnostic_results');
if ($diagnostic_results) {
    if (is_string($diagnostic_results) && strpos($diagnostic_results, '_migrated') !== false) {
        echo "Found migrated references in diagnostic results, clearing...\n";
        delete_option('redco_diagnostic_results');
        echo "✅ Cleared diagnostic results\n";
    } else {
        echo "Diagnostic results exist but no migrated references found\n";
    }
} else {
    echo "No diagnostic results found\n";
}

// Check for any WordPress options that might contain backup references
$all_options = wp_load_alloptions();
$cleared_count = 0;

foreach ($all_options as $option_name => $option_value) {
    if (strpos($option_name, 'redco') !== false) {
        if (is_string($option_value) && strpos($option_value, '_migrated') !== false) {
            echo "Found migrated reference in option: $option_name\n";
            delete_option($option_name);
            $cleared_count++;
            echo "Cleared option: $option_name\n";
        }
    }
}

echo "Cleared $cleared_count options with migrated references\n";

// Clear all WordPress caches
wp_cache_flush();
echo "✅ WordPress caches flushed\n";

// Clear any object cache if available
if (function_exists('wp_cache_flush_runtime')) {
    wp_cache_flush_runtime();
    echo "✅ Runtime cache flushed\n";
}

// Check if there are any transients with backup references
global $wpdb;
$transients = $wpdb->get_results(
    "SELECT option_name, option_value FROM {$wpdb->options} 
     WHERE option_name LIKE '_transient_%' 
     AND option_value LIKE '%_migrated%'"
);

if (!empty($transients)) {
    echo "Found " . count($transients) . " transients with migrated references:\n";
    foreach ($transients as $transient) {
        $transient_name = str_replace('_transient_', '', $transient->option_name);
        delete_transient($transient_name);
        echo "Cleared transient: $transient_name\n";
    }
} else {
    echo "No transients with migrated references found\n";
}

echo "\n=== CACHE CLEARING COMPLETE ===\n";
echo "All cached backup references have been cleared.\n";
echo "Please refresh the diagnostic page to see if the issue is resolved.\n";
