<?php
/**
 * Setup Wizard Class
 * Clean, modern setup wizard for Redco Optimizer
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Optimizer_Setup_Wizard {

    private $current_step = 1;
    private $total_steps = 3;
    private $setup_configs = array();

    public function __construct() {
        $this->init_hooks();
        $this->define_setup_configs();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        add_action('admin_enqueue_scripts', array($this, 'enqueue_assets'));
        add_action('wp_ajax_redco_complete_setup', array($this, 'complete_setup'));
        add_action('wp_ajax_redco_skip_setup', array($this, 'skip_setup'));
        add_action('wp_ajax_redco_rollback_setup', array($this, 'rollback_setup'));
    }

    /**
     * Define setup configurations
     */
    private function define_setup_configs() {
        $this->setup_configs = array(
            'basic' => array(
                'name' => __('Basic', 'redco-optimizer'),
                'description' => __('Perfect for beginners. Safe optimizations with minimal risk of breaking functionality.', 'redco-optimizer'),
                'icon' => 'dashicons-admin-users',
                'modules' => array('page-cache', 'lazy-load', 'wordpress-core-tweaks'),
                'settings' => array(
                    'enable_monitoring' => 1,
                    'update_interval' => 60,
                    'auto_enable_modules' => 0
                ),
                'module_settings' => array(
                    'page-cache' => array(
                        'cache_duration' => 3600, // 1 hour - conservative
                        'enable_gzip' => 1,
                        'exclude_logged_users' => 1,
                        'cache_mobile' => 0, // Safer for compatibility
                        'preload_cache' => 0
                    ),
                    'lazy-load' => array(
                        'enable_images' => 1,
                        'enable_iframes' => 0, // Conservative - avoid breaking embeds
                        'threshold' => 200, // Load earlier for better UX
                        'fade_in' => 1,
                        'placeholder_type' => 'blur'
                    ),
                    'wordpress-core-tweaks' => array(
                        'remove_emoji_scripts' => 1,
                        'remove_emoji_styles' => 1,
                        'disable_autosave' => 0, // Conservative
                        'remove_version_strings' => 1
                    )
                )
            ),
            'professional' => array(
                'name' => __('Professional', 'redco-optimizer'),
                'description' => __('Recommended for most websites. Balanced performance gains with low risk.', 'redco-optimizer'),
                'icon' => 'dashicons-businessman',
                'modules' => array('page-cache', 'lazy-load', 'asset-optimization', 'database-cleanup', 'heartbeat-control', 'wordpress-core-tweaks', 'smart-webp-conversion'),
                'settings' => array(
                    'enable_monitoring' => 1,
                    'update_interval' => 30,
                    'auto_enable_modules' => 1
                ),
                'module_settings' => array(
                    'page-cache' => array(
                        'cache_duration' => 7200, // 2 hours
                        'enable_gzip' => 1,
                        'exclude_logged_users' => 1,
                        'cache_mobile' => 1,
                        'preload_cache' => 1
                    ),
                    'lazy-load' => array(
                        'enable_images' => 1,
                        'enable_iframes' => 1,
                        'threshold' => 100,
                        'fade_in' => 1,
                        'placeholder_type' => 'blur'
                    ),
                    'asset-optimization' => array(
                        'minify_css' => 1,
                        'minify_js' => 1,
                        'combine_css' => 0, // Safer approach
                        'combine_js' => 0,
                        'defer_js' => 1
                    ),
                    'database-cleanup' => array(
                        'auto_cleanup' => 1,
                        'cleanup_frequency' => 'weekly',
                        'remove_revisions' => 1,
                        'remove_spam' => 1,
                        'remove_trash' => 1,
                        'optimize_tables' => 1
                    ),
                    'heartbeat-control' => array(
                        'frontend_heartbeat' => 0,
                        'admin_heartbeat_interval' => 60,
                        'post_edit_heartbeat_interval' => 30
                    ),
                    'smart-webp-conversion' => array(
                        'auto_convert' => 1,
                        'quality' => 85,
                        'convert_on_upload' => 1,
                        'serve_webp' => 1
                    )
                )
            ),
            'advanced' => array(
                'name' => __('Advanced', 'redco-optimizer'),
                'description' => __('Maximum optimization for experts. Aggressive settings for maximum performance.', 'redco-optimizer'),
                'icon' => 'dashicons-admin-tools',
                'modules' => array('page-cache', 'lazy-load', 'asset-optimization', 'database-cleanup', 'heartbeat-control', 'wordpress-core-tweaks', 'smart-webp-conversion', 'cdn-integration', 'diagnostic-autofix'),
                'settings' => array(
                    'enable_monitoring' => 1,
                    'update_interval' => 15,
                    'auto_enable_modules' => 1
                ),
                'module_settings' => array(
                    'page-cache' => array(
                        'cache_duration' => 14400, // 4 hours
                        'enable_gzip' => 1,
                        'exclude_logged_users' => 0, // Cache for all users
                        'cache_mobile' => 1,
                        'preload_cache' => 1,
                        'cache_query_strings' => 1
                    ),
                    'lazy-load' => array(
                        'enable_images' => 1,
                        'enable_iframes' => 1,
                        'threshold' => 50, // More aggressive
                        'fade_in' => 1,
                        'placeholder_type' => 'blur'
                    ),
                    'asset-optimization' => array(
                        'minify_css' => 1,
                        'minify_js' => 1,
                        'combine_css' => 1, // Aggressive combining
                        'combine_js' => 1,
                        'defer_js' => 1,
                        'remove_unused_css' => 1
                    ),
                    'database-cleanup' => array(
                        'auto_cleanup' => 1,
                        'cleanup_frequency' => 'daily',
                        'remove_revisions' => 1,
                        'remove_spam' => 1,
                        'remove_trash' => 1,
                        'optimize_tables' => 1,
                        'remove_transients' => 1
                    ),
                    'heartbeat-control' => array(
                        'frontend_heartbeat' => 0,
                        'admin_heartbeat_interval' => 120, // Less frequent
                        'post_edit_heartbeat_interval' => 60
                    ),
                    'smart-webp-conversion' => array(
                        'auto_convert' => 1,
                        'quality' => 80, // Slightly more aggressive
                        'convert_on_upload' => 1,
                        'serve_webp' => 1,
                        'convert_existing' => 1
                    ),
                    'wordpress-core-tweaks' => array(
                        'remove_emoji_scripts' => 1,
                        'remove_emoji_styles' => 1,
                        'disable_autosave' => 1, // More aggressive
                        'remove_version_strings' => 1,
                        'remove_query_strings' => 1
                    ),
                    'cdn-integration' => array(
                        'enable_cdn' => 1,
                        'cdn_url' => '',
                        'include_images' => 1,
                        'include_css' => 1,
                        'include_js' => 1
                    ),
                    'diagnostic-autofix' => array(
                        'auto_fix_enabled' => 1,
                        'monitoring_enabled' => 1,
                        'performance_alerts' => 1
                    )
                )
            )
        );
    }

    /**
     * Check if setup wizard should be shown
     */
    public function should_show_wizard() {
        $setup_completed = get_option('redco_optimizer_setup_completed', false);
        $setup_skipped = get_option('redco_optimizer_setup_skipped', false);
        return !$setup_completed && !$setup_skipped;
    }

    /**
     * Enqueue assets
     */
    public function enqueue_assets($hook) {
        // Check if we're on the setup wizard page
        if (!isset($_GET['page']) || $_GET['page'] !== 'redco-optimizer-setup') {
            return;
        }

        wp_enqueue_style(
            'redco-setup-wizard',
            REDCO_OPTIMIZER_PLUGIN_URL . 'assets/css/setup-wizard.css',
            array(),
            REDCO_OPTIMIZER_VERSION
        );

        wp_enqueue_script(
            'redco-setup-wizard',
            REDCO_OPTIMIZER_PLUGIN_URL . 'assets/js/setup-wizard.js',
            array('jquery'),
            REDCO_OPTIMIZER_VERSION,
            true
        );

        wp_localize_script('redco-setup-wizard', 'redcoWizard', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('redco_setup_wizard'),
            'adminUrl' => admin_url(),
            'strings' => array(
                'saving' => __('Saving...', 'redco-optimizer'),
                'saved' => __('Saved!', 'redco-optimizer'),
                'error' => __('Error occurred', 'redco-optimizer'),
                'confirmSkip' => __('Are you sure you want to skip the setup?', 'redco-optimizer'),
                'selectSetupType' => __('Please select a setup type', 'redco-optimizer')
            )
        ));
    }

    /**
     * Render setup wizard
     */
    public function render_wizard() {
        // Enqueue assets directly
        wp_enqueue_style(
            'redco-setup-wizard',
            REDCO_OPTIMIZER_PLUGIN_URL . 'assets/css/setup-wizard.css',
            array(),
            REDCO_OPTIMIZER_VERSION
        );

        wp_enqueue_script(
            'redco-setup-wizard',
            REDCO_OPTIMIZER_PLUGIN_URL . 'assets/js/setup-wizard.js',
            array('jquery'),
            REDCO_OPTIMIZER_VERSION,
            true
        );

        wp_localize_script('redco-setup-wizard', 'redcoWizard', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('redco_setup_wizard'),
            'adminUrl' => admin_url(),
            'strings' => array(
                'saving' => __('Saving...', 'redco-optimizer'),
                'saved' => __('Saved!', 'redco-optimizer'),
                'error' => __('Error occurred', 'redco-optimizer'),
                'confirmSkip' => __('Are you sure you want to skip the setup?', 'redco-optimizer'),
                'selectSetupType' => __('Please select a setup type', 'redco-optimizer')
            )
        ));

        $this->current_step = isset($_GET['step']) ? absint($_GET['step']) : 1;
        if ($this->current_step < 1 || $this->current_step > $this->total_steps) {
            $this->current_step = 1;
        }
        ?>
        <style>
        /* Inline fallback styles */
        .redco-setup-wizard { background: #f1f1f1; margin: 0; padding: 0; }
        .redco-setup-wizard .wrap { margin: 0; padding: 0; }
        .wizard-container { max-width: 800px; margin: 0 auto; background: #fff; min-height: 100vh; box-shadow: 0 0 20px rgba(0, 0, 0, 0.1); }
        .wizard-header { background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); color: white; padding: 40px; text-align: center; }
        .wizard-logo { margin-bottom: 30px; }
        .wizard-logo .dashicons { font-size: 48px; width: 48px; height: 48px; margin-bottom: 15px; }
        .wizard-logo h1 { color: white; font-size: 28px; font-weight: 300; margin: 0; }
        .wizard-progress { max-width: 400px; margin: 0 auto; }
        .progress-bar { background: rgba(255, 255, 255, 0.2); height: 4px; border-radius: 2px; margin-bottom: 20px; overflow: hidden; }
        .progress-fill { background: white; height: 100%; border-radius: 2px; transition: width 0.3s ease; }
        .progress-steps { display: flex; justify-content: space-between; margin-bottom: 10px; }
        .step-indicator { width: 32px; height: 32px; border-radius: 50%; background: rgba(255, 255, 255, 0.2); display: flex; align-items: center; justify-content: center; font-size: 14px; font-weight: 600; transition: all 0.3s ease; }
        .step-indicator.active { background: white; color: #4CAF50; }
        .step-indicator.completed { background: white; color: #4CAF50; }
        .progress-text { font-size: 14px; opacity: 0.9; }
        .wizard-content { padding: 60px 40px; }
        .wizard-step h2 { font-size: 32px; font-weight: 300; margin: 0 0 15px 0; text-align: center; color: #333; }
        .step-description { font-size: 16px; color: #666; text-align: center; margin: 0 0 40px 0; line-height: 1.6; }
        .features-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px; margin-top: 40px; }
        .feature-item { text-align: center; padding: 30px 20px; border: 1px solid #e1e1e1; border-radius: 8px; transition: all 0.3s ease; }
        .feature-item:hover { border-color: #4CAF50; box-shadow: 0 4px 12px rgba(76, 175, 80, 0.1); }
        .feature-item .dashicons { font-size: 48px; width: 48px; height: 48px; color: #4CAF50; margin-bottom: 20px; }
        .feature-item h3 { font-size: 18px; margin: 0 0 10px 0; color: #333; }
        .feature-item p { color: #666; margin: 0; line-height: 1.5; }
        .setup-options { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 40px; }
        .setup-option { border: 2px solid #e1e1e1; border-radius: 12px; padding: 30px 20px; text-align: center; cursor: pointer; transition: all 0.3s ease; position: relative; }
        .setup-option:hover { border-color: #4CAF50; box-shadow: 0 4px 12px rgba(76, 175, 80, 0.1); }
        .setup-option.selected { border-color: #4CAF50; background: #f8fff8; box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2); }
        .option-header { margin-bottom: 15px; }
        .option-header .dashicons { font-size: 48px; width: 48px; height: 48px; color: #4CAF50; margin-bottom: 15px; }
        .option-header h3 { font-size: 20px; margin: 0; color: #333; }
        .setup-option p { color: #666; margin: 0 0 20px 0; line-height: 1.5; }
        .option-details { text-align: left; background: #f9f9f9; padding: 15px; border-radius: 6px; margin-top: 15px; }
        .completion-message { text-align: center; margin-bottom: 40px; }
        .completion-message .dashicons { font-size: 64px; width: 64px; height: 64px; color: #4CAF50; margin-bottom: 20px; }
        .next-steps-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }
        .next-step-item { display: block; padding: 25px 20px; border: 1px solid #e1e1e1; border-radius: 8px; text-decoration: none; text-align: center; transition: all 0.3s ease; }
        .next-step-item:hover { border-color: #4CAF50; box-shadow: 0 4px 12px rgba(76, 175, 80, 0.1); text-decoration: none; }
        .wizard-footer { background: #f9f9f9; border-top: 1px solid #e1e1e1; padding: 30px 40px; }
        .footer-actions { display: flex; justify-content: space-between; align-items: center; }
        .footer-actions-right { display: flex; gap: 15px; align-items: center; }
        .wizard-btn-back, .wizard-btn-next, .wizard-btn-setup, .wizard-btn-finish { display: inline-flex; align-items: center; gap: 8px; padding: 12px 24px; font-size: 14px; border-radius: 6px; text-decoration: none; transition: all 0.3s ease; }
        .wizard-btn-skip { color: #666; text-decoration: none; padding: 12px 16px; border-radius: 6px; transition: all 0.3s ease; }
        .wizard-btn-skip:hover { color: #333; background: #f0f0f0; text-decoration: none; }
        </style>

        <div class="wrap redco-setup-wizard">
            <div class="wizard-container">
                <?php $this->render_header(); ?>
                <?php $this->render_content(); ?>
                <?php $this->render_footer(); ?>
            </div>
        </div>

        <script>
        console.log('Setup wizard loaded');
        console.log('CSS file URL: <?php echo REDCO_OPTIMIZER_PLUGIN_URL . "assets/css/setup-wizard.css"; ?>');
        </script>
        <?php
    }

    /**
     * Render wizard header
     */
    private function render_header() {
        ?>
        <div class="wizard-header">
            <div class="wizard-logo">
                <span class="dashicons dashicons-performance"></span>
                <h1><?php _e('Redco Optimizer Setup', 'redco-optimizer'); ?></h1>
            </div>

            <div class="wizard-progress" data-current-step="<?php echo $this->current_step; ?>" data-total-steps="<?php echo $this->total_steps; ?>">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: <?php echo ($this->current_step / $this->total_steps) * 100; ?>%"></div>
                </div>
                <div class="progress-steps">
                    <?php for ($i = 1; $i <= $this->total_steps; $i++): ?>
                        <div class="step-indicator <?php echo $i === $this->current_step ? 'active' : ($i < $this->current_step ? 'completed' : 'pending'); ?>">
                            <?php if ($i < $this->current_step): ?>
                                <span class="dashicons dashicons-yes"></span>
                            <?php else: ?>
                                <span><?php echo $i; ?></span>
                            <?php endif; ?>
                        </div>
                    <?php endfor; ?>
                </div>
                <div class="progress-text">
                    <?php printf(__('Step %d of %d', 'redco-optimizer'), $this->current_step, $this->total_steps); ?>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render wizard content
     */
    private function render_content() {
        ?>
        <div class="wizard-content">
            <?php
            switch ($this->current_step) {
                case 1:
                    $this->render_welcome_step();
                    break;
                case 2:
                    $this->render_setup_type_step();
                    break;
                case 3:
                    $this->render_complete_step();
                    break;
            }
            ?>
        </div>
        <?php
    }

    /**
     * Render welcome step
     */
    private function render_welcome_step() {
        ?>
        <div class="wizard-step">
            <h2><?php _e('Welcome to Redco Optimizer!', 'redco-optimizer'); ?></h2>
            <p class="step-description">
                <?php _e('This quick setup wizard will help you configure Redco Optimizer for optimal performance on your website. It will only take a few minutes.', 'redco-optimizer'); ?>
            </p>

            <div class="features-grid">
                <div class="feature-item">
                    <span class="dashicons dashicons-performance"></span>
                    <h3><?php _e('Page Caching', 'redco-optimizer'); ?></h3>
                    <p><?php _e('Speed up your website with intelligent caching', 'redco-optimizer'); ?></p>
                </div>
                <div class="feature-item">
                    <span class="dashicons dashicons-images-alt2"></span>
                    <h3><?php _e('Smart WebP Conversion', 'redco-optimizer'); ?></h3>
                    <p><?php _e('Automatically convert images to WebP format', 'redco-optimizer'); ?></p>
                </div>
                <div class="feature-item">
                    <span class="dashicons dashicons-database"></span>
                    <h3><?php _e('Database Cleanup', 'redco-optimizer'); ?></h3>
                    <p><?php _e('Remove unnecessary data and optimize database', 'redco-optimizer'); ?></p>
                </div>
                <div class="feature-item">
                    <span class="dashicons dashicons-admin-generic"></span>
                    <h3><?php _e('Asset Optimization', 'redco-optimizer'); ?></h3>
                    <p><?php _e('Minify and optimize CSS and JavaScript files', 'redco-optimizer'); ?></p>
                </div>
                <div class="feature-item">
                    <span class="dashicons dashicons-networking"></span>
                    <h3><?php _e('CDN Integration', 'redco-optimizer'); ?></h3>
                    <p><?php _e('Integrate with CDN providers for global delivery', 'redco-optimizer'); ?></p>
                </div>
                <div class="feature-item">
                    <span class="dashicons dashicons-admin-tools"></span>
                    <h3><?php _e('Diagnostic & Auto-Fix', 'redco-optimizer'); ?></h3>
                    <p><?php _e('Automatically detect and fix performance issues', 'redco-optimizer'); ?></p>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render setup type step
     */
    private function render_setup_type_step() {
        ?>
        <div class="wizard-step">
            <h2><?php _e('Choose Your Setup Type', 'redco-optimizer'); ?></h2>
            <p class="step-description">
                <?php _e('Select the configuration that best matches your experience level and website needs.', 'redco-optimizer'); ?>
            </p>

            <div class="setup-options">
                <?php foreach ($this->setup_configs as $key => $config): ?>
                    <div class="setup-option" data-setup-type="<?php echo esc_attr($key); ?>">
                        <div class="option-header">
                            <span class="dashicons <?php echo esc_attr($config['icon']); ?>"></span>
                            <h3><?php echo esc_html($config['name']); ?></h3>
                        </div>
                        <p><?php echo esc_html($config['description']); ?></p>
                        <div class="option-details">
                            <strong><?php _e('Includes:', 'redco-optimizer'); ?></strong>
                            <ul>
                                <?php foreach ($config['modules'] as $module): ?>
                                    <li><?php echo esc_html($this->get_module_name($module)); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <input type="hidden" id="selected-setup-type" value="">
        </div>
        <?php
    }

    /**
     * Render complete step
     */
    private function render_complete_step() {
        $setup_type = get_option('redco_optimizer_setup_type', '');
        $setup_date = get_option('redco_optimizer_setup_date', '');
        $config = isset($this->setup_configs[$setup_type]) ? $this->setup_configs[$setup_type] : null;
        ?>
        <div class="wizard-step">
            <div class="completion-message">
                <span class="dashicons dashicons-yes-alt"></span>
                <h2><?php _e('Setup Complete!', 'redco-optimizer'); ?></h2>
                <p><?php _e('Redco Optimizer has been successfully configured for your website.', 'redco-optimizer'); ?></p>

                <?php if ($config): ?>
                <div class="setup-summary">
                    <h3><?php printf(__('Configuration: %s Level', 'redco-optimizer'), $config['name']); ?></h3>
                    <p><?php echo esc_html($config['description']); ?></p>

                    <div class="configured-modules">
                        <h4><?php _e('Enabled Modules:', 'redco-optimizer'); ?></h4>
                        <ul class="module-list">
                            <?php foreach ($config['modules'] as $module): ?>
                                <li>
                                    <span class="dashicons dashicons-yes"></span>
                                    <?php echo esc_html($this->get_module_name($module)); ?>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>

                    <?php if ($setup_date): ?>
                    <p class="setup-date">
                        <small><?php printf(__('Configured on: %s', 'redco-optimizer'), date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($setup_date))); ?></small>
                    </p>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>

            <div class="next-steps">
                <h3><?php _e('What\'s Next?', 'redco-optimizer'); ?></h3>
                <div class="next-steps-grid">
                    <a href="<?php echo admin_url('admin.php?page=redco-optimizer'); ?>" class="next-step-item">
                        <span class="dashicons dashicons-dashboard"></span>
                        <h4><?php _e('View Dashboard', 'redco-optimizer'); ?></h4>
                        <p><?php _e('Monitor your website\'s performance', 'redco-optimizer'); ?></p>
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>" class="next-step-item">
                        <span class="dashicons dashicons-admin-plugins"></span>
                        <h4><?php _e('Manage Modules', 'redco-optimizer'); ?></h4>
                        <p><?php _e('Enable or disable optimization modules', 'redco-optimizer'); ?></p>
                    </a>
                    <a href="<?php echo admin_url('admin.php?page=redco-optimizer-settings'); ?>" class="next-step-item">
                        <span class="dashicons dashicons-admin-settings"></span>
                        <h4><?php _e('Fine-tune Settings', 'redco-optimizer'); ?></h4>
                        <p><?php _e('Customize optimization settings', 'redco-optimizer'); ?></p>
                    </a>
                </div>

                <div class="rollback-section">
                    <h4><?php _e('Need to Change Configuration?', 'redco-optimizer'); ?></h4>
                    <p><?php _e('If you\'re experiencing issues, you can rollback to your previous settings or run the setup wizard again.', 'redco-optimizer'); ?></p>
                    <div class="rollback-actions">
                        <button type="button" class="button button-secondary wizard-btn-rollback">
                            <span class="dashicons dashicons-undo"></span>
                            <?php _e('Rollback Configuration', 'redco-optimizer'); ?>
                        </button>
                        <a href="<?php echo admin_url('admin.php?page=redco-optimizer-setup&step=1&reset=1'); ?>" class="button button-link">
                            <?php _e('Run Setup Again', 'redco-optimizer'); ?>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render wizard footer
     */
    private function render_footer() {
        ?>
        <div class="wizard-footer">
            <div class="footer-actions">
                <?php if ($this->current_step > 1): ?>
                    <a href="<?php echo admin_url('admin.php?page=redco-optimizer-setup&step=' . ($this->current_step - 1)); ?>"
                       class="button button-secondary wizard-btn-back">
                        <span class="dashicons dashicons-arrow-left-alt2"></span>
                        <?php _e('Back', 'redco-optimizer'); ?>
                    </a>
                <?php endif; ?>

                <div class="footer-actions-right">
                    <?php if ($this->current_step < $this->total_steps): ?>
                        <button type="button" class="button button-link wizard-btn-skip">
                            <?php _e('Skip Setup', 'redco-optimizer'); ?>
                        </button>

                        <?php if ($this->current_step === 1): ?>
                            <a href="<?php echo admin_url('admin.php?page=redco-optimizer-setup&step=2'); ?>"
                               class="button button-primary wizard-btn-next">
                                <?php _e('Get Started', 'redco-optimizer'); ?>
                                <span class="dashicons dashicons-arrow-right-alt2"></span>
                            </a>
                        <?php elseif ($this->current_step === 2): ?>
                            <button type="button" class="button button-primary wizard-btn-setup">
                                <?php _e('Setup Redco Optimizer', 'redco-optimizer'); ?>
                                <span class="dashicons dashicons-arrow-right-alt2"></span>
                            </button>
                        <?php endif; ?>
                    <?php else: ?>
                        <a href="<?php echo admin_url('admin.php?page=redco-optimizer'); ?>"
                           class="button button-primary wizard-btn-finish">
                            <?php _e('Go to Dashboard', 'redco-optimizer'); ?>
                            <span class="dashicons dashicons-external"></span>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Get module name by key
     */
    private function get_module_name($module_key) {
        $module_names = array(
            'page-cache' => __('Page Caching', 'redco-optimizer'),
            'lazy-load' => __('Lazy Load Images', 'redco-optimizer'),
            'asset-optimization' => __('Asset Optimization', 'redco-optimizer'),
            'database-cleanup' => __('Database Cleanup', 'redco-optimizer'),
            'heartbeat-control' => __('Heartbeat Control', 'redco-optimizer'),
            'wordpress-core-tweaks' => __('WordPress Core Tweaks', 'redco-optimizer'),
            'smart-webp-conversion' => __('Smart WebP Conversion', 'redco-optimizer'),
            'cdn-integration' => __('CDN Integration', 'redco-optimizer'),
            'diagnostic-autofix' => __('Diagnostic & Auto-Fix', 'redco-optimizer'),
            // Legacy module names for backward compatibility
            'css-js-minifier' => __('CSS/JS Minification', 'redco-optimizer'),
            'emoji-stripper' => __('Emoji Stripper', 'redco-optimizer'),
            'webp-converter' => __('WebP Image Conversion', 'redco-optimizer'),
            'query-string-remover' => __('Query String Remover', 'redco-optimizer'),
            'asset-version-remover' => __('Asset Version Remover', 'redco-optimizer'),
            'autosave-reducer' => __('Autosave Reducer', 'redco-optimizer'),
            'critical-resource-optimizer' => __('Critical Resource Optimizer', 'redco-optimizer')
        );

        return isset($module_names[$module_key]) ? $module_names[$module_key] : $module_key;
    }

    /**
     * Complete setup via AJAX
     */
    public function complete_setup() {
        check_ajax_referer('redco_setup_wizard', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'redco-optimizer'));
        }

        $setup_type = sanitize_text_field($_POST['setup_type']);

        if (!isset($this->setup_configs[$setup_type])) {
            wp_send_json_error(__('Invalid setup type', 'redco-optimizer'));
        }

        $config = $this->setup_configs[$setup_type];

        try {
            // Store backup of current settings for rollback
            $this->create_settings_backup();

            // Enable selected modules
            $enabled_modules = get_option('redco_optimizer_options', array());
            $enabled_modules['modules_enabled'] = $config['modules'];
            update_option('redco_optimizer_options', $enabled_modules);

            // Apply general settings
            $performance_settings = get_option('redco_optimizer_performance', array());
            $performance_settings = array_merge($performance_settings, $config['settings']);
            update_option('redco_optimizer_performance', $performance_settings);

            // Apply module-specific settings
            $configured_modules = $this->apply_module_settings($config['module_settings']);

            // Mark setup as completed
            update_option('redco_optimizer_setup_completed', true);
            update_option('redco_optimizer_setup_type', $setup_type);
            update_option('redco_optimizer_setup_date', current_time('mysql'));

            // Generate success message with configured modules
            $success_message = sprintf(
                __('Setup completed successfully! Configured %d modules with %s level optimizations.', 'redco-optimizer'),
                count($configured_modules),
                $config['name']
            );

            wp_send_json_success(array(
                'message' => $success_message,
                'configured_modules' => $configured_modules,
                'setup_type' => $setup_type,
                'redirect_url' => admin_url('admin.php?page=redco-optimizer-setup&step=3')
            ));

        } catch (Exception $e) {
            // Restore backup on error
            $this->restore_settings_backup();
            wp_send_json_error(__('Error completing setup: ', 'redco-optimizer') . $e->getMessage());
        }
    }

    /**
     * Skip setup via AJAX
     */
    public function skip_setup() {
        check_ajax_referer('redco_setup_wizard', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'redco-optimizer'));
        }

        update_option('redco_optimizer_setup_skipped', true);
        update_option('redco_optimizer_setup_skip_date', current_time('mysql'));

        wp_send_json_success(array(
            'message' => __('Setup skipped', 'redco-optimizer'),
            'redirect_url' => admin_url('admin.php?page=redco-optimizer')
        ));
    }

    /**
     * Apply module-specific settings
     */
    private function apply_module_settings($module_settings) {
        $configured_modules = array();

        foreach ($module_settings as $module_key => $settings) {
            $option_name = $this->get_module_option_name($module_key);

            if ($option_name) {
                // Get existing settings and merge with new ones
                $existing_settings = get_option($option_name, array());
                $merged_settings = array_merge($existing_settings, $settings);

                // Update the option
                update_option($option_name, $merged_settings);

                $configured_modules[] = array(
                    'module' => $module_key,
                    'name' => $this->get_module_name($module_key),
                    'settings_count' => count($settings)
                );
            }
        }

        return $configured_modules;
    }

    /**
     * Get WordPress option name for a module
     */
    private function get_module_option_name($module_key) {
        $option_mapping = array(
            'page-cache' => 'redco_optimizer_page_cache',
            'lazy-load' => 'redco_optimizer_lazy_load',
            'asset-optimization' => 'redco_optimizer_asset_optimization',
            'database-cleanup' => 'redco_optimizer_database_cleanup',
            'heartbeat-control' => 'redco_optimizer_heartbeat_control',
            'wordpress-core-tweaks' => 'redco_optimizer_wordpress_core_tweaks',
            'smart-webp-conversion' => 'redco_optimizer_smart_webp_conversion',
            'cdn-integration' => 'redco_optimizer_cdn_integration',
            'diagnostic-autofix' => 'redco_optimizer_diagnostic_autofix',
            // Legacy mappings for backward compatibility
            'css-js-minifier' => 'redco_optimizer_css_js_minifier',
            'emoji-stripper' => 'redco_optimizer_emoji_stripper',
            'webp-converter' => 'redco_optimizer_webp_converter',
            'query-string-remover' => 'redco_optimizer_query_string_remover',
            'asset-version-remover' => 'redco_optimizer_asset_version_remover'
        );

        return isset($option_mapping[$module_key]) ? $option_mapping[$module_key] : null;
    }

    /**
     * Create backup of current settings
     */
    private function create_settings_backup() {
        $backup_data = array(
            'timestamp' => current_time('mysql'),
            'options' => array()
        );

        // Backup main options
        $main_options = array(
            'redco_optimizer_options',
            'redco_optimizer_performance',
            'redco_optimizer_page_cache',
            'redco_optimizer_lazy_load',
            'redco_optimizer_asset_optimization',
            'redco_optimizer_database_cleanup',
            'redco_optimizer_heartbeat_control',
            'redco_optimizer_wordpress_core_tweaks',
            'redco_optimizer_smart_webp_conversion',
            'redco_optimizer_cdn_integration',
            'redco_optimizer_diagnostic_autofix',
            // Legacy options for backward compatibility
            'redco_optimizer_css_js_minifier',
            'redco_optimizer_emoji_stripper',
            'redco_optimizer_webp_converter',
            'redco_optimizer_query_string_remover',
            'redco_optimizer_asset_version_remover'
        );

        foreach ($main_options as $option_name) {
            $backup_data['options'][$option_name] = get_option($option_name, array());
        }

        update_option('redco_optimizer_settings_backup', $backup_data);
    }

    /**
     * Restore settings from backup
     */
    private function restore_settings_backup() {
        $backup_data = get_option('redco_optimizer_settings_backup', array());

        if (!empty($backup_data['options'])) {
            foreach ($backup_data['options'] as $option_name => $option_value) {
                update_option($option_name, $option_value);
            }
        }
    }

    /**
     * Rollback to previous configuration
     */
    public function rollback_setup() {
        check_ajax_referer('redco_setup_wizard', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Insufficient permissions', 'redco-optimizer'));
        }

        try {
            $this->restore_settings_backup();

            // Reset setup flags
            delete_option('redco_optimizer_setup_completed');
            delete_option('redco_optimizer_setup_type');
            delete_option('redco_optimizer_setup_date');

            wp_send_json_success(array(
                'message' => __('Configuration rolled back successfully!', 'redco-optimizer')
            ));

        } catch (Exception $e) {
            wp_send_json_error(__('Error during rollback: ', 'redco-optimizer') . $e->getMessage());
        }
    }

    /**
     * Reset setup wizard
     */
    public function reset_wizard() {
        delete_option('redco_optimizer_setup_completed');
        delete_option('redco_optimizer_setup_skipped');
        delete_option('redco_optimizer_setup_type');
        delete_option('redco_optimizer_setup_date');
        delete_option('redco_optimizer_setup_skip_date');
        delete_option('redco_optimizer_settings_backup');
    }
}