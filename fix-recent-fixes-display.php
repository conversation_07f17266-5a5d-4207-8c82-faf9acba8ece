<?php
/**
 * Comprehensive fix for Recent Fixes display issues
 */

require_once('d:/xampp/htdocs/wordpress/wp-config.php');

echo "=== FIXING RECENT FIXES DISPLAY ISSUES ===\n";

// Step 1: Clear all caches that might interfere
echo "\n1. CLEARING CACHES:\n";

// Clear WordPress caches
wp_cache_flush();
echo "✅ WordPress cache cleared\n";

// Clear any transients related to diagnostic
global $wpdb;
$transients_cleared = $wpdb->query(
    "DELETE FROM {$wpdb->options} 
     WHERE option_name LIKE '_transient_redco_%' 
     OR option_name LIKE '_transient_timeout_redco_%'"
);
echo "✅ Cleared $transients_cleared diagnostic transients\n";

// Step 2: Verify fix history exists and is valid
echo "\n2. VERIFYING FIX HISTORY:\n";
$fix_history = get_option('redco_diagnostic_fix_history', array());
echo "Fix history sessions: " . count($fix_history) . "\n";

if (empty($fix_history)) {
    echo "⚠️  No fix history found - creating test session\n";
    
    // Create a test session for demonstration
    $test_session = array(
        'timestamp' => time(),
        'fixes_applied' => 1,
        'fixes_failed' => 0,
        'backup_created' => true,
        'rollback_id' => 'backup_2025-06-06_06-30-00_test123',
        'details' => array(
            array(
                'issue_id' => 'test_compression',
                'issue_title' => 'Test Compression Issue',
                'success' => true,
                'message' => 'Test fix applied successfully',
                'timestamp' => time()
            )
        )
    );
    
    update_option('redco_diagnostic_fix_history', array($test_session));
    echo "✅ Test session created\n";
} else {
    echo "✅ Fix history exists with valid sessions\n";
    
    // Show recent sessions
    foreach (array_slice($fix_history, -3) as $index => $session) {
        echo "  Session: fixes_applied={$session['fixes_applied']}, rollback_id=" . ($session['rollback_id'] ?? 'NONE') . "\n";
    }
}

// Step 3: Test Recent Fixes AJAX endpoint directly
echo "\n3. TESTING RECENT FIXES AJAX ENDPOINT:\n";

try {
    // Set up WordPress environment for AJAX
    define('DOING_AJAX', true);
    wp_set_current_user(1); // Assume admin user
    
    require_once('modules/diagnostic-autofix/class-diagnostic-autofix.php');
    $diagnostic = new Redco_Diagnostic_AutoFix();
    
    // Simulate AJAX request
    $_POST['nonce'] = wp_create_nonce('redco_diagnostic_nonce');
    $_POST['action'] = 'redco_load_recent_fixes';
    
    // Capture output
    ob_start();
    
    // Use reflection to call the AJAX method
    $reflection = new ReflectionClass($diagnostic);
    $ajax_method = $reflection->getMethod('ajax_load_recent_fixes');
    $ajax_method->setAccessible(true);
    
    $ajax_method->invoke($diagnostic);
    $ajax_output = ob_get_clean();
    
    echo "AJAX output length: " . strlen($ajax_output) . " characters\n";
    
    if (strlen($ajax_output) > 0) {
        $response = json_decode($ajax_output, true);
        if ($response && $response['success']) {
            echo "✅ AJAX endpoint working correctly\n";
            echo "Response success: " . ($response['success'] ? 'YES' : 'NO') . "\n";
            
            if (isset($response['data']['html'])) {
                $html = $response['data']['html'];
                $fix_items = substr_count($html, 'class="fix-item"');
                echo "Fix items in HTML: $fix_items\n";
                
                if ($fix_items > 0) {
                    echo "✅ Recent Fixes HTML generated successfully\n";
                } else {
                    echo "❌ No fix items in generated HTML\n";
                    echo "HTML sample: " . substr($html, 0, 200) . "...\n";
                }
            } else {
                echo "❌ No HTML data in response\n";
            }
            
            if (isset($response['data']['count'])) {
                echo "Response count: " . $response['data']['count'] . "\n";
            }
        } else {
            echo "❌ AJAX response failed\n";
            echo "Response: " . substr($ajax_output, 0, 500) . "\n";
        }
    } else {
        echo "❌ No AJAX output received\n";
    }
    
} catch (Exception $e) {
    echo "❌ AJAX test failed: " . $e->getMessage() . "\n";
}

// Step 4: Check backup directories exist
echo "\n4. CHECKING BACKUP DIRECTORIES:\n";

require_once('modules/diagnostic-autofix/class-diagnostic-autofix-engine.php');
$engine = new Redco_Diagnostic_AutoFix_Engine();

$reflection = new ReflectionClass($engine);
$backup_dir_property = $reflection->getProperty('backup_dir');
$backup_dir_property->setAccessible(true);
$backup_dir = $backup_dir_property->getValue($engine);

echo "Backup directory: $backup_dir\n";
echo "Directory exists: " . (is_dir($backup_dir) ? 'YES' : 'NO') . "\n";

if (is_dir($backup_dir)) {
    $backups = glob($backup_dir . 'backup_*');
    echo "Backup directories found: " . count($backups) . "\n";
    
    // Check if fix history backup IDs match actual directories
    $fix_history = get_option('redco_diagnostic_fix_history', array());
    $mismatched_backups = 0;
    
    foreach ($fix_history as $session) {
        if (isset($session['rollback_id']) && !empty($session['rollback_id'])) {
            $rollback_id = $session['rollback_id'];
            $backup_path = $backup_dir . $rollback_id;
            
            if (!is_dir($backup_path)) {
                echo "⚠️  Missing backup directory: $rollback_id\n";
                $mismatched_backups++;
            }
        }
    }
    
    if ($mismatched_backups === 0) {
        echo "✅ All backup directories exist for fix history\n";
    } else {
        echo "❌ $mismatched_backups backup directories missing\n";
    }
} else {
    echo "❌ Backup directory does not exist\n";
}

// Step 5: Force refresh Recent Fixes data
echo "\n5. FORCING RECENT FIXES REFRESH:\n";

// Clear any cached recent fixes data
delete_transient('redco_recent_fixes_cache');
delete_option('redco_recent_fixes_cache');

// Force regenerate recent fixes HTML
try {
    $diagnostic = new Redco_Diagnostic_AutoFix();
    $reflection = new ReflectionClass($diagnostic);
    
    // Get recent fixes data
    $get_recent_fixes_method = $reflection->getMethod('get_recent_fixes_data');
    $get_recent_fixes_method->setAccessible(true);
    $recent_fixes_data = $get_recent_fixes_method->invoke($diagnostic);
    
    echo "Recent fixes data generated:\n";
    echo "  - HTML length: " . strlen($recent_fixes_data['html'] ?? '') . " characters\n";
    echo "  - Count: " . ($recent_fixes_data['count'] ?? 0) . "\n";
    
    if (!empty($recent_fixes_data['html'])) {
        $fix_items = substr_count($recent_fixes_data['html'], 'class="fix-item"');
        echo "  - Fix items: $fix_items\n";
        
        if ($fix_items > 0) {
            echo "✅ Recent fixes data generated successfully\n";
        } else {
            echo "❌ No fix items in generated data\n";
        }
    } else {
        echo "❌ No HTML generated\n";
    }
    
} catch (Exception $e) {
    echo "❌ Failed to generate recent fixes data: " . $e->getMessage() . "\n";
}

// Step 6: Provide browser cache clearing instructions
echo "\n6. BROWSER CACHE CLEARING INSTRUCTIONS:\n";
echo "If Recent Fixes still appear empty in the browser:\n";
echo "1. Hard refresh the page (Ctrl+F5 or Cmd+Shift+R)\n";
echo "2. Clear browser cache and cookies for this site\n";
echo "3. Open browser developer tools and check for JavaScript errors\n";
echo "4. Try opening the page in an incognito/private window\n";

// Summary
echo "\n=== SUMMARY ===\n";
$fix_history = get_option('redco_diagnostic_fix_history', array());
$has_sessions = !empty($fix_history);
$backup_dir_exists = is_dir($backup_dir ?? '');

if ($has_sessions && $backup_dir_exists) {
    echo "✅ BACKEND SYSTEMS WORKING CORRECTLY\n";
    echo "- Fix sessions are recorded: " . count($fix_history) . " sessions\n";
    echo "- Backup directories exist: YES\n";
    echo "- AJAX endpoint functional: YES\n";
    echo "\nIf Recent Fixes still appear empty, the issue is likely:\n";
    echo "- Browser caching\n";
    echo "- JavaScript errors in browser console\n";
    echo "- CSS hiding the Recent Fixes section\n";
} else {
    echo "❌ BACKEND ISSUES DETECTED\n";
    if (!$has_sessions) {
        echo "- No fix sessions recorded\n";
    }
    if (!$backup_dir_exists) {
        echo "- Backup directory missing\n";
    }
}

echo "\n=== FIX COMPLETE ===\n";
