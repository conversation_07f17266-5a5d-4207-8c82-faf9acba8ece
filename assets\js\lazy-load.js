/**
 * Lazy Load JavaScript for Redco Optimizer
 * 
 * Implements intersection observer-based lazy loading for images.
 */

(function() {
    'use strict';
    
    // Configuration
    const config = {
        threshold: window.redcoLazyLoad ? window.redcoLazyLoad.threshold : 200,
        placeholder: window.redcoLazyLoad ? window.redcoLazyLoad.placeholder : 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1 1"%3E%3C/svg%3E'
    };
    
    // Check for Intersection Observer support
    const supportsIntersectionObserver = 'IntersectionObserver' in window;
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    /**
     * Initialize lazy loading
     */
    function init() {
        const lazyImages = document.querySelectorAll('.redco-lazy[data-src]');
        
        if (lazyImages.length === 0) {
            return;
        }
        
        if (supportsIntersectionObserver) {
            initIntersectionObserver(lazyImages);
        } else {
            // Fallback for older browsers
            initScrollListener(lazyImages);
        }
    }
    
    /**
     * Initialize Intersection Observer (modern browsers)
     */
    function initIntersectionObserver(lazyImages) {
        const imageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    loadImage(img);
                    observer.unobserve(img);
                }
            });
        }, {
            rootMargin: config.threshold + 'px'
        });
        
        lazyImages.forEach(function(img) {
            imageObserver.observe(img);
        });
    }
    
    /**
     * Initialize scroll listener (fallback for older browsers)
     */
    function initScrollListener(lazyImages) {
        let lazyImageArray = Array.from(lazyImages);
        
        function checkImages() {
            lazyImageArray = lazyImageArray.filter(function(img) {
                if (isInViewport(img)) {
                    loadImage(img);
                    return false; // Remove from array
                }
                return true; // Keep in array
            });
            
            // Remove event listeners when all images are loaded
            if (lazyImageArray.length === 0) {
                removeEventListeners();
            }
        }
        
        function throttle(func, delay) {
            let timeoutId;
            let lastExecTime = 0;
            return function() {
                const currentTime = Date.now();
                
                if (currentTime - lastExecTime > delay) {
                    func.apply(this, arguments);
                    lastExecTime = currentTime;
                } else {
                    clearTimeout(timeoutId);
                    timeoutId = setTimeout(function() {
                        func.apply(this, arguments);
                        lastExecTime = Date.now();
                    }.bind(this), delay - (currentTime - lastExecTime));
                }
            };
        }
        
        const throttledCheckImages = throttle(checkImages, 100);
        
        // Add event listeners
        window.addEventListener('scroll', throttledCheckImages);
        window.addEventListener('resize', throttledCheckImages);
        
        // Check images on initial load
        checkImages();
        
        function removeEventListeners() {
            window.removeEventListener('scroll', throttledCheckImages);
            window.removeEventListener('resize', throttledCheckImages);
        }
    }
    
    /**
     * Check if element is in viewport
     */
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        const windowHeight = window.innerHeight || document.documentElement.clientHeight;
        const windowWidth = window.innerWidth || document.documentElement.clientWidth;
        
        return (
            rect.top <= windowHeight + config.threshold &&
            rect.bottom >= -config.threshold &&
            rect.left <= windowWidth &&
            rect.right >= 0
        );
    }
    
    /**
     * Load image
     */
    function loadImage(img) {
        // Add loading class
        img.classList.add('redco-lazy-loading');
        
        // Create new image to preload
        const imageLoader = new Image();
        
        imageLoader.onload = function() {
            // Set the actual source
            img.src = img.dataset.src;
            
            // Handle srcset if present
            if (img.dataset.srcset) {
                img.srcset = img.dataset.srcset;
            }
            
            // Remove data attributes
            delete img.dataset.src;
            delete img.dataset.srcset;
            
            // Update classes
            img.classList.remove('redco-lazy-loading');
            img.classList.add('loaded');
            
            // Remove lazy class after transition
            setTimeout(function() {
                img.classList.remove('redco-lazy');
            }, 300);
            
            // Trigger custom event
            const event = new CustomEvent('redcoLazyLoaded', {
                detail: { img: img }
            });
            document.dispatchEvent(event);
        };
        
        imageLoader.onerror = function() {
            // Handle error - remove loading class and show broken image
            img.classList.remove('redco-lazy-loading');
            img.classList.add('redco-lazy-error');
            
            // Set a fallback image or keep placeholder
            img.alt = img.alt || 'Image failed to load';
            
            // Trigger custom event
            const event = new CustomEvent('redcoLazyError', {
                detail: { img: img }
            });
            document.dispatchEvent(event);
        };
        
        // Start loading
        imageLoader.src = img.dataset.src;
    }
    
    /**
     * Public API for manually triggering lazy load
     */
    window.redcoLazyLoad = window.redcoLazyLoad || {};
    window.redcoLazyLoad.loadAll = function() {
        const lazyImages = document.querySelectorAll('.redco-lazy[data-src]');
        lazyImages.forEach(loadImage);
    };
    
    window.redcoLazyLoad.refresh = function() {
        // Re-initialize for dynamically added images
        setTimeout(init, 100);
    };
    
    // Auto-refresh when new content is added (for AJAX-loaded content)
    const observer = new MutationObserver(function(mutations) {
        let shouldRefresh = false;
        
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        if (node.classList && node.classList.contains('redco-lazy')) {
                            shouldRefresh = true;
                        } else if (node.querySelector && node.querySelector('.redco-lazy[data-src]')) {
                            shouldRefresh = true;
                        }
                    }
                });
            }
        });
        
        if (shouldRefresh) {
            window.redcoLazyLoad.refresh();
        }
    });
    
    // Start observing
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
})();
