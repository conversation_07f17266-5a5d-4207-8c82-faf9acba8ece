/**
 * Redco Optimizer Help System
 *
 * Handles the contextual help system with sliding panel and tooltips
 */

(function($) {
    'use strict';

    let helpPanel = null;
    let helpOverlay = null;
    let currentModule = '';
    let helpData = {};

    /**
     * Initialize the help system
     */
    function initHelpSystem() {
        helpPanel = $('#redco-help-panel');
        helpOverlay = $('#redco-help-overlay');

        // Bind events
        bindHelpEvents();

        // Initialize tooltips
        initTooltips();

        // Get current module from URL or page
        currentModule = getCurrentModule();
    }

    /**
     * Bind help system events
     */
    function bindHelpEvents() {
        // Help icon clicks
        $(document).on('click', '.redco-help-icon', function(e) {
            e.preventDefault();
            const $icon = $(this);
            const module = $icon.data('module');
            const section = $icon.data('section');
            const field = $icon.data('field');

            console.log('Help icon clicked:', { module, section, field });
            showHelpPanel(module, section, field);
        });

        // Global help button clicks (header, sidebar, and enhanced)
        $(document).on('click', '.redco-help-trigger, .sidebar-help-trigger, .enhanced-help-trigger', function(e) {
            e.preventDefault();
            const $button = $(this);
            const module = $button.data('module') || currentModule;
            const section = $button.data('section') || '';

            showHelpPanel(module, section, '');
        });

        // Close help panel
        $(document).on('click', '.help-panel-close, #redco-help-overlay', function() {
            hideHelpPanel();
        });

        // Search functionality
        $(document).on('input', '#help-search', function() {
            const query = $(this).val().toLowerCase();
            filterHelpContent(query);
        });

        // Escape key to close panel
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27 && helpPanel.hasClass('active')) {
                hideHelpPanel();
            }
        });

        // Prevent panel close when clicking inside
        $(document).on('click', '.redco-help-panel', function(e) {
            e.stopPropagation();
        });
    }

    /**
     * Initialize tooltips for help icons
     */
    function initTooltips() {
        $(document).on('mouseenter', '.redco-help-icon[data-tooltip]', function() {
            const $icon = $(this);
            const tooltip = $icon.data('tooltip');

            if (tooltip) {
                showTooltip($icon, tooltip);
            }
        });

        $(document).on('mouseleave', '.redco-help-icon[data-tooltip]', function() {
            hideTooltip();
        });
    }

    /**
     * Show tooltip
     */
    function showTooltip($element, text) {
        const $tooltip = $('<div class="redco-tooltip">' + text + '</div>');
        $('body').append($tooltip);

        const offset = $element.offset();
        const elementHeight = $element.outerHeight();

        $tooltip.css({
            top: offset.top + elementHeight + 5,
            left: offset.left - ($tooltip.outerWidth() / 2) + ($element.outerWidth() / 2)
        });

        setTimeout(() => $tooltip.addClass('visible'), 10);
    }

    /**
     * Hide tooltip
     */
    function hideTooltip() {
        $('.redco-tooltip').remove();
    }

    /**
     * Get current module from page context
     */
    function getCurrentModule() {
        const urlParams = new URLSearchParams(window.location.search);
        const tab = urlParams.get('tab');

        if (tab) {
            return tab;
        }

        // Try to get from module settings container
        const $moduleContainer = $('.redco-module-settings');
        if ($moduleContainer.length) {
            return $moduleContainer.data('module');
        }

        return 'dashboard';
    }

    /**
     * Show help panel with content
     */
    function showHelpPanel(module, section, field) {
        if (!helpPanel.length) {
            console.error('Help panel not found');
            return;
        }

        // Show loading state
        showHelpLoading();

        // Show panel and overlay
        helpPanel.addClass('active');
        helpOverlay.addClass('active');
        $('body').addClass('help-panel-open');

        // Load help content
        loadHelpContent(module, section, field);
    }

    /**
     * Hide help panel
     */
    function hideHelpPanel() {
        helpPanel.removeClass('active');
        helpOverlay.removeClass('active');
        $('body').removeClass('help-panel-open');

        // Clear search
        $('#help-search').val('');
    }

    /**
     * Show loading state in help panel
     */
    function showHelpLoading() {
        const $content = helpPanel.find('.help-panel-content');
        $content.html(`
            <div class="help-loading">
                <span class="dashicons dashicons-update-alt spinning"></span>
                ${redcoHelp.strings.loading}
            </div>
        `);
    }

    /**
     * Load help content via AJAX
     */
    function loadHelpContent(module, section, field) {

        $.ajax({
            url: redcoHelp.ajaxurl,
            type: 'POST',
            data: {
                action: 'redco_get_help_content',
                module: module,
                section: section,
                field: field,
                nonce: redcoHelp.nonce
            },
            success: function(response) {

                if (response.success) {
                    const $content = helpPanel.find('.help-panel-content');
                    const $header = helpPanel.find('.help-panel-header h3');

                    // Update header title if provided
                    if (response.data.title) {
                        $header.text(response.data.title);
                    }

                    // CRITICAL SECURITY FIX: Safely render content to prevent XSS
                    if (response.data.content) {
                        // Use wp_kses filtered content from server (already sanitized)
                        // Only allow if content comes from trusted admin source
                        if (response.data.is_admin_content && response.data.content_sanitized) {
                            $content.html(response.data.content);
                        } else {
                            // For untrusted content, use text only
                            $content.text(response.data.content);
                        }
                    }
                    helpData = response.data;
                } else {

                    showHelpError(response.data || redcoHelp.strings.error);
                }
            },
            error: function() {
                showHelpError(redcoHelp.strings.error);
            }
        });
    }

    /**
     * Render help content in panel
     */
    function renderHelpContent(data, section, field) {
        const $content = helpPanel.find('.help-panel-content');
        let html = '';

        // Module overview
        if (data.overview) {
            html += `
                <div class="help-section help-overview">
                    <h4>${data.title}</h4>
                    <p>${data.overview}</p>
                </div>
            `;
        }

        // Specific section content
        if (section && data.sections && data.sections[section]) {
            const sectionData = data.sections[section];
            html += renderHelpSection(sectionData, section, field);
        } else if (data.sections) {
            // All sections
            html += '<div class="help-sections">';
            for (const [sectionKey, sectionData] of Object.entries(data.sections)) {
                html += renderHelpSection(sectionData, sectionKey);
            }
            html += '</div>';
        }

        $content.html(html);
    }

    /**
     * Render individual help section
     */
    function renderHelpSection(sectionData, sectionKey, field) {
        let html = `
            <div class="help-section" data-section="${sectionKey}">
                <h4>${sectionData.title}</h4>
                <p>${sectionData.content}</p>
        `;

        // Field-specific help
        if (field && sectionData.fields && sectionData.fields[field]) {
            const fieldData = sectionData.fields[field];
            html += `
                <div class="help-field-specific">
                    <h5>${fieldData.title}</h5>
                    <p>${fieldData.help}</p>
                    ${renderHelpList(fieldData.recommendations, 'Recommendations')}
                </div>
            `;
        }

        // Section recommendations, warnings, etc.
        html += renderHelpList(sectionData.recommendations, 'Recommendations');
        html += renderHelpList(sectionData.warnings, 'Important Notes', 'warning');
        html += renderHelpList(sectionData.safety, 'Safety Tips', 'safety');
        html += renderHelpList(sectionData.benefits, 'Benefits', 'benefits');

        // Metrics explanation
        if (sectionData.metrics) {
            html += '<div class="help-metrics"><h5>Metrics Explained</h5><ul>';
            for (const [metric, explanation] of Object.entries(sectionData.metrics)) {
                html += `<li><strong>${metric}:</strong> ${explanation}</li>`;
            }
            html += '</ul></div>';
        }

        html += '</div>';
        return html;
    }

    /**
     * Render help list (recommendations, warnings, etc.)
     */
    function renderHelpList(items, title, type = 'info') {
        if (!items || !items.length) {
            return '';
        }

        let html = `<div class="help-list help-${type}"><h5>${title}</h5><ul>`;
        items.forEach(item => {
            html += `<li>${item}</li>`;
        });
        html += '</ul></div>';

        return html;
    }

    /**
     * Show error in help panel
     */
    function showHelpError(message) {
        const $content = helpPanel.find('.help-panel-content');
        $content.html(`
            <div class="help-error">
                <span class="dashicons dashicons-warning"></span>
                <p>${message}</p>
            </div>
        `);
    }

    /**
     * Filter help content based on search query
     */
    function filterHelpContent(query) {
        const $sections = helpPanel.find('.help-section');

        if (!query) {
            $sections.show();
            return;
        }

        let hasResults = false;

        $sections.each(function() {
            const $section = $(this);
            const text = $section.text().toLowerCase();

            if (text.includes(query)) {
                $section.show();
                hasResults = true;
            } else {
                $section.hide();
            }
        });

        // Show no results message
        if (!hasResults) {
            if (!helpPanel.find('.help-no-results').length) {
                helpPanel.find('.help-panel-content').append(`
                    <div class="help-no-results">
                        <p>${redcoHelp.strings.no_results}</p>
                    </div>
                `);
            }
        } else {
            helpPanel.find('.help-no-results').remove();
        }
    }

    // Initialize when document is ready
    $(document).ready(function() {
        initHelpSystem();
    });

})(jQuery);
