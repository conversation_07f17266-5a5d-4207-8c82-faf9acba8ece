<?php
/**
 * Standalone Diagnostic Helpers for Redco Diagnostic & Auto-Fix
 * 
 * Contains all diagnostic scanning methods (standalone version)
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Standalone_Diagnostic_Helpers {
    
    private $settings;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->settings = get_option('redco_diagnostic_settings', array());
    }
    
    /**
     * Scan WordPress core issues
     */
    public function scan_wordpress_core_issues() {
        $issues = array();
        
        // Check WordPress version
        $wp_version = redco_diagnostic_get_wp_version();
        $latest_version = $this->get_latest_wp_version();
        
        if (version_compare($wp_version, $latest_version, '<')) {
            $issues[] = array(
                'id' => 'outdated_wordpress',
                'title' => __('Outdated WordPress Version', 'redco-diagnostic-autofix'),
                'description' => sprintf(__('WordPress %s is available. You are running %s.', 'redco-diagnostic-autofix'), $latest_version, $wp_version),
                'severity' => 'high',
                'category' => 'wordpress',
                'auto_fixable' => false,
                'impact' => 'Security vulnerabilities and missing features',
                'recommendation' => 'Update WordPress to the latest version',
                'fix_action' => 'update_wordpress'
            );
        }
        
        // Check for debug mode in production
        if (defined('WP_DEBUG') && WP_DEBUG && !redco_diagnostic_is_development_environment()) {
            $issues[] = array(
                'id' => 'debug_enabled_production',
                'title' => __('Debug Mode Enabled in Production', 'redco-diagnostic-autofix'),
                'description' => __('WP_DEBUG is enabled on a production site.', 'redco-diagnostic-autofix'),
                'severity' => 'high',
                'category' => 'wordpress',
                'auto_fixable' => true,
                'impact' => 'Performance overhead and security risks',
                'recommendation' => 'Disable WP_DEBUG in production',
                'fix_action' => 'disable_debug_mode'
            );
        }
        
        // Check for default admin user
        $admin_user = get_user_by('login', 'admin');
        if ($admin_user) {
            $issues[] = array(
                'id' => 'default_admin_user',
                'title' => __('Default Admin Username', 'redco-diagnostic-autofix'),
                'description' => __('The default "admin" username is still active.', 'redco-diagnostic-autofix'),
                'severity' => 'medium',
                'category' => 'security',
                'auto_fixable' => false,
                'impact' => 'Increased vulnerability to brute force attacks',
                'recommendation' => 'Change the admin username to something unique',
                'fix_action' => 'change_admin_username'
            );
        }
        
        // Check file permissions
        $issues = array_merge($issues, $this->check_file_permissions());
        
        return $issues;
    }
    
    /**
     * Scan basic performance issues
     */
    public function scan_basic_performance_issues() {
        $issues = array();
        
        // Check if caching is enabled
        if (!$this->is_caching_enabled()) {
            $issues[] = array(
                'id' => 'no_caching',
                'title' => __('No Caching Enabled', 'redco-diagnostic-autofix'),
                'description' => __('No caching mechanism is detected.', 'redco-diagnostic-autofix'),
                'severity' => 'high',
                'category' => 'performance',
                'auto_fixable' => true,
                'impact' => 'Slower page load times and increased server load',
                'recommendation' => 'Enable page caching',
                'fix_action' => 'enable_caching'
            );
        }
        
        // Check compression (FIXED: Use comprehensive detection)
        if (!$this->is_compression_enabled_comprehensive()) {
            $issues[] = array(
                'id' => 'no_compression',
                'title' => __('GZIP Compression Disabled', 'redco-diagnostic-autofix'),
                'description' => __('GZIP compression is not enabled.', 'redco-diagnostic-autofix'),
                'severity' => 'medium',
                'category' => 'performance',
                'auto_fixable' => true,
                'impact' => 'Larger file sizes and slower loading',
                'recommendation' => 'Enable GZIP compression',
                'fix_action' => 'enable_gzip_compression'
            );
        }
        
        // Check database size
        $db_size = redco_diagnostic_get_database_size();
        if ($db_size > 100) { // 100MB
            $issues[] = array(
                'id' => 'large_database',
                'title' => __('Large Database Size', 'redco-diagnostic-autofix'),
                'description' => sprintf(__('Database size is %s MB. Consider cleanup.', 'redco-diagnostic-autofix'), $db_size),
                'severity' => 'medium',
                'category' => 'database',
                'auto_fixable' => true,
                'impact' => 'Slower database queries and increased backup time',
                'recommendation' => 'Clean up unnecessary data',
                'fix_action' => 'cleanup_database'
            );
        }
        
        return $issues;
    }
    
    /**
     * Scan basic security issues
     */
    public function scan_basic_security_issues() {
        $issues = array();
        
        // Check SSL
        if (!redco_diagnostic_is_ssl_enabled()) {
            $issues[] = array(
                'id' => 'no_ssl',
                'title' => __('SSL Not Enabled', 'redco-diagnostic-autofix'),
                'description' => __('Your site is not using HTTPS.', 'redco-diagnostic-autofix'),
                'severity' => 'high',
                'category' => 'security',
                'auto_fixable' => false,
                'impact' => 'Data transmitted in plain text, SEO penalties',
                'recommendation' => 'Install and configure SSL certificate',
                'fix_action' => 'enable_ssl'
            );
        }
        
        // Check for exposed wp-config.php
        if ($this->is_wp_config_exposed()) {
            $issues[] = array(
                'id' => 'exposed_wp_config',
                'title' => __('wp-config.php Exposed', 'redco-diagnostic-autofix'),
                'description' => __('wp-config.php file may be accessible via web.', 'redco-diagnostic-autofix'),
                'severity' => 'critical',
                'category' => 'security',
                'auto_fixable' => true,
                'impact' => 'Database credentials and security keys exposed',
                'recommendation' => 'Protect wp-config.php file',
                'fix_action' => 'protect_wp_config'
            );
        }
        
        return $issues;
    }
    
    /**
     * Scan performance issues (comprehensive)
     */
    public function scan_performance_issues() {
        $issues = array();
        
        // Check for render-blocking resources
        $blocking_resources = $this->detect_render_blocking_resources();
        if (!empty($blocking_resources)) {
            $issues[] = array(
                'id' => 'render_blocking_resources',
                'title' => __('Render-Blocking Resources', 'redco-diagnostic-autofix'),
                'description' => sprintf(__('%d render-blocking resources detected.', 'redco-diagnostic-autofix'), count($blocking_resources)),
                'severity' => 'medium',
                'category' => 'performance',
                'auto_fixable' => true,
                'impact' => 'Delayed page rendering and slower perceived load times',
                'recommendation' => 'Defer or inline critical CSS/JS',
                'fix_action' => 'optimize_render_blocking'
            );
        }
        
        // Check image optimization
        if (!$this->is_image_optimization_enabled()) {
            $issues[] = array(
                'id' => 'unoptimized_images',
                'title' => __('Images Not Optimized', 'redco-diagnostic-autofix'),
                'description' => __('Image optimization is not enabled.', 'redco-diagnostic-autofix'),
                'severity' => 'medium',
                'category' => 'performance',
                'auto_fixable' => true,
                'impact' => 'Larger image files and slower loading',
                'recommendation' => 'Enable image optimization and lazy loading',
                'fix_action' => 'optimize_images'
            );
        }
        
        return $issues;
    }
    
    /**
     * Scan database performance
     */
    public function scan_database_performance() {
        $issues = array();
        
        // Check autoload size
        $autoload_size = redco_diagnostic_get_autoload_size();
        if ($autoload_size > 1024 * 1024) { // 1MB
            $issues[] = array(
                'id' => 'large_autoload',
                'title' => __('Large Autoload Data', 'redco-diagnostic-autofix'),
                'description' => sprintf(__('Autoload data is %s. Consider cleanup.', 'redco-diagnostic-autofix'), redco_diagnostic_format_bytes($autoload_size)),
                'severity' => 'medium',
                'category' => 'database',
                'auto_fixable' => true,
                'impact' => 'Slower page load times due to large autoload',
                'recommendation' => 'Clean up autoload data',
                'fix_action' => 'cleanup_autoload'
            );
        }
        
        // Check for unused plugins
        $inactive_plugins = $this->get_inactive_plugins();
        if (!empty($inactive_plugins)) {
            $issues[] = array(
                'id' => 'inactive_plugins',
                'title' => __('Inactive Plugins Installed', 'redco-diagnostic-autofix'),
                'description' => sprintf(__('%d inactive plugins are installed.', 'redco-diagnostic-autofix'), count($inactive_plugins)),
                'severity' => 'low',
                'category' => 'cleanup',
                'auto_fixable' => false,
                'impact' => 'Unnecessary files and potential security risks',
                'recommendation' => 'Remove unused plugins',
                'fix_action' => 'remove_inactive_plugins'
            );
        }
        
        return $issues;
    }
    
    /**
     * Scan caching issues
     */
    public function scan_caching_issues() {
        $issues = array();
        
        // Check object cache
        if (!redco_diagnostic_is_object_cache_enabled()) {
            $issues[] = array(
                'id' => 'no_object_cache',
                'title' => __('Object Cache Not Enabled', 'redco-diagnostic-autofix'),
                'description' => __('Object caching is not enabled.', 'redco-diagnostic-autofix'),
                'severity' => 'medium',
                'category' => 'caching',
                'auto_fixable' => false,
                'impact' => 'Repeated database queries for the same data',
                'recommendation' => 'Enable object caching (Redis/Memcached)',
                'fix_action' => 'enable_object_cache'
            );
        }
        
        return $issues;
    }
    
    /**
     * Scan image optimization
     */
    public function scan_image_optimization() {
        $issues = array();
        
        // Check for WebP support
        if (!$this->is_webp_supported()) {
            $issues[] = array(
                'id' => 'no_webp_support',
                'title' => __('WebP Images Not Supported', 'redco-diagnostic-autofix'),
                'description' => __('WebP image format is not enabled.', 'redco-diagnostic-autofix'),
                'severity' => 'low',
                'category' => 'images',
                'auto_fixable' => true,
                'impact' => 'Larger image file sizes',
                'recommendation' => 'Enable WebP image support',
                'fix_action' => 'enable_webp'
            );
        }
        
        return $issues;
    }
    
    /**
     * Scan CSS/JS optimization
     */
    public function scan_css_js_optimization() {
        $issues = array();
        
        // Check minification
        if (!$this->is_minification_enabled()) {
            $issues[] = array(
                'id' => 'no_minification',
                'title' => __('CSS/JS Not Minified', 'redco-diagnostic-autofix'),
                'description' => __('CSS and JavaScript files are not minified.', 'redco-diagnostic-autofix'),
                'severity' => 'medium',
                'category' => 'performance',
                'auto_fixable' => true,
                'impact' => 'Larger file sizes and slower loading',
                'recommendation' => 'Enable CSS/JS minification',
                'fix_action' => 'enable_minification'
            );
        }
        
        return $issues;
    }
    
    /**
     * Scan security issues (comprehensive)
     */
    public function scan_security_issues() {
        $issues = array();
        
        // Check for security headers
        $missing_headers = $this->check_security_headers();
        if (!empty($missing_headers)) {
            $issues[] = array(
                'id' => 'missing_security_headers',
                'title' => __('Missing Security Headers', 'redco-diagnostic-autofix'),
                'description' => sprintf(__('%d security headers are missing.', 'redco-diagnostic-autofix'), count($missing_headers)),
                'severity' => 'medium',
                'category' => 'security',
                'auto_fixable' => true,
                'impact' => 'Increased vulnerability to various attacks',
                'recommendation' => 'Add security headers',
                'fix_action' => 'add_security_headers'
            );
        }
        
        return $issues;
    }
    
    // Add placeholder methods for missing scan types
    public function scan_seo_issues() { return array(); }
    public function scan_accessibility_issues() { return array(); }
    public function scan_plugin_conflicts() { return array(); }
    public function scan_file_permissions() { return array(); }
    public function scan_wp_config_security() { return array(); }
    public function scan_plugin_vulnerabilities() { return array(); }
    
    /**
     * Helper methods
     */
    private function get_latest_wp_version() {
        $version_check = wp_remote_get('https://api.wordpress.org/core/version-check/1.7/');
        if (!is_wp_error($version_check)) {
            $version_data = json_decode(wp_remote_retrieve_body($version_check), true);
            if (isset($version_data['offers'][0]['version'])) {
                return $version_data['offers'][0]['version'];
            }
        }
        return get_bloginfo('version'); // Fallback to current version
    }
    
    private function is_caching_enabled() {
        return defined('WP_CACHE') && WP_CACHE;
    }
    
    /**
     * COMPREHENSIVE COMPRESSION DETECTION (SYNCHRONIZED WITH AUTO-FIX ENGINE)
     *
     * This method uses the same logic as the auto-fix engine to prevent contradictions
     */
    private function is_compression_enabled_comprehensive() {
        // Get the auto-fix engine instance to use its comprehensive detection
        if (!class_exists('Redco_Diagnostic_AutoFix_Engine')) {
            // Fallback to legacy detection if engine not available
            return $this->is_compression_enabled();
        }

        $engine = new Redco_Diagnostic_AutoFix_Engine();
        return $engine->is_compression_enabled_comprehensive();
    }

    /**
     * LEGACY METHOD: Basic compression detection
     */
    private function is_compression_enabled() {
        $response = wp_remote_get(home_url(), array('timeout' => 10));
        if (!is_wp_error($response)) {
            $headers = wp_remote_retrieve_headers($response);
            return isset($headers['content-encoding']) &&
                   (strpos($headers['content-encoding'], 'gzip') !== false ||
                    strpos($headers['content-encoding'], 'deflate') !== false);
        }
        return false;
    }
    
    private function is_image_optimization_enabled() {
        $image_plugins = array(
            'wp-smushit/wp-smush.php',
            'shortpixel-image-optimiser/wp-shortpixel.php',
            'ewww-image-optimizer/ewww-image-optimizer.php'
        );
        
        foreach ($image_plugins as $plugin) {
            if (is_plugin_active($plugin)) {
                return true;
            }
        }
        
        return false;
    }
    
    private function is_minification_enabled() {
        $minification_plugins = array(
            'autoptimize/autoptimize.php',
            'w3-total-cache/w3-total-cache.php',
            'wp-rocket/wp-rocket.php'
        );
        
        foreach ($minification_plugins as $plugin) {
            if (is_plugin_active($plugin)) {
                return true;
            }
        }
        
        return false;
    }
    
    private function is_webp_supported() {
        return function_exists('imagewebp');
    }
    
    private function check_file_permissions() {
        $issues = array();
        
        $wp_config = ABSPATH . 'wp-config.php';
        if (file_exists($wp_config)) {
            $perms = fileperms($wp_config) & 0777;
            if ($perms > 0644) {
                $issues[] = array(
                    'id' => 'wp_config_permissions',
                    'title' => __('wp-config.php Permissions Too Open', 'redco-diagnostic-autofix'),
                    'description' => sprintf(__('wp-config.php has permissions %o. Should be 644 or 600.', 'redco-diagnostic-autofix'), $perms),
                    'severity' => 'high',
                    'category' => 'security',
                    'auto_fixable' => true,
                    'impact' => 'Configuration file may be readable by others',
                    'recommendation' => 'Set wp-config.php permissions to 644',
                    'fix_action' => 'fix_wp_config_permissions'
                );
            }
        }
        
        return $issues;
    }
    
    private function is_wp_config_exposed() {
        $wp_config_url = home_url('/wp-config.php');
        $response = wp_remote_get($wp_config_url, array('timeout' => 10));
        
        if (!is_wp_error($response)) {
            $response_code = wp_remote_retrieve_response_code($response);
            return $response_code === 200;
        }
        
        return false;
    }
    
    private function detect_render_blocking_resources() {
        $blocking_resources = array();
        
        $homepage = wp_remote_get(home_url(), array('timeout' => 10));
        if (!is_wp_error($homepage)) {
            $content = wp_remote_retrieve_body($homepage);
            
            preg_match_all('/<link[^>]*rel=["\']stylesheet["\'][^>]*>/i', $content, $css_matches);
            if (count($css_matches[0]) > 3) {
                $blocking_resources[] = 'css_files';
            }
            
            preg_match_all('/<script[^>]*src=[^>]*><\/script>/i', $content, $js_matches);
            if (count($js_matches[0]) > 2) {
                $blocking_resources[] = 'js_files';
            }
        }
        
        return $blocking_resources;
    }
    
    private function get_inactive_plugins() {
        $all_plugins = get_plugins();
        $active_plugins = get_option('active_plugins', array());
        
        $inactive_plugins = array();
        foreach ($all_plugins as $plugin_file => $plugin_data) {
            if (!in_array($plugin_file, $active_plugins)) {
                $inactive_plugins[] = $plugin_file;
            }
        }
        
        return $inactive_plugins;
    }
    
    private function check_security_headers() {
        $missing_headers = array();
        
        $response = wp_remote_get(home_url(), array('timeout' => 10));
        if (!is_wp_error($response)) {
            $headers = wp_remote_retrieve_headers($response);
            
            $security_headers = array(
                'X-Content-Type-Options',
                'X-Frame-Options',
                'X-XSS-Protection',
                'Strict-Transport-Security'
            );
            
            foreach ($security_headers as $header) {
                if (!isset($headers[strtolower($header)])) {
                    $missing_headers[] = $header;
                }
            }
        }
        
        return $missing_headers;
    }
}
