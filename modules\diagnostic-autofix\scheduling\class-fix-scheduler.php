<?php
/**
 * Fix Scheduling & Preview System for Redco Optimizer
 * 
 * Allows users to schedule fixes and preview their effects before application
 * Integrated with existing diagnostic system
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Fix_Scheduler {
    
    private $schedule_types = array(
        'immediate' => 'Apply immediately',
        'maintenance_window' => 'During maintenance window',
        'low_traffic' => 'During low traffic period',
        'custom_time' => 'Custom date/time'
    );
    
    /**
     * Initialize the scheduler
     */
    public function init() {
        // Add custom cron intervals
        add_filter('cron_schedules', array($this, 'add_cron_intervals'));
        
        // AJAX handlers
        add_action('wp_ajax_redco_schedule_fix', array($this, 'ajax_schedule_fix'));
        add_action('wp_ajax_redco_preview_fix', array($this, 'ajax_preview_fix'));
        add_action('wp_ajax_redco_get_scheduled_fixes', array($this, 'ajax_get_scheduled_fixes'));
        add_action('wp_ajax_redco_cancel_scheduled_fix', array($this, 'ajax_cancel_scheduled_fix'));
        
        // Cron handlers
        add_action('redco_execute_scheduled_fix', array($this, 'execute_scheduled_fix'));
        
        // Ensure database table exists
        $this->maybe_create_tables();
    }
    
    /**
     * Add custom cron intervals
     */
    public function add_cron_intervals($schedules) {
        $schedules['redco_5min'] = array(
            'interval' => 300,
            'display' => __('Every 5 Minutes', 'redco-optimizer')
        );
        
        $schedules['redco_15min'] = array(
            'interval' => 900,
            'display' => __('Every 15 Minutes', 'redco-optimizer')
        );
        
        return $schedules;
    }
    
    /**
     * AJAX: Schedule a fix for future application
     */
    public function ajax_schedule_fix() {
        check_ajax_referer('redco_diagnostic_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $fix_id = sanitize_text_field($_POST['fix_id'] ?? '');
        $schedule_type = sanitize_text_field($_POST['schedule_type'] ?? 'immediate');
        $custom_datetime = sanitize_text_field($_POST['custom_datetime'] ?? '');
        $notifications = array(
            'email' => sanitize_email($_POST['notification_email'] ?? ''),
            'enabled' => !empty($_POST['email_notifications'])
        );
        
        if (empty($fix_id)) {
            wp_send_json_error('Fix ID is required');
            return;
        }
        
        $schedule_config = array(
            'type' => $schedule_type,
            'custom_datetime' => $custom_datetime,
            'notifications' => $notifications
        );
        
        $result = $this->schedule_fix($fix_id, $schedule_config);
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success(array(
                'scheduled_fix_id' => $result,
                'message' => 'Fix scheduled successfully'
            ));
        }
    }
    
    /**
     * Schedule a fix for future application
     */
    public function schedule_fix($fix_id, $schedule_config) {
        // Get fix details from existing diagnostic system
        $diagnostic_autofix = new Redco_Diagnostic_AutoFix();
        $all_issues = get_option('redco_diagnostic_results', array());
        
        $fix_details = null;
        if (isset($all_issues['issues'])) {
            foreach ($all_issues['issues'] as $issue) {
                if ($issue['id'] === $fix_id) {
                    $fix_details = $issue;
                    break;
                }
            }
        }
        
        if (!$fix_details) {
            return new WP_Error('invalid_fix', 'Fix not found');
        }
        
        // Validate schedule configuration
        $validation = $this->validate_schedule_config($schedule_config);
        if (is_wp_error($validation)) {
            return $validation;
        }
        
        // Create scheduled fix entry
        $scheduled_fix = array(
            'id' => wp_generate_uuid4(),
            'fix_id' => $fix_id,
            'fix_details' => $fix_details,
            'schedule_type' => $schedule_config['type'],
            'scheduled_time' => $this->calculate_execution_time($schedule_config),
            'created_by' => get_current_user_id(),
            'created_at' => current_time('mysql'),
            'status' => 'scheduled',
            'config' => $schedule_config,
            'notifications' => $schedule_config['notifications'] ?? array()
        );
        
        // Store in database
        $this->store_scheduled_fix($scheduled_fix);
        
        // Schedule WordPress cron event
        $this->schedule_cron_event($scheduled_fix);
        
        return $scheduled_fix['id'];
    }
    
    /**
     * AJAX: Preview fix effects without applying
     */
    public function ajax_preview_fix() {
        check_ajax_referer('redco_diagnostic_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $fix_id = sanitize_text_field($_POST['fix_id'] ?? '');
        
        if (empty($fix_id)) {
            wp_send_json_error('Fix ID is required');
            return;
        }
        
        $preview_results = $this->preview_fix($fix_id);
        
        if (is_wp_error($preview_results)) {
            wp_send_json_error($preview_results->get_error_message());
        } else {
            wp_send_json_success($preview_results);
        }
    }
    
    /**
     * Preview fix effects without applying
     */
    public function preview_fix($fix_id, $preview_config = array()) {
        // Get fix details from existing diagnostic system
        $all_issues = get_option('redco_diagnostic_results', array());
        
        $fix_details = null;
        if (isset($all_issues['issues'])) {
            foreach ($all_issues['issues'] as $issue) {
                if ($issue['id'] === $fix_id) {
                    $fix_details = $issue;
                    break;
                }
            }
        }
        
        if (!$fix_details) {
            return new WP_Error('invalid_fix', 'Fix not found');
        }
        
        $preview_results = array(
            'fix_id' => $fix_id,
            'fix_details' => $fix_details,
            'preview_timestamp' => time(),
            'estimated_impact' => array(),
            'before_metrics' => array(),
            'simulated_after_metrics' => array(),
            'file_changes' => array(),
            'database_changes' => array(),
            'configuration_changes' => array(),
            'warnings' => array(),
            'recommendations' => array()
        );
        
        // Collect current metrics
        $preview_results['before_metrics'] = $this->collect_current_metrics();
        
        // Simulate fix application based on fix type
        $preview_results = $this->simulate_fix_application($preview_results, $fix_details);
        
        // Calculate estimated improvements
        $preview_results['estimated_impact'] = $this->calculate_estimated_impact($preview_results);
        
        return $preview_results;
    }
    
    /**
     * Simulate fix application for preview
     */
    private function simulate_fix_application($preview_results, $fix_details) {
        $fix_id = $fix_details['id'];
        
        switch ($fix_id) {
            case 'enable_gzip_compression':
                $preview_results = $this->preview_gzip_compression($preview_results);
                break;
            case 'cleanup_transients':
                $preview_results = $this->preview_transient_cleanup($preview_results);
                break;
            case 'optimize_autoload':
                $preview_results = $this->preview_autoload_optimization($preview_results);
                break;
            case 'set_browser_caching':
                $preview_results = $this->preview_browser_caching($preview_results);
                break;
            default:
                $preview_results = $this->preview_generic_fix($preview_results, $fix_details);
                break;
        }
        
        return $preview_results;
    }
    
    /**
     * Preview GZIP compression effects
     */
    private function preview_gzip_compression($preview_results) {
        $preview_results['configuration_changes'][] = array(
            'file' => '.htaccess',
            'change' => 'Add GZIP compression rules',
            'content_preview' => "# Enable GZIP Compression\n<IfModule mod_deflate.c>\n    AddOutputFilterByType DEFLATE text/plain\n    AddOutputFilterByType DEFLATE text/html\n    AddOutputFilterByType DEFLATE text/css\n    AddOutputFilterByType DEFLATE application/javascript\n</IfModule>"
        );
        
        $preview_results['simulated_after_metrics']['bandwidth_savings'] = '60-80%';
        $preview_results['simulated_after_metrics']['load_time_improvement'] = '20-40%';
        
        return $preview_results;
    }
    
    /**
     * Preview transient cleanup effects
     */
    private function preview_transient_cleanup($preview_results) {
        global $wpdb;
        
        // Count expired transients
        $expired_transients = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$wpdb->options} 
             WHERE option_name LIKE '_transient_timeout_%' 
             AND option_value < UNIX_TIMESTAMP()"
        );
        
        $preview_results['database_changes'][] = array(
            'action' => 'Delete expired transients',
            'affected_rows' => intval($expired_transients),
            'estimated_space_saved' => $expired_transients * 1024 // Rough estimate
        );
        
        $preview_results['simulated_after_metrics']['database_size_reduction'] = redco_format_bytes($expired_transients * 1024);
        
        return $preview_results;
    }
    
    /**
     * Calculate execution time based on schedule configuration
     */
    private function calculate_execution_time($schedule_config) {
        switch ($schedule_config['type']) {
            case 'immediate':
                return current_time('mysql');
                
            case 'maintenance_window':
                return $this->get_next_maintenance_window();
                
            case 'low_traffic':
                return $this->get_next_low_traffic_period();
                
            case 'custom_time':
                return date('Y-m-d H:i:s', strtotime($schedule_config['custom_datetime']));
                
            default:
                return current_time('mysql');
        }
    }
    
    /**
     * Get next maintenance window
     */
    private function get_next_maintenance_window() {
        $maintenance_config = get_option('redco_maintenance_windows', array(
            'day' => 'sunday',
            'time' => '02:00',
            'timezone' => get_option('timezone_string', 'UTC')
        ));
        
        $next_sunday = strtotime('next sunday ' . $maintenance_config['time']);
        return date('Y-m-d H:i:s', $next_sunday);
    }
    
    /**
     * Get next low traffic period
     */
    private function get_next_low_traffic_period() {
        $low_traffic_hours = array(2, 3, 4, 5); // 2 AM - 5 AM typically low traffic
        
        $current_hour = intval(current_time('H'));
        $next_low_traffic_hour = null;
        
        foreach ($low_traffic_hours as $hour) {
            if ($hour > $current_hour) {
                $next_low_traffic_hour = $hour;
                break;
            }
        }
        
        if (!$next_low_traffic_hour) {
            $next_low_traffic_hour = $low_traffic_hours[0];
            $next_day = strtotime('+1 day');
            return date('Y-m-d', $next_day) . ' ' . sprintf('%02d:00:00', $next_low_traffic_hour);
        }
        
        return current_time('Y-m-d') . ' ' . sprintf('%02d:00:00', $next_low_traffic_hour);
    }
    
    /**
     * Execute scheduled fix
     */
    public function execute_scheduled_fix($scheduled_fix_id) {
        $scheduled_fix = $this->get_scheduled_fix($scheduled_fix_id);
        
        if (!$scheduled_fix || $scheduled_fix['status'] !== 'scheduled') {
            return;
        }
        
        // Update status to executing
        $this->update_scheduled_fix_status($scheduled_fix_id, 'executing');
        
        try {
            // Use existing diagnostic autofix engine
            $fix_engine = new Redco_Diagnostic_AutoFix_Engine();
            $result = $fix_engine->apply_single_fix($scheduled_fix['fix_id']);
            
            if ($result['success']) {
                $this->update_scheduled_fix_status($scheduled_fix_id, 'completed', $result);
                
                // Send notification if enabled
                if (!empty($scheduled_fix['notifications']['enabled'])) {
                    $this->send_fix_notification($scheduled_fix, 'success', $result);
                }
            } else {
                $this->update_scheduled_fix_status($scheduled_fix_id, 'failed', $result);
                
                // Send failure notification
                if (!empty($scheduled_fix['notifications']['enabled'])) {
                    $this->send_fix_notification($scheduled_fix, 'failure', $result);
                }
            }
            
        } catch (Exception $e) {
            $this->update_scheduled_fix_status($scheduled_fix_id, 'failed', array(
                'error' => $e->getMessage()
            ));
        }
    }
    
    /**
     * Store scheduled fix in database
     */
    private function store_scheduled_fix($scheduled_fix) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'redco_scheduled_fixes';
        
        return $wpdb->insert(
            $table_name,
            array(
                'id' => $scheduled_fix['id'],
                'fix_id' => $scheduled_fix['fix_id'],
                'fix_details' => json_encode($scheduled_fix['fix_details']),
                'schedule_type' => $scheduled_fix['schedule_type'],
                'scheduled_time' => $scheduled_fix['scheduled_time'],
                'created_by' => $scheduled_fix['created_by'],
                'created_at' => $scheduled_fix['created_at'],
                'status' => $scheduled_fix['status'],
                'config' => json_encode($scheduled_fix['config']),
                'notifications' => json_encode($scheduled_fix['notifications'])
            ),
            array('%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s', '%s', '%s')
        );
    }
    
    /**
     * Schedule WordPress cron event
     */
    private function schedule_cron_event($scheduled_fix) {
        $timestamp = strtotime($scheduled_fix['scheduled_time']);
        
        wp_schedule_single_event(
            $timestamp,
            'redco_execute_scheduled_fix',
            array($scheduled_fix['id'])
        );
    }
    
    /**
     * Validate schedule configuration
     */
    private function validate_schedule_config($config) {
        if ($config['type'] === 'custom_time') {
            if (empty($config['custom_datetime'])) {
                return new WP_Error('invalid_datetime', 'Custom datetime is required');
            }
            
            $timestamp = strtotime($config['custom_datetime']);
            if ($timestamp === false || $timestamp <= time()) {
                return new WP_Error('invalid_datetime', 'Custom datetime must be in the future');
            }
        }
        
        return true;
    }
    
    /**
     * Collect current metrics for preview
     */
    private function collect_current_metrics() {
        return array(
            'page_load_time' => $this->measure_page_load_time(),
            'database_size' => $this->get_database_size(),
            'file_count' => $this->count_files(),
            'timestamp' => time()
        );
    }
    
    /**
     * Maybe create database tables
     */
    private function maybe_create_tables() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'redco_scheduled_fixes';
        
        if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") != $table_name) {
            $charset_collate = $wpdb->get_charset_collate();
            
            $sql = "CREATE TABLE {$table_name} (
                id varchar(36) NOT NULL,
                fix_id varchar(100) NOT NULL,
                fix_details longtext NOT NULL,
                schedule_type varchar(50) NOT NULL,
                scheduled_time datetime NOT NULL,
                created_by bigint(20) unsigned NOT NULL,
                created_at datetime NOT NULL,
                executed_at datetime DEFAULT NULL,
                status varchar(20) NOT NULL DEFAULT 'scheduled',
                execution_result longtext DEFAULT NULL,
                config longtext DEFAULT NULL,
                notifications longtext DEFAULT NULL,
                PRIMARY KEY (id),
                KEY fix_id_idx (fix_id),
                KEY scheduled_time_idx (scheduled_time),
                KEY status_idx (status)
            ) {$charset_collate};";
            
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
        }
    }

    /**
     * Helper methods for scheduling operations
     */
    private function get_scheduled_fix($scheduled_fix_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'redco_scheduled_fixes';

        return $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$table_name} WHERE id = %s", $scheduled_fix_id),
            ARRAY_A
        );
    }

    private function update_scheduled_fix_status($scheduled_fix_id, $status, $result = null) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'redco_scheduled_fixes';

        $update_data = array(
            'status' => $status
        );

        if ($status === 'completed' || $status === 'failed') {
            $update_data['executed_at'] = current_time('mysql');
            $update_data['execution_result'] = json_encode($result);
        }

        return $wpdb->update(
            $table_name,
            $update_data,
            array('id' => $scheduled_fix_id),
            array('%s', '%s', '%s'),
            array('%s')
        );
    }

    private function send_fix_notification($scheduled_fix, $type, $result = null) {
        $notifications = $scheduled_fix['notifications'];

        if (empty($notifications['enabled']) || !$notifications['enabled']) {
            return;
        }

        $subject = "Redco Optimizer: Scheduled Fix {$type}";
        $message = $this->build_notification_message($scheduled_fix, $type, $result);

        // Email notification
        if (!empty($notifications['email'])) {
            wp_mail($notifications['email'], $subject, $message);
        }
    }

    private function build_notification_message($scheduled_fix, $type, $result = null) {
        $fix_name = $scheduled_fix['fix_details']['title'] ?? $scheduled_fix['fix_id'];

        switch ($type) {
            case 'success':
                return "The scheduled fix '{$fix_name}' has been successfully applied.";
            case 'failure':
                $error = $result['error'] ?? 'Unknown error';
                return "The scheduled fix '{$fix_name}' failed to apply. Error: {$error}";
            default:
                return "Scheduled fix '{$fix_name}' status: {$type}";
        }
    }

    private function measure_page_load_time() {
        // Simple page load time measurement
        $start_time = microtime(true);
        $response = wp_remote_get(home_url());
        $end_time = microtime(true);

        if (is_wp_error($response)) {
            return 0;
        }

        return round(($end_time - $start_time) * 1000, 2); // Convert to milliseconds
    }

    private function get_database_size() {
        global $wpdb;

        $result = $wpdb->get_row(
            "SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'size'
             FROM information_schema.tables
             WHERE table_schema = '{$wpdb->dbname}'"
        );

        return $result ? $result->size : 0;
    }

    private function count_files() {
        $count = 0;

        // Count theme files
        $theme_dir = get_template_directory();
        if (is_dir($theme_dir)) {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($theme_dir)
            );

            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $count++;
                }
            }
        }

        return $count;
    }

    private function calculate_estimated_impact($preview_results) {
        $impact = array(
            'performance_improvement' => 'Unknown',
            'risk_level' => 'Low',
            'estimated_time' => '< 1 minute'
        );

        // Calculate based on simulated metrics
        if (!empty($preview_results['simulated_after_metrics'])) {
            $metrics = $preview_results['simulated_after_metrics'];

            if (isset($metrics['load_time_improvement'])) {
                $impact['performance_improvement'] = $metrics['load_time_improvement'];
            }

            if (isset($metrics['bandwidth_savings'])) {
                $impact['bandwidth_savings'] = $metrics['bandwidth_savings'];
            }
        }

        return $impact;
    }

    private function preview_generic_fix($preview_results, $fix_details) {
        // Generic preview for unknown fix types
        $preview_results['configuration_changes'][] = array(
            'type' => 'generic_optimization',
            'description' => 'Apply optimization fix: ' . ($fix_details['title'] ?? $fix_details['id']),
            'estimated_impact' => 'Moderate improvement expected'
        );

        $preview_results['simulated_after_metrics']['estimated_improvement'] = '10-20%';

        return $preview_results;
    }

    private function preview_autoload_optimization($preview_results) {
        global $wpdb;

        // Count autoloaded options
        $autoload_size = $wpdb->get_var(
            "SELECT SUM(LENGTH(option_value)) FROM {$wpdb->options} WHERE autoload = 'yes'"
        );

        $preview_results['database_changes'][] = array(
            'action' => 'Optimize autoloaded options',
            'current_size' => $autoload_size,
            'estimated_reduction' => $autoload_size * 0.2 // 20% reduction estimate
        );

        $preview_results['simulated_after_metrics']['autoload_optimization'] = '20% reduction';

        return $preview_results;
    }

    private function preview_browser_caching($preview_results) {
        $preview_results['configuration_changes'][] = array(
            'file' => '.htaccess',
            'change' => 'Add browser caching rules',
            'content_preview' => "# Browser Caching\n<IfModule mod_expires.c>\n    ExpiresActive On\n    ExpiresByType text/css \"access plus 1 year\"\n    ExpiresByType application/javascript \"access plus 1 year\"\n</IfModule>"
        );

        $preview_results['simulated_after_metrics']['caching_improvement'] = 'Repeat visitors: 50% faster load times';

        return $preview_results;
    }
}
