<?php
/**
 * Immediate Diagnostic Reset Script
 * This script provides an immediate reset of all diagnostic data for testing purposes
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// Security check - only allow admin users
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

echo "<h1>🔄 Immediate Diagnostic Reset</h1>\n";
echo "<p><strong>This script will immediately clear all diagnostic data to create a fresh testing environment.</strong></p>\n";

// Check if reset is requested
$perform_reset = isset($_GET['confirm']) && $_GET['confirm'] === 'yes';

if (!$perform_reset) {
    // Show confirmation form
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h3>⚠️ Warning</h3>\n";
    echo "<p>This will permanently delete:</p>\n";
    echo "<ul>\n";
    echo "<li>All fix history (<code>redco_diagnostic_fix_history</code>)</li>\n";
    echo "<li>All fixed issues (<code>redco_fixed_issues</code>)</li>\n";
    echo "<li>All diagnostic results (<code>redco_diagnostic_results</code>)</li>\n";
    echo "<li>All diagnostic statistics (<code>redco_diagnostic_stats</code>)</li>\n";
    echo "<li>All rollback history (<code>redco_rollback_history</code>)</li>\n";
    echo "<li>All related caches and transients</li>\n";
    echo "</ul>\n";
    echo "<p><strong>This action cannot be undone!</strong></p>\n";
    echo "</div>\n";
    
    echo "<p><a href='?confirm=yes' style='background: #dc3232; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🗑️ Confirm Reset</a></p>\n";
    echo "<p><a href='javascript:history.back()' style='background: #666; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>← Cancel</a></p>\n";
    exit;
}

// Perform the reset
echo "<h2>🔄 Performing Reset...</h2>\n";

$operation_id = 'immediate_reset_' . time();
$cleared_items = array();
$start_time = microtime(true);

try {
    // 1. Clear fix history
    if (delete_option('redco_diagnostic_fix_history')) {
        $cleared_items[] = 'Fix history';
        echo "✅ Fix history cleared<br>\n";
    } else {
        echo "ℹ️ Fix history was already empty<br>\n";
    }

    // 2. Clear fixed issues
    if (delete_option('redco_fixed_issues')) {
        $cleared_items[] = 'Fixed issues';
        echo "✅ Fixed issues cleared<br>\n";
    } else {
        echo "ℹ️ Fixed issues was already empty<br>\n";
    }

    // 3. Clear diagnostic results
    if (delete_option('redco_diagnostic_results')) {
        $cleared_items[] = 'Diagnostic results';
        echo "✅ Diagnostic results cleared<br>\n";
    } else {
        echo "ℹ️ Diagnostic results was already empty<br>\n";
    }

    // 4. Reset diagnostic statistics
    $default_stats = array(
        'health_score' => 100,
        'performance_score' => 68,
        'issues_found' => 0,
        'critical_issues' => 0,
        'auto_fixable_issues' => 0,
        'fixes_applied' => 0,
        'last_scan_time' => 0,
        'scan_frequency' => 'weekly',
        'auto_fix_enabled' => true,
        'emergency_mode_active' => false,
        'last_reset' => time(),
        'reset_by' => 'immediate_script'
    );
    update_option('redco_diagnostic_stats', $default_stats);
    $cleared_items[] = 'Diagnostic statistics (reset to defaults)';
    echo "✅ Diagnostic statistics reset to defaults<br>\n";

    // 5. Clear rollback history
    if (delete_option('redco_rollback_history')) {
        $cleared_items[] = 'Rollback history';
        echo "✅ Rollback history cleared<br>\n";
    } else {
        echo "ℹ️ Rollback history was already empty<br>\n";
    }

    // 6. Clear scan cache and related options
    $cache_options = array(
        'redco_scan_cache',
        'redco_last_scan_time',
        'redco_optimization_logs',
        'redco_scan_progress'
    );

    foreach ($cache_options as $option) {
        if (delete_option($option)) {
            $cleared_items[] = str_replace('redco_', '', $option);
            echo "✅ Cleared {$option}<br>\n";
        }
    }

    // 7. Clear all diagnostic-related transients
    $transients_cleared = 0;
    $diagnostic_transients = array(
        'redco_compression_check',
        'redco_cache_headers_check',
        'redco_autoload_size',
        'redco_database_size',
        'redco_ttfb_measurement',
        'redco_render_blocking_resources',
        'redco_security_headers_check',
        'redco_optimization_opportunities',
        'redco_diagnostic_results',
        'redco_diagnostic_stats',
        'redco_recent_fixes'
    );

    foreach ($diagnostic_transients as $transient) {
        if (delete_transient($transient)) {
            $transients_cleared++;
        }
    }

    if ($transients_cleared > 0) {
        $cleared_items[] = "{$transients_cleared} transients";
        echo "✅ Cleared {$transients_cleared} transients<br>\n";
    }

    // 8. Clear WordPress object cache
    wp_cache_delete('redco_diagnostic_results');
    wp_cache_delete('redco_diagnostic_stats');
    wp_cache_delete('redco_recent_fixes');
    wp_cache_delete('redco_fixed_issues');
    $cleared_items[] = 'WordPress object cache keys';
    echo "✅ WordPress object cache cleared<br>\n";

    // 9. Clear database tables if they exist
    global $wpdb;
    $tables_cleared = 0;

    try {
        // Clear fix history table
        $fix_history_table = $wpdb->prefix . 'redco_fix_history';
        if ($wpdb->get_var("SHOW TABLES LIKE '{$fix_history_table}'") == $fix_history_table) {
            $deleted_rows = $wpdb->query("DELETE FROM {$fix_history_table}");
            if ($deleted_rows !== false) {
                $tables_cleared++;
                $cleared_items[] = "Fix history table ({$deleted_rows} rows)";
                echo "✅ Cleared fix history table ({$deleted_rows} rows)<br>\n";
            }
        }

        // Clear scheduled fixes table
        $scheduled_fixes_table = $wpdb->prefix . 'redco_scheduled_fixes';
        if ($wpdb->get_var("SHOW TABLES LIKE '{$scheduled_fixes_table}'") == $scheduled_fixes_table) {
            $deleted_rows = $wpdb->query("DELETE FROM {$scheduled_fixes_table}");
            if ($deleted_rows !== false) {
                $tables_cleared++;
                $cleared_items[] = "Scheduled fixes table ({$deleted_rows} rows)";
                echo "✅ Cleared scheduled fixes table ({$deleted_rows} rows)<br>\n";
            }
        }
    } catch (Exception $e) {
        echo "⚠️ Database clearing error (non-fatal): " . $e->getMessage() . "<br>\n";
        $cleared_items[] = 'Database tables (some errors occurred)';
    }

    $end_time = microtime(true);
    $duration = round(($end_time - $start_time) * 1000, 2);

    echo "<h2>✅ Reset Complete!</h2>\n";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h3>📊 Reset Summary</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>Operation ID:</strong> {$operation_id}</li>\n";
    echo "<li><strong>Items Cleared:</strong> " . count($cleared_items) . "</li>\n";
    echo "<li><strong>Duration:</strong> {$duration}ms</li>\n";
    echo "<li><strong>Timestamp:</strong> " . date('Y-m-d H:i:s') . "</li>\n";
    echo "</ul>\n";
    echo "<h4>Cleared Items:</h4>\n";
    echo "<ul>\n";
    foreach ($cleared_items as $item) {
        echo "<li>{$item}</li>\n";
    }
    echo "</ul>\n";
    echo "</div>\n";

    echo "<h3>🎯 Next Steps</h3>\n";
    echo "<ol>\n";
    echo "<li>Go to the <a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>Diagnostic & Auto-Fix module</a></li>\n";
    echo "<li>Verify that all sections show empty/default states</li>\n";
    echo "<li>Run a comprehensive scan to create test data</li>\n";
    echo "<li>Apply some fixes to create fix history</li>\n";
    echo "<li>Test the rollback functionality to validate the state management fixes</li>\n";
    echo "</ol>\n";

    echo "<p><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🚀 Go to Diagnostic Module</a></p>\n";

} catch (Exception $e) {
    echo "<h2>❌ Reset Failed</h2>\n";
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>\n";
    echo "</div>\n";
}
?>
