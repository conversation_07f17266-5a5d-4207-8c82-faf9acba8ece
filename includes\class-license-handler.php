<?php
/**
 * License Handler for Redco Optimizer
 * 
 * Pre-wired for future licensing integration with external SaaS platform.
 * Currently disabled but ready for activation.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Optimizer_License_Handler {
    
    /**
     * License API endpoint
     */
    private $api_endpoint = 'https://api.redco.com/v1/';
    
    /**
     * License option key
     */
    private $license_key_option = 'redco_optimizer_license_key';
    
    /**
     * License status option key
     */
    private $license_status_option = 'redco_optimizer_license_status';
    
    /**
     * Constructor
     */
    public function __construct() {
        // Currently disabled - will be enabled in future versions
        if (defined('REDCO_OPTIMIZER_ENABLE_LICENSING') && REDCO_OPTIMIZER_ENABLE_LICENSING) {
            $this->init();
        }
    }
    
    /**
     * Initialize license handler
     */
    private function init() {
        add_action('admin_init', array($this, 'register_license_settings'));
        add_action('wp_ajax_redco_activate_license', array($this, 'ajax_activate_license'));
        add_action('wp_ajax_redco_deactivate_license', array($this, 'ajax_deactivate_license'));
        add_action('wp_ajax_redco_check_license', array($this, 'ajax_check_license'));
        
        // Daily license check
        add_action('redco_optimizer_daily_license_check', array($this, 'daily_license_check'));
        
        if (!wp_next_scheduled('redco_optimizer_daily_license_check')) {
            wp_schedule_event(time(), 'daily', 'redco_optimizer_daily_license_check');
        }
    }
    
    /**
     * Register license settings
     */
    public function register_license_settings() {
        register_setting('redco_optimizer_license', $this->license_key_option);
        register_setting('redco_optimizer_license', $this->license_status_option);
    }
    
    /**
     * Get license key
     */
    public function get_license_key() {
        return get_option($this->license_key_option, '');
    }
    
    /**
     * Get license status
     */
    public function get_license_status() {
        return get_option($this->license_status_option, 'inactive');
    }
    
    /**
     * Set license key
     */
    public function set_license_key($key) {
        return update_option($this->license_key_option, sanitize_text_field($key));
    }
    
    /**
     * Set license status
     */
    public function set_license_status($status) {
        return update_option($this->license_status_option, sanitize_text_field($status));
    }
    
    /**
     * Activate license
     */
    public function activate_license($license_key) {
        $response = $this->api_request('activate', array(
            'license_key' => $license_key,
            'site_url' => home_url(),
            'plugin_version' => REDCO_OPTIMIZER_VERSION
        ));
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if ($data && isset($data['success']) && $data['success']) {
            $this->set_license_key($license_key);
            $this->set_license_status('active');
            
            // Store license data
            update_option('redco_optimizer_license_data', $data['license']);
            
            return true;
        }
        
        return new WP_Error('license_activation_failed', $data['message'] ?? __('License activation failed', 'redco-optimizer'));
    }
    
    /**
     * Deactivate license
     */
    public function deactivate_license() {
        $license_key = $this->get_license_key();
        
        if (empty($license_key)) {
            return new WP_Error('no_license_key', __('No license key found', 'redco-optimizer'));
        }
        
        $response = $this->api_request('deactivate', array(
            'license_key' => $license_key,
            'site_url' => home_url()
        ));
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if ($data && isset($data['success']) && $data['success']) {
            $this->set_license_status('inactive');
            delete_option('redco_optimizer_license_data');
            
            return true;
        }
        
        return new WP_Error('license_deactivation_failed', $data['message'] ?? __('License deactivation failed', 'redco-optimizer'));
    }
    
    /**
     * Check license status
     */
    public function check_license() {
        $license_key = $this->get_license_key();
        
        if (empty($license_key)) {
            return false;
        }
        
        $response = $this->api_request('check', array(
            'license_key' => $license_key,
            'site_url' => home_url()
        ));
        
        if (is_wp_error($response)) {
            return false;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if ($data && isset($data['success']) && $data['success']) {
            $this->set_license_status($data['license']['status']);
            update_option('redco_optimizer_license_data', $data['license']);
            
            return $data['license']['status'] === 'active';
        }
        
        return false;
    }
    
    /**
     * Get license data
     */
    public function get_license_data() {
        return get_option('redco_optimizer_license_data', array());
    }
    
    /**
     * Check if license is valid
     */
    public function is_license_valid() {
        $status = $this->get_license_status();
        return $status === 'active';
    }
    
    /**
     * Make API request
     */
    private function api_request($action, $data = array()) {
        $url = $this->api_endpoint . 'license/' . $action;
        
        $args = array(
            'method' => 'POST',
            'timeout' => 30,
            'headers' => array(
                'Content-Type' => 'application/json',
                'User-Agent' => 'Redco Optimizer/' . REDCO_OPTIMIZER_VERSION
            ),
            'body' => json_encode($data)
        );
        
        return wp_remote_request($url, $args);
    }
    
    /**
     * AJAX handler for license activation
     */
    public function ajax_activate_license() {
        check_ajax_referer('redco_optimizer_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'redco-optimizer'));
        }
        
        $license_key = sanitize_text_field($_POST['license_key']);
        
        if (empty($license_key)) {
            wp_send_json_error(array('message' => __('Please enter a license key', 'redco-optimizer')));
        }
        
        $result = $this->activate_license($license_key);
        
        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => $result->get_error_message()));
        }
        
        wp_send_json_success(array('message' => __('License activated successfully', 'redco-optimizer')));
    }
    
    /**
     * AJAX handler for license deactivation
     */
    public function ajax_deactivate_license() {
        check_ajax_referer('redco_optimizer_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'redco-optimizer'));
        }
        
        $result = $this->deactivate_license();
        
        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => $result->get_error_message()));
        }
        
        wp_send_json_success(array('message' => __('License deactivated successfully', 'redco-optimizer')));
    }
    
    /**
     * AJAX handler for license check
     */
    public function ajax_check_license() {
        check_ajax_referer('redco_optimizer_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'redco-optimizer'));
        }
        
        $is_valid = $this->check_license();
        
        wp_send_json_success(array(
            'valid' => $is_valid,
            'status' => $this->get_license_status(),
            'data' => $this->get_license_data()
        ));
    }
    
    /**
     * Daily license check
     */
    public function daily_license_check() {
        $this->check_license();
    }
    
    /**
     * Render license settings page
     */
    public function render_license_page() {
        $license_key = $this->get_license_key();
        $license_status = $this->get_license_status();
        $license_data = $this->get_license_data();
        ?>
        <div class="redco-license-section">
            <h2><?php _e('License Settings', 'redco-optimizer'); ?></h2>
            
            <form id="redco-license-form">
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php _e('License Key', 'redco-optimizer'); ?></th>
                        <td>
                            <input type="text" 
                                   id="license_key" 
                                   name="license_key" 
                                   value="<?php echo esc_attr($license_key); ?>" 
                                   class="regular-text"
                                   <?php disabled($license_status === 'active'); ?>>
                            
                            <?php if ($license_status === 'active'): ?>
                                <button type="button" id="deactivate-license" class="button button-secondary">
                                    <?php _e('Deactivate License', 'redco-optimizer'); ?>
                                </button>
                            <?php else: ?>
                                <button type="button" id="activate-license" class="button button-primary">
                                    <?php _e('Activate License', 'redco-optimizer'); ?>
                                </button>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('License Status', 'redco-optimizer'); ?></th>
                        <td>
                            <span class="license-status license-<?php echo esc_attr($license_status); ?>">
                                <?php echo esc_html(ucfirst($license_status)); ?>
                            </span>
                        </td>
                    </tr>
                    <?php if (!empty($license_data)): ?>
                        <tr>
                            <th scope="row"><?php _e('License Information', 'redco-optimizer'); ?></th>
                            <td>
                                <ul>
                                    <?php if (isset($license_data['expires'])): ?>
                                        <li><?php printf(__('Expires: %s', 'redco-optimizer'), date('F j, Y', strtotime($license_data['expires']))); ?></li>
                                    <?php endif; ?>
                                    <?php if (isset($license_data['sites_limit'])): ?>
                                        <li><?php printf(__('Sites Limit: %d', 'redco-optimizer'), $license_data['sites_limit']); ?></li>
                                    <?php endif; ?>
                                    <?php if (isset($license_data['sites_used'])): ?>
                                        <li><?php printf(__('Sites Used: %d', 'redco-optimizer'), $license_data['sites_used']); ?></li>
                                    <?php endif; ?>
                                </ul>
                            </td>
                        </tr>
                    <?php endif; ?>
                </table>
            </form>
        </div>
        <?php
    }
}
