<?php
// Test backup validation to see why it's failing
require_once('d:/xampp/htdocs/wordpress/wp-config.php');
require_once('includes/helpers.php');
require_once('modules/diagnostic-autofix/class-diagnostic-autofix-engine.php');

echo "=== TESTING BACKUP VALIDATION ===\n";

// Create engine instance
$engine = new Redco_Diagnostic_AutoFix_Engine();

// Use reflection to access private methods for testing
$reflection = new ReflectionClass($engine);

// Get the backup directory
$backup_dir_property = $reflection->getProperty('backup_dir');
$backup_dir_property->setAccessible(true);
$backup_dir = $backup_dir_property->getValue($engine);

echo "Backup directory: $backup_dir\n";

// Check existing backups
if (is_dir($backup_dir)) {
    $backups = glob($backup_dir . '*');
    echo "Found " . count($backups) . " existing backups\n";
    
    if (!empty($backups)) {
        // Test validation on the latest backup
        $latest_backup = end($backups);
        $backup_id = basename($latest_backup);
        echo "Testing validation on latest backup: $backup_id\n";
        
        // Access private validate_backup method
        $validate_method = $reflection->getMethod('validate_backup');
        $validate_method->setAccessible(true);
        
        $validation_result = $validate_method->invoke($engine, $backup_id);
        echo "Validation result: " . ($validation_result ? 'PASSED' : 'FAILED') . "\n";
        
        // Check backup metadata
        $backup_data_file = $latest_backup . '/backup_data.json';
        if (file_exists($backup_data_file)) {
            echo "Backup metadata file exists\n";
            $backup_data = json_decode(file_get_contents($backup_data_file), true);
            if ($backup_data) {
                echo "Backup data structure:\n";
                echo "  Files backed up: " . count($backup_data['files']) . "\n";
                echo "  Options backed up: " . count($backup_data['options']) . "\n";
                echo "  System state saved: " . (isset($backup_data['system_state']) ? 'Yes' : 'No') . "\n";
                
                // Check if files exist
                foreach ($backup_data['files'] as $file_data) {
                    $backup_file_exists = file_exists($file_data['backup']);
                    $original_file_exists = file_exists($file_data['original']);
                    echo "  File: " . basename($file_data['original']) . " - Original: " . ($original_file_exists ? 'Yes' : 'No') . ", Backup: " . ($backup_file_exists ? 'Yes' : 'No') . "\n";
                }
            } else {
                echo "Failed to parse backup metadata JSON\n";
            }
        } else {
            echo "Backup metadata file missing!\n";
        }
    }
} else {
    echo "Backup directory does not exist!\n";
}

// Test creating a new backup
echo "\n=== TESTING NEW BACKUP CREATION ===\n";
$create_method = $reflection->getMethod('create_backup');
$create_method->setAccessible(true);

$new_backup_id = $create_method->invoke($engine);
echo "New backup creation result: " . ($new_backup_id ? $new_backup_id : 'FAILED') . "\n";

if ($new_backup_id) {
    echo "SUCCESS! Backup created with ID: $new_backup_id\n";
} else {
    echo "FAILED! Backup creation failed\n";
}
