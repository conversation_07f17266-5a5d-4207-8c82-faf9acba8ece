<?php
/**
 * Asset Optimization Module Migration Script
 * 
 * Handles migration from separate CSS/JS Minifier and Critical Resource Optimizer modules
 * to the unified Asset Optimization module.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Asset_Optimization_Migration {

    /**
     * Run the migration process
     */
    public static function migrate() {
        $migration_log = array();
        
        // Check if migration is needed
        if (!self::needs_migration()) {
            return array(
                'success' => true,
                'message' => 'No migration needed',
                'log' => array()
            );
        }

        try {
            // Step 1: Migrate CSS/JS Minifier settings
            $css_js_migration = self::migrate_css_js_minifier();
            $migration_log = array_merge($migration_log, $css_js_migration['log']);

            // Step 2: Migrate Critical Resource Optimizer settings  
            $critical_migration = self::migrate_critical_resource_optimizer();
            $migration_log = array_merge($migration_log, $critical_migration['log']);

            // Step 3: Merge and consolidate settings
            $consolidation = self::consolidate_settings();
            $migration_log = array_merge($migration_log, $consolidation['log']);

            // Step 4: Migrate cache files
            $cache_migration = self::migrate_cache_files();
            $migration_log = array_merge($migration_log, $cache_migration['log']);

            // Step 5: Migrate statistics
            $stats_migration = self::migrate_statistics();
            $migration_log = array_merge($migration_log, $stats_migration['log']);

            // Step 6: Disable old modules
            $disable_result = self::disable_old_modules();
            $migration_log = array_merge($migration_log, $disable_result['log']);

            // Step 7: Enable new module
            redco_enable_module('asset-optimization');
            $migration_log[] = 'Enabled Asset Optimization module';

            // Step 8: Mark migration as complete
            self::mark_migration_complete();
            $migration_log[] = 'Migration completed successfully';

            return array(
                'success' => true,
                'message' => 'Migration completed successfully',
                'log' => $migration_log
            );

        } catch (Exception $e) {
            $migration_log[] = 'Migration failed: ' . $e->getMessage();
            
            return array(
                'success' => false,
                'message' => 'Migration failed: ' . $e->getMessage(),
                'log' => $migration_log
            );
        }
    }

    /**
     * Check if migration is needed
     */
    private static function needs_migration() {
        // Check if migration already completed
        if (get_option('redco_asset_optimization_migration_complete', false)) {
            return false;
        }

        // Check if old modules exist and are enabled
        $css_js_enabled = redco_is_module_enabled('css-js-minifier');
        $critical_enabled = redco_is_module_enabled('critical-resource-optimizer');

        return $css_js_enabled || $critical_enabled;
    }

    /**
     * Migrate CSS/JS Minifier settings
     */
    private static function migrate_css_js_minifier() {
        $log = array();
        
        if (!redco_is_module_enabled('css-js-minifier')) {
            $log[] = 'CSS/JS Minifier not enabled, skipping migration';
            return array('success' => true, 'log' => $log);
        }

        // Get existing settings
        $old_settings = redco_get_module_option('css-js-minifier', 'settings', array());
        
        if (empty($old_settings)) {
            $log[] = 'No CSS/JS Minifier settings found';
            return array('success' => true, 'log' => $log);
        }

        // Map settings to new structure
        $mapped_settings = array();
        
        $setting_map = array(
            'minify_css' => 'minify_css',
            'minify_js' => 'minify_js', 
            'minify_inline' => 'minify_inline',
            'exclude_css' => 'exclude_css',
            'exclude_js' => 'exclude_js',
            'combine_css' => 'combine_css',
            'combine_js' => 'combine_js'
        );

        foreach ($setting_map as $old_key => $new_key) {
            if (isset($old_settings[$old_key])) {
                $mapped_settings[$new_key] = $old_settings[$old_key];
                $log[] = "Migrated setting: {$old_key} -> {$new_key}";
            }
        }

        // Store migrated settings temporarily
        update_option('redco_asset_optimization_migrated_css_js', $mapped_settings);
        $log[] = 'CSS/JS Minifier settings migrated successfully';

        return array('success' => true, 'log' => $log);
    }

    /**
     * Migrate Critical Resource Optimizer settings
     */
    private static function migrate_critical_resource_optimizer() {
        $log = array();
        
        if (!redco_is_module_enabled('critical-resource-optimizer')) {
            $log[] = 'Critical Resource Optimizer not enabled, skipping migration';
            return array('success' => true, 'log' => $log);
        }

        // Get existing settings
        $old_settings = redco_get_module_option('critical-resource-optimizer', 'settings', array());
        
        if (empty($old_settings)) {
            $log[] = 'No Critical Resource Optimizer settings found';
            return array('success' => true, 'log' => $log);
        }

        // Map settings to new structure
        $mapped_settings = array();
        
        $setting_map = array(
            'critical_css' => 'critical_css',
            'defer_non_critical' => 'defer_non_critical',
            'optimize_js' => 'optimize_js',
            'optimize_fonts' => 'optimize_fonts',
            'resource_hints' => 'resource_hints',
            'preconnect_google_fonts' => 'preconnect_google_fonts',
            'preconnect_analytics' => 'preconnect_analytics',
            'async_js' => 'async_js',
            'defer_js' => 'defer_js',
            'preload_critical' => 'preload_critical'
        );

        foreach ($setting_map as $old_key => $new_key) {
            if (isset($old_settings[$old_key])) {
                $mapped_settings[$new_key] = $old_settings[$old_key];
                $log[] = "Migrated setting: {$old_key} -> {$new_key}";
            }
        }

        // Store migrated settings temporarily
        update_option('redco_asset_optimization_migrated_critical', $mapped_settings);
        $log[] = 'Critical Resource Optimizer settings migrated successfully';

        return array('success' => true, 'log' => $log);
    }

    /**
     * Consolidate migrated settings
     */
    private static function consolidate_settings() {
        $log = array();

        // Get migrated settings
        $css_js_settings = get_option('redco_asset_optimization_migrated_css_js', array());
        $critical_settings = get_option('redco_asset_optimization_migrated_critical', array());

        // Merge settings with defaults
        $default_settings = array(
            'minify_css' => true,
            'minify_js' => true,
            'minify_inline' => true,
            'exclude_css' => array(),
            'exclude_js' => array('jquery-core', 'jquery-migrate'),
            'critical_css' => true,
            'defer_non_critical' => true,
            'optimize_js' => true,
            'optimize_fonts' => true,
            'resource_hints' => true,
            'preconnect_google_fonts' => true,
            'preconnect_analytics' => true,
            'combine_css' => false,
            'combine_js' => false,
            'async_js' => true,
            'defer_js' => true,
            'preload_critical' => true,
            'remove_unused_css' => false,
            'enable_gzip' => true,
            'cache_duration' => 86400,
            'enable_brotli' => false
        );

        // Merge all settings
        $consolidated_settings = array_merge($default_settings, $css_js_settings, $critical_settings);

        // Save consolidated settings
        redco_update_module_option('asset-optimization', 'settings', $consolidated_settings);
        $log[] = 'Settings consolidated successfully';

        // Clean up temporary settings
        delete_option('redco_asset_optimization_migrated_css_js');
        delete_option('redco_asset_optimization_migrated_critical');
        $log[] = 'Temporary migration data cleaned up';

        return array('success' => true, 'log' => $log);
    }

    /**
     * Migrate cache files
     */
    private static function migrate_cache_files() {
        $log = array();

        $upload_dir = wp_upload_dir();
        $new_cache_dir = $upload_dir['basedir'] . '/redco-optimizer-cache/';

        // Create new cache directory structure
        if (!file_exists($new_cache_dir)) {
            wp_mkdir_p($new_cache_dir);
        }
        
        $optimized_dir = $new_cache_dir . 'optimized/';
        $critical_dir = $new_cache_dir . 'critical-css/';
        
        if (!file_exists($optimized_dir)) {
            wp_mkdir_p($optimized_dir);
        }
        
        if (!file_exists($critical_dir)) {
            wp_mkdir_p($critical_dir);
        }

        // Migrate CSS/JS Minifier cache
        $old_css_js_cache = $upload_dir['basedir'] . '/redco-css-js-cache/';
        if (is_dir($old_css_js_cache)) {
            $migrated_files = self::copy_directory($old_css_js_cache, $optimized_dir);
            $log[] = "Migrated {$migrated_files} files from CSS/JS cache";
        }

        // Migrate Critical Resource Optimizer cache
        $old_critical_cache = $upload_dir['basedir'] . '/redco-critical-css/';
        if (is_dir($old_critical_cache)) {
            $migrated_files = self::copy_directory($old_critical_cache, $critical_dir);
            $log[] = "Migrated {$migrated_files} files from Critical CSS cache";
        }

        return array('success' => true, 'log' => $log);
    }

    /**
     * Migrate statistics from old modules
     */
    private static function migrate_statistics() {
        $log = array();

        // Get existing statistics
        $css_js_stats = get_option('redco_css_js_minifier_stats', array());
        $critical_stats = get_option('redco_critical_resource_optimizer_stats', array());

        // Merge statistics
        $merged_stats = array(
            'css_files' => ($css_js_stats['css_files'] ?? 0) + ($critical_stats['css_files'] ?? 0),
            'js_files' => ($css_js_stats['js_files'] ?? 0) + ($critical_stats['js_files'] ?? 0),
            'total_files' => ($css_js_stats['total_files'] ?? 0) + ($critical_stats['total_files'] ?? 0),
            'original_size' => ($css_js_stats['original_size'] ?? 0) + ($critical_stats['original_size'] ?? 0),
            'optimized_size' => ($css_js_stats['optimized_size'] ?? 0) + ($critical_stats['optimized_size'] ?? 0),
            'bytes_saved' => ($css_js_stats['bytes_saved'] ?? 0) + ($critical_stats['bytes_saved'] ?? 0),
            'last_optimized' => max($css_js_stats['last_optimized'] ?? 0, $critical_stats['last_optimized'] ?? 0),
            'compression_ratio' => 0
        );

        // Calculate compression ratio
        if ($merged_stats['original_size'] > 0) {
            $merged_stats['compression_ratio'] = round((1 - ($merged_stats['optimized_size'] / $merged_stats['original_size'])) * 100, 1);
        }

        // Save merged statistics
        update_option('redco_asset_optimization_stats', $merged_stats);
        $log[] = 'Statistics migrated and merged successfully';

        return array('success' => true, 'log' => $log);
    }

    /**
     * Disable old modules
     */
    private static function disable_old_modules() {
        $log = array();

        // Disable CSS/JS Minifier
        if (redco_is_module_enabled('css-js-minifier')) {
            redco_disable_module('css-js-minifier');
            $log[] = 'Disabled CSS/JS Minifier module';
        }

        // Disable Critical Resource Optimizer
        if (redco_is_module_enabled('critical-resource-optimizer')) {
            redco_disable_module('critical-resource-optimizer');
            $log[] = 'Disabled Critical Resource Optimizer module';
        }

        return array('success' => true, 'log' => $log);
    }

    /**
     * Copy directory contents
     */
    private static function copy_directory($source, $destination) {
        $files_copied = 0;

        if (!is_dir($source)) {
            return 0;
        }

        if (!is_dir($destination)) {
            wp_mkdir_p($destination);
        }

        try {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($source, RecursiveDirectoryIterator::SKIP_DOTS),
                RecursiveIteratorIterator::SELF_FIRST
            );

            foreach ($iterator as $item) {
                $target = $destination . DIRECTORY_SEPARATOR . $iterator->getSubPathName();
                
                if ($item->isDir()) {
                    if (!is_dir($target)) {
                        wp_mkdir_p($target);
                    }
                } else {
                    copy($item, $target);
                    $files_copied++;
                }
            }
        } catch (Exception $e) {
            // Handle errors silently
        }

        return $files_copied;
    }

    /**
     * Mark migration as complete
     */
    private static function mark_migration_complete() {
        update_option('redco_asset_optimization_migration_complete', true);
        update_option('redco_asset_optimization_migration_date', current_time('mysql'));
    }

    /**
     * Check if migration was completed
     */
    public static function is_migration_complete() {
        return get_option('redco_asset_optimization_migration_complete', false);
    }

    /**
     * Get migration date
     */
    public static function get_migration_date() {
        return get_option('redco_asset_optimization_migration_date', '');
    }
}
