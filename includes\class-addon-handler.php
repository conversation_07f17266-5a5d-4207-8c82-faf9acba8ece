<?php
/**
 * <PERSON><PERSON> Handler for Redco Optimizer
 * 
 * Pre-wired for future addon management and integration.
 * Currently disabled but ready for activation.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Optimizer_Addon_Handler {
    
    /**
     * Addon API endpoint
     */
    private $api_endpoint = 'https://api.redco.com/v1/';
    
    /**
     * Installed addons option key
     */
    private $addons_option = 'redco_optimizer_addons';
    
    /**
     * Available addons
     */
    private $available_addons = array();
    
    /**
     * Constructor
     */
    public function __construct() {
        // Currently disabled - will be enabled in future versions
        if (defined('REDCO_OPTIMIZER_ENABLE_ADDONS') && REDCO_OPTIMIZER_ENABLE_ADDONS) {
            $this->init();
        }
    }
    
    /**
     * Initialize addon handler
     */
    private function init() {
        add_action('admin_init', array($this, 'check_for_addon_updates'));
        add_action('wp_ajax_redco_install_addon', array($this, 'ajax_install_addon'));
        add_action('wp_ajax_redco_activate_addon', array($this, 'ajax_activate_addon'));
        add_action('wp_ajax_redco_deactivate_addon', array($this, 'ajax_deactivate_addon'));
        add_action('wp_ajax_redco_update_addon', array($this, 'ajax_update_addon'));
        add_action('wp_ajax_redco_refresh_addons', array($this, 'ajax_refresh_addons'));
        
        // Load available addons
        $this->load_available_addons();
        
        // Load active addons
        $this->load_active_addons();
    }
    
    /**
     * Load available addons from API
     */
    private function load_available_addons() {
        $cached_addons = get_transient('redco_optimizer_available_addons');
        
        if ($cached_addons !== false) {
            $this->available_addons = $cached_addons;
            return;
        }
        
        $response = $this->api_request('addons/list');
        
        if (!is_wp_error($response)) {
            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);
            
            if ($data && isset($data['success']) && $data['success']) {
                $this->available_addons = $data['addons'];
                set_transient('redco_optimizer_available_addons', $this->available_addons, HOUR_IN_SECONDS);
            }
        }
    }
    
    /**
     * Load active addons
     */
    private function load_active_addons() {
        $installed_addons = $this->get_installed_addons();
        
        foreach ($installed_addons as $addon_slug => $addon_data) {
            if ($addon_data['active']) {
                $addon_file = WP_PLUGIN_DIR . '/redco-optimizer-addons/' . $addon_slug . '/' . $addon_slug . '.php';
                
                if (file_exists($addon_file)) {
                    include_once $addon_file;
                }
            }
        }
    }
    
    /**
     * Get installed addons
     */
    public function get_installed_addons() {
        return get_option($this->addons_option, array());
    }
    
    /**
     * Get available addons
     */
    public function get_available_addons() {
        return $this->available_addons;
    }
    
    /**
     * Install addon
     */
    public function install_addon($addon_slug) {
        // Check if user has valid license
        $license_handler = new Redco_Optimizer_License_Handler();
        if (!$license_handler->is_license_valid()) {
            return new WP_Error('invalid_license', __('Valid license required to install addons', 'redco-optimizer'));
        }
        
        // Get addon download URL
        $response = $this->api_request('addons/download', array(
            'addon_slug' => $addon_slug,
            'license_key' => $license_handler->get_license_key(),
            'site_url' => home_url()
        ));
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (!$data || !isset($data['success']) || !$data['success']) {
            return new WP_Error('download_failed', $data['message'] ?? __('Failed to get download URL', 'redco-optimizer'));
        }
        
        // Download and install addon
        $download_url = $data['download_url'];
        $result = $this->download_and_install_addon($addon_slug, $download_url);
        
        if (is_wp_error($result)) {
            return $result;
        }
        
        // Update installed addons list
        $installed_addons = $this->get_installed_addons();
        $installed_addons[$addon_slug] = array(
            'version' => $data['version'],
            'active' => false,
            'installed_date' => current_time('mysql')
        );
        
        update_option($this->addons_option, $installed_addons);
        
        return true;
    }
    
    /**
     * Download and install addon
     */
    private function download_and_install_addon($addon_slug, $download_url) {
        require_once ABSPATH . 'wp-admin/includes/file.php';
        
        // Create addons directory if it doesn't exist
        $addons_dir = WP_PLUGIN_DIR . '/redco-optimizer-addons/';
        if (!file_exists($addons_dir)) {
            wp_mkdir_p($addons_dir);
        }
        
        // Download the addon zip file
        $temp_file = download_url($download_url);
        
        if (is_wp_error($temp_file)) {
            return $temp_file;
        }
        
        // Extract the addon
        $addon_dir = $addons_dir . $addon_slug . '/';
        
        WP_Filesystem();
        global $wp_filesystem;
        
        $result = unzip_file($temp_file, $addon_dir);
        
        // Clean up temp file
        unlink($temp_file);
        
        if (is_wp_error($result)) {
            return $result;
        }
        
        return true;
    }
    
    /**
     * Activate addon
     */
    public function activate_addon($addon_slug) {
        $installed_addons = $this->get_installed_addons();
        
        if (!isset($installed_addons[$addon_slug])) {
            return new WP_Error('addon_not_installed', __('Addon is not installed', 'redco-optimizer'));
        }
        
        $addon_file = WP_PLUGIN_DIR . '/redco-optimizer-addons/' . $addon_slug . '/' . $addon_slug . '.php';
        
        if (!file_exists($addon_file)) {
            return new WP_Error('addon_file_missing', __('Addon file is missing', 'redco-optimizer'));
        }
        
        // Include the addon file
        include_once $addon_file;
        
        // Update addon status
        $installed_addons[$addon_slug]['active'] = true;
        update_option($this->addons_option, $installed_addons);
        
        // Trigger addon activation hook
        do_action('redco_optimizer_addon_activated', $addon_slug);
        
        return true;
    }
    
    /**
     * Deactivate addon
     */
    public function deactivate_addon($addon_slug) {
        $installed_addons = $this->get_installed_addons();
        
        if (!isset($installed_addons[$addon_slug])) {
            return new WP_Error('addon_not_installed', __('Addon is not installed', 'redco-optimizer'));
        }
        
        // Trigger addon deactivation hook
        do_action('redco_optimizer_addon_deactivated', $addon_slug);
        
        // Update addon status
        $installed_addons[$addon_slug]['active'] = false;
        update_option($this->addons_option, $installed_addons);
        
        return true;
    }
    
    /**
     * Update addon
     */
    public function update_addon($addon_slug) {
        // First deactivate the addon
        $this->deactivate_addon($addon_slug);
        
        // Remove old files
        $addon_dir = WP_PLUGIN_DIR . '/redco-optimizer-addons/' . $addon_slug . '/';
        if (is_dir($addon_dir)) {
            $this->remove_directory($addon_dir);
        }
        
        // Install new version
        $result = $this->install_addon($addon_slug);
        
        if (is_wp_error($result)) {
            return $result;
        }
        
        // Reactivate if it was active before
        $installed_addons = $this->get_installed_addons();
        if (isset($installed_addons[$addon_slug]) && $installed_addons[$addon_slug]['active']) {
            $this->activate_addon($addon_slug);
        }
        
        return true;
    }
    
    /**
     * Remove directory recursively
     */
    private function remove_directory($dir) {
        if (!is_dir($dir)) {
            return false;
        }
        
        $files = array_diff(scandir($dir), array('.', '..'));
        
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->remove_directory($path);
            } else {
                unlink($path);
            }
        }
        
        return rmdir($dir);
    }
    
    /**
     * Check for addon updates
     */
    public function check_for_addon_updates() {
        $installed_addons = $this->get_installed_addons();
        
        if (empty($installed_addons)) {
            return;
        }
        
        $response = $this->api_request('addons/check-updates', array(
            'addons' => array_keys($installed_addons)
        ));
        
        if (!is_wp_error($response)) {
            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);
            
            if ($data && isset($data['success']) && $data['success']) {
                update_option('redco_optimizer_addon_updates', $data['updates']);
            }
        }
    }
    
    /**
     * Get addon updates
     */
    public function get_addon_updates() {
        return get_option('redco_optimizer_addon_updates', array());
    }
    
    /**
     * Make API request
     */
    private function api_request($endpoint, $data = array()) {
        $url = $this->api_endpoint . $endpoint;
        
        $args = array(
            'method' => 'POST',
            'timeout' => 30,
            'headers' => array(
                'Content-Type' => 'application/json',
                'User-Agent' => 'Redco Optimizer/' . REDCO_OPTIMIZER_VERSION
            ),
            'body' => json_encode($data)
        );
        
        return wp_remote_request($url, $args);
    }
    
    /**
     * AJAX handlers
     */
    public function ajax_install_addon() {
        check_ajax_referer('redco_optimizer_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'redco-optimizer'));
        }
        
        $addon_slug = sanitize_text_field($_POST['addon_slug']);
        $result = $this->install_addon($addon_slug);
        
        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => $result->get_error_message()));
        }
        
        wp_send_json_success(array('message' => __('Addon installed successfully', 'redco-optimizer')));
    }
    
    public function ajax_activate_addon() {
        check_ajax_referer('redco_optimizer_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'redco-optimizer'));
        }
        
        $addon_slug = sanitize_text_field($_POST['addon_slug']);
        $result = $this->activate_addon($addon_slug);
        
        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => $result->get_error_message()));
        }
        
        wp_send_json_success(array('message' => __('Addon activated successfully', 'redco-optimizer')));
    }
    
    public function ajax_deactivate_addon() {
        check_ajax_referer('redco_optimizer_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'redco-optimizer'));
        }
        
        $addon_slug = sanitize_text_field($_POST['addon_slug']);
        $result = $this->deactivate_addon($addon_slug);
        
        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => $result->get_error_message()));
        }
        
        wp_send_json_success(array('message' => __('Addon deactivated successfully', 'redco-optimizer')));
    }
    
    public function ajax_update_addon() {
        check_ajax_referer('redco_optimizer_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'redco-optimizer'));
        }
        
        $addon_slug = sanitize_text_field($_POST['addon_slug']);
        $result = $this->update_addon($addon_slug);
        
        if (is_wp_error($result)) {
            wp_send_json_error(array('message' => $result->get_error_message()));
        }
        
        wp_send_json_success(array('message' => __('Addon updated successfully', 'redco-optimizer')));
    }
    
    public function ajax_refresh_addons() {
        check_ajax_referer('redco_optimizer_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'redco-optimizer'));
        }
        
        // Clear cached addons
        delete_transient('redco_optimizer_available_addons');
        
        // Reload available addons
        $this->load_available_addons();
        
        wp_send_json_success(array(
            'message' => __('Addons list refreshed', 'redco-optimizer'),
            'addons' => $this->available_addons
        ));
    }
    
    /**
     * Render addons page
     */
    public function render_addons_page() {
        $available_addons = $this->get_available_addons();
        $installed_addons = $this->get_installed_addons();
        $addon_updates = $this->get_addon_updates();
        ?>
        <div class="redco-addons-section">
            <div class="addons-header">
                <h2><?php _e('Addons', 'redco-optimizer'); ?></h2>
                <button type="button" id="refresh-addons" class="button button-secondary">
                    <?php _e('Refresh List', 'redco-optimizer'); ?>
                </button>
            </div>
            
            <div class="addons-grid">
                <?php foreach ($available_addons as $addon): ?>
                    <?php
                    $is_installed = isset($installed_addons[$addon['slug']]);
                    $is_active = $is_installed && $installed_addons[$addon['slug']]['active'];
                    $has_update = isset($addon_updates[$addon['slug']]);
                    ?>
                    <div class="addon-card <?php echo $is_installed ? 'installed' : ''; ?> <?php echo $is_active ? 'active' : ''; ?>">
                        <div class="addon-header">
                            <h3><?php echo esc_html($addon['name']); ?></h3>
                            <span class="addon-version">v<?php echo esc_html($addon['version']); ?></span>
                        </div>
                        
                        <div class="addon-description">
                            <p><?php echo esc_html($addon['description']); ?></p>
                        </div>
                        
                        <div class="addon-actions">
                            <?php if (!$is_installed): ?>
                                <button type="button" class="button button-primary install-addon" data-addon="<?php echo esc_attr($addon['slug']); ?>">
                                    <?php _e('Install', 'redco-optimizer'); ?>
                                </button>
                            <?php elseif ($has_update): ?>
                                <button type="button" class="button button-secondary update-addon" data-addon="<?php echo esc_attr($addon['slug']); ?>">
                                    <?php _e('Update', 'redco-optimizer'); ?>
                                </button>
                            <?php elseif ($is_active): ?>
                                <button type="button" class="button button-secondary deactivate-addon" data-addon="<?php echo esc_attr($addon['slug']); ?>">
                                    <?php _e('Deactivate', 'redco-optimizer'); ?>
                                </button>
                            <?php else: ?>
                                <button type="button" class="button button-primary activate-addon" data-addon="<?php echo esc_attr($addon['slug']); ?>">
                                    <?php _e('Activate', 'redco-optimizer'); ?>
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php
    }
}
