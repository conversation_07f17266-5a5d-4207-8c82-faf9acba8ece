<?php
/**
 * Performance Monitoring System for Redco Optimizer
 * 
 * Tracks and monitors plugin performance metrics
 * 
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Performance_Monitor {
    
    /**
     * Performance metrics
     */
    private static $metrics = array();
    
    /**
     * Start times for operations
     */
    private static $start_times = array();
    
    /**
     * Memory usage tracking
     */
    private static $memory_usage = array();
    
    /**
     * Database query tracking
     */
    private static $query_count = 0;
    private static $query_time = 0;
    
    /**
     * Performance thresholds
     */
    const SLOW_OPERATION_THRESHOLD = 1.0; // 1 second
    const HIGH_MEMORY_THRESHOLD = 67108864; // 64MB
    const EXCESSIVE_QUERIES_THRESHOLD = 50;
    
    /**
     * Initialize performance monitoring
     */
    public static function init() {
        if (!self::should_monitor()) {
            return;
        }
        
        // Track database queries
        add_filter('query', array(__CLASS__, 'track_query'));
        
        // Track memory usage at key points
        add_action('init', array(__CLASS__, 'track_memory_usage'));
        add_action('wp_loaded', array(__CLASS__, 'track_memory_usage'));
        add_action('wp_footer', array(__CLASS__, 'track_memory_usage'));
        
        // Track page load time
        add_action('wp_footer', array(__CLASS__, 'track_page_load_time'));
        
        // Log performance data on shutdown
        add_action('shutdown', array(__CLASS__, 'log_performance_data'));
    }
    
    /**
     * Start timing an operation
     * 
     * @param string $operation Operation name
     */
    public static function start_timer($operation) {
        self::$start_times[$operation] = microtime(true);
        self::$memory_usage[$operation . '_start'] = memory_get_usage(true);
    }
    
    /**
     * End timing an operation
     * 
     * @param string $operation Operation name
     * @return float Execution time in seconds
     */
    public static function end_timer($operation) {
        if (!isset(self::$start_times[$operation])) {
            return 0;
        }
        
        $end_time = microtime(true);
        $execution_time = $end_time - self::$start_times[$operation];
        
        $end_memory = memory_get_usage(true);
        $start_memory = isset(self::$memory_usage[$operation . '_start']) 
            ? self::$memory_usage[$operation . '_start'] 
            : 0;
        $memory_used = $end_memory - $start_memory;
        
        self::$metrics[$operation] = array(
            'execution_time' => $execution_time,
            'memory_used' => $memory_used,
            'timestamp' => current_time('timestamp')
        );
        
        // TEMPORARILY DISABLED: Log slow operations
        /*
        if ($execution_time > self::SLOW_OPERATION_THRESHOLD) {
            Redco_Error_Handler::warning(
                "Slow operation detected: {$operation} took {$execution_time}s",
                Redco_Error_Handler::CONTEXT_PERFORMANCE,
                array(
                    'operation' => $operation,
                    'execution_time' => $execution_time,
                    'memory_used' => $memory_used
                )
            );
        }

        // Log high memory usage
        if ($memory_used > self::HIGH_MEMORY_THRESHOLD) {
            Redco_Error_Handler::warning(
                "High memory usage detected: {$operation} used " . size_format($memory_used),
                Redco_Error_Handler::CONTEXT_PERFORMANCE,
                array(
                    'operation' => $operation,
                    'memory_used' => $memory_used,
                    'execution_time' => $execution_time
                )
            );
        }
        */
        
        unset(self::$start_times[$operation]);
        
        return $execution_time;
    }
    
    /**
     * Track database query
     * 
     * @param string $query SQL query
     * @return string Query (unchanged)
     */
    public static function track_query($query) {
        if (!self::should_monitor()) {
            return $query;
        }
        
        self::$query_count++;
        
        // Track query execution time
        $start_time = microtime(true);
        
        // Use a filter to track when query completes
        add_filter('posts_results', function($results) use ($start_time) {
            $query_time = microtime(true) - $start_time;
            self::$query_time += $query_time;
            return $results;
        }, 10, 1);
        
        return $query;
    }
    
    /**
     * Track memory usage
     */
    public static function track_memory_usage() {
        $hook = current_filter();
        self::$memory_usage[$hook] = memory_get_usage(true);
    }
    
    /**
     * Track page load time
     */
    public static function track_page_load_time() {
        if (!defined('REDCO_PAGE_START_TIME')) {
            return;
        }
        
        $page_load_time = microtime(true) - REDCO_PAGE_START_TIME;
        
        self::$metrics['page_load'] = array(
            'execution_time' => $page_load_time,
            'memory_peak' => memory_get_peak_usage(true),
            'timestamp' => current_time('timestamp')
        );
        
        // TEMPORARILY DISABLED: Log slow page loads
        /*
        if ($page_load_time > 3.0) { // 3 seconds threshold
            Redco_Error_Handler::warning(
                "Slow page load detected: {$page_load_time}s",
                Redco_Error_Handler::CONTEXT_PERFORMANCE,
                array(
                    'page_load_time' => $page_load_time,
                    'memory_peak' => memory_get_peak_usage(true),
                    'query_count' => self::$query_count,
                    'query_time' => self::$query_time
                )
            );
        }
        */
    }
    
    /**
     * Log performance data
     */
    public static function log_performance_data() {
        if (!self::should_monitor() || empty(self::$metrics)) {
            return;
        }
        
        // TEMPORARILY DISABLED: Check for excessive queries
        /*
        if (self::$query_count > self::EXCESSIVE_QUERIES_THRESHOLD) {
            Redco_Error_Handler::warning(
                "Excessive database queries detected: " . self::$query_count . " queries",
                Redco_Error_Handler::CONTEXT_PERFORMANCE,
                array(
                    'query_count' => self::$query_count,
                    'query_time' => self::$query_time,
                    'average_query_time' => self::$query_count > 0 ? self::$query_time / self::$query_count : 0
                )
            );
        }
        */
        
        // Store performance data for analysis
        self::store_performance_data();
    }
    
    /**
     * Store performance data
     */
    private static function store_performance_data() {
        $performance_data = array(
            'timestamp' => current_time('timestamp'),
            'metrics' => self::$metrics,
            'query_count' => self::$query_count,
            'query_time' => self::$query_time,
            'memory_usage' => self::$memory_usage,
            'url' => $_SERVER['REQUEST_URI'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        );
        
        // Store in transient for recent data (keep for 1 hour)
        $recent_data = get_transient('redco_performance_recent') ?: array();
        $recent_data[] = $performance_data;
        
        // Keep only last 50 entries
        if (count($recent_data) > 50) {
            $recent_data = array_slice($recent_data, -50);
        }
        
        set_transient('redco_performance_recent', $recent_data, HOUR_IN_SECONDS);
        
        // Store daily aggregated data
        self::store_daily_aggregated_data($performance_data);
    }
    
    /**
     * Store daily aggregated performance data
     */
    private static function store_daily_aggregated_data($data) {
        $today = date('Y-m-d');
        $daily_key = 'redco_performance_daily_' . $today;
        
        $daily_data = get_option($daily_key, array(
            'date' => $today,
            'total_requests' => 0,
            'total_execution_time' => 0,
            'total_memory_usage' => 0,
            'total_queries' => 0,
            'total_query_time' => 0,
            'slow_operations' => 0,
            'high_memory_operations' => 0
        ));
        
        $daily_data['total_requests']++;
        $daily_data['total_queries'] += $data['query_count'];
        $daily_data['total_query_time'] += $data['query_time'];
        
        if (isset($data['metrics']['page_load'])) {
            $daily_data['total_execution_time'] += $data['metrics']['page_load']['execution_time'];
            $daily_data['total_memory_usage'] += $data['metrics']['page_load']['memory_peak'];
        }
        
        // Count slow operations
        foreach ($data['metrics'] as $metric) {
            if (isset($metric['execution_time']) && $metric['execution_time'] > self::SLOW_OPERATION_THRESHOLD) {
                $daily_data['slow_operations']++;
            }
            if (isset($metric['memory_used']) && $metric['memory_used'] > self::HIGH_MEMORY_THRESHOLD) {
                $daily_data['high_memory_operations']++;
            }
        }
        
        update_option($daily_key, $daily_data);
        
        // Clean up old daily data (keep 30 days)
        self::cleanup_old_performance_data();
    }
    
    /**
     * Clean up old performance data
     */
    private static function cleanup_old_performance_data() {
        global $wpdb;
        
        $cutoff_date = date('Y-m-d', strtotime('-30 days'));
        
        $old_options = $wpdb->get_col($wpdb->prepare("
            SELECT option_name 
            FROM {$wpdb->options} 
            WHERE option_name LIKE 'redco_performance_daily_%' 
            AND option_name < %s
        ", 'redco_performance_daily_' . $cutoff_date));
        
        foreach ($old_options as $option_name) {
            delete_option($option_name);
        }
    }
    
    /**
     * Get performance metrics
     * 
     * @param string $period Period (recent, daily, weekly)
     * @return array Performance metrics
     */
    public static function get_metrics($period = 'recent') {
        switch ($period) {
            case 'recent':
                return get_transient('redco_performance_recent') ?: array();
                
            case 'daily':
                return self::get_daily_metrics();
                
            case 'weekly':
                return self::get_weekly_metrics();
                
            default:
                return array();
        }
    }
    
    /**
     * Get daily metrics
     */
    private static function get_daily_metrics() {
        $metrics = array();
        
        for ($i = 0; $i < 7; $i++) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $daily_key = 'redco_performance_daily_' . $date;
            $daily_data = get_option($daily_key);
            
            if ($daily_data) {
                $metrics[] = $daily_data;
            }
        }
        
        return array_reverse($metrics); // Oldest first
    }
    
    /**
     * Get weekly metrics
     */
    private static function get_weekly_metrics() {
        $daily_metrics = self::get_daily_metrics();
        
        if (empty($daily_metrics)) {
            return array();
        }
        
        // Aggregate daily metrics into weekly summary
        $weekly = array(
            'period' => 'week',
            'total_requests' => 0,
            'avg_execution_time' => 0,
            'avg_memory_usage' => 0,
            'avg_queries_per_request' => 0,
            'slow_operations_percentage' => 0,
            'high_memory_operations_percentage' => 0
        );
        
        $total_execution_time = 0;
        $total_memory_usage = 0;
        $total_operations = 0;
        
        foreach ($daily_metrics as $daily) {
            $weekly['total_requests'] += $daily['total_requests'];
            $total_execution_time += $daily['total_execution_time'];
            $total_memory_usage += $daily['total_memory_usage'];
            $weekly['slow_operations_percentage'] += $daily['slow_operations'];
            $weekly['high_memory_operations_percentage'] += $daily['high_memory_operations'];
            $total_operations += $daily['total_requests'];
        }
        
        if ($weekly['total_requests'] > 0) {
            $weekly['avg_execution_time'] = $total_execution_time / $weekly['total_requests'];
            $weekly['avg_memory_usage'] = $total_memory_usage / $weekly['total_requests'];
            $weekly['slow_operations_percentage'] = ($weekly['slow_operations_percentage'] / $total_operations) * 100;
            $weekly['high_memory_operations_percentage'] = ($weekly['high_memory_operations_percentage'] / $total_operations) * 100;
        }
        
        return $weekly;
    }
    
    /**
     * Check if performance monitoring should be enabled
     */
    private static function should_monitor() {
        // Always monitor in development
        if (Redco_Config::is_development_environment()) {
            return true;
        }
        
        // Check if explicitly enabled
        if (defined('REDCO_ENABLE_PERFORMANCE_MONITORING') && REDCO_ENABLE_PERFORMANCE_MONITORING) {
            return true;
        }
        
        // Check option
        return get_option('redco_optimizer_enable_performance_monitoring', false);
    }
    
    /**
     * Get current operation metrics
     */
    public static function get_current_metrics() {
        return array(
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'query_count' => self::$query_count,
            'query_time' => self::$query_time,
            'operations' => self::$metrics
        );
    }
    
    /**
     * Reset metrics
     */
    public static function reset_metrics() {
        self::$metrics = array();
        self::$start_times = array();
        self::$memory_usage = array();
        self::$query_count = 0;
        self::$query_time = 0;
    }
}
