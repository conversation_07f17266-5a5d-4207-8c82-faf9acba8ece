/**
 * Enhanced UI Components for Redco Optimizer Diagnostic & Auto-Fix
 * 
 * Integrates with existing diagnostic-autofix.js while adding new tiered interface
 */

(function($) {
    'use strict';
    
    // Extend existing DiagnosticAutoFix object
    if (typeof window.DiagnosticAutoFix !== 'undefined') {
        
        // Store reference to original methods
        const originalInit = window.DiagnosticAutoFix.init;
        const originalLoadResults = window.DiagnosticAutoFix.loadResults;
        
        // Enhanced initialization
        window.DiagnosticAutoFix.init = function() {
            // Call original init
            originalInit.call(this);
            
            // Initialize enhanced features
            this.initTieredInterface();
            this.initPreviewSystem();
            this.initSchedulingSystem();
            this.bindEnhancedEvents();
        };
        
        // Enhanced result loading
        window.DiagnosticAutoFix.loadResults = function() {
            // Call original loadResults
            originalLoadResults.call(this);
            
            // Enhance results with tier data
            this.enhanceResultsWithTierData();
        };
        
        // Add new methods to existing object
        $.extend(window.DiagnosticAutoFix, {
            
            /**
             * Initialize tiered interface
             */
            initTieredInterface: function() {
                this.createTierTabs();
                this.createCategoryFilters();
                this.updateFixCards();
            },
            
            /**
             * Create tier navigation tabs
             */
            createTierTabs: function() {
                const $container = $('.diagnostic-results-container');
                if ($container.length === 0) return;
                
                const tierTabsHtml = `
                    <div class="redco-tier-tabs" style="margin-bottom: 20px;">
                        <button class="redco-tier-tab active" data-tier="all">
                            <span class="tier-icon">🔍</span>
                            <span class="tier-name">All Fixes</span>
                            <span class="tier-count" id="all-count">0</span>
                        </button>
                        <button class="redco-tier-tab" data-tier="safe">
                            <span class="tier-icon">🟢</span>
                            <span class="tier-name">Safe</span>
                            <span class="tier-count" id="safe-count">0</span>
                        </button>
                        <button class="redco-tier-tab" data-tier="moderate">
                            <span class="tier-icon">🟡</span>
                            <span class="tier-name">Moderate</span>
                            <span class="tier-count" id="moderate-count">0</span>
                        </button>
                        <button class="redco-tier-tab" data-tier="advanced">
                            <span class="tier-icon">🔴</span>
                            <span class="tier-name">Advanced</span>
                            <span class="tier-count" id="advanced-count">0</span>
                        </button>
                    </div>
                `;
                
                $container.prepend(tierTabsHtml);
                this.updateTierCounts();
            },
            
            /**
             * Create category filters
             */
            createCategoryFilters: function() {
                const $container = $('.redco-tier-tabs');
                if ($container.length === 0) return;
                
                const categoryFiltersHtml = `
                    <div class="redco-category-filters" style="margin-top: 15px;">
                        <label>Filter by category:</label>
                        <select id="category-filter">
                            <option value="all">All Categories</option>
                            <option value="database">Database</option>
                            <option value="frontend">Frontend</option>
                            <option value="server">Server</option>
                            <option value="security">Security</option>
                            <option value="seo">SEO</option>
                            <option value="images">Images</option>
                        </select>
                    </div>
                `;
                
                $container.append(categoryFiltersHtml);
            },
            
            /**
             * Update fix cards with tier information
             */
            updateFixCards: function() {
                const self = this;
                
                $('.issue-item').each(function() {
                    const $card = $(this);
                    const issueId = $card.data('issue-id');
                    
                    if (!issueId) return;
                    
                    // Get issue data
                    const issue = self.findIssueById(issueId);
                    if (!issue) return;
                    
                    // Add tier indicator
                    self.addTierIndicator($card, issue);
                    
                    // Add safety information
                    self.addSafetyInfo($card, issue);
                    
                    // Add preview and schedule buttons
                    self.addEnhancedButtons($card, issue);
                });
            },
            
            /**
             * Add tier indicator to fix card
             */
            addTierIndicator: function($card, issue) {
                const tier = issue.tier || 'safe';
                const tierConfig = this.getTierConfig(tier);
                
                if (!tierConfig) return;
                
                const tierIndicatorHtml = `
                    <div class="tier-indicator tier-${tier}" style="
                        position: absolute;
                        top: 10px;
                        right: 10px;
                        background: ${tierConfig.color};
                        color: white;
                        padding: 4px 8px;
                        border-radius: 12px;
                        font-size: 11px;
                        font-weight: bold;
                    ">
                        ${tierConfig.icon} ${tier.toUpperCase()}
                    </div>
                `;
                
                $card.css('position', 'relative').prepend(tierIndicatorHtml);
            },
            
            /**
             * Add safety information to fix card
             */
            addSafetyInfo: function($card, issue) {
                const safetyInfo = issue.safety_info || {};
                const $description = $card.find('.issue-description');
                
                if ($description.length === 0) return;
                
                let safetyHtml = '<div class="safety-indicators" style="margin-top: 10px;">';
                
                if (safetyInfo.backup_required) {
                    safetyHtml += '<span class="safety-badge backup-required">🛡️ Backup Required</span>';
                }
                
                if (safetyInfo.reversible) {
                    safetyHtml += '<span class="safety-badge reversible">↩️ Reversible</span>';
                }
                
                if (safetyInfo.warnings && safetyInfo.warnings.length > 0) {
                    safetyHtml += '<span class="safety-badge warnings">⚠️ Has Warnings</span>';
                }
                
                safetyHtml += '</div>';
                
                $description.after(safetyHtml);
            },
            
            /**
             * Add enhanced buttons (Preview, Schedule)
             */
            addEnhancedButtons: function($card, issue) {
                const $actions = $card.find('.issue-actions');
                if ($actions.length === 0) return;
                
                const enhancedButtonsHtml = `
                    <button class="button button-secondary preview-fix" data-fix-id="${issue.id}">
                        Preview
                    </button>
                    <button class="button schedule-fix" data-fix-id="${issue.id}">
                        Schedule
                    </button>
                `;
                
                $actions.prepend(enhancedButtonsHtml);
            },
            
            /**
             * Initialize preview system
             */
            initPreviewSystem: function() {
                this.createPreviewModal();
            },
            
            /**
             * Create preview modal
             */
            createPreviewModal: function() {
                if ($('#redco-preview-modal').length > 0) return;
                
                const previewModalHtml = `
                    <div id="redco-preview-modal" class="redco-modal" style="display: none;">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h2>Fix Preview</h2>
                                <button class="modal-close">&times;</button>
                            </div>
                            
                            <div class="modal-body">
                                <div class="preview-tabs">
                                    <button class="preview-tab active" data-tab="overview">Overview</button>
                                    <button class="preview-tab" data-tab="changes">Changes</button>
                                    <button class="preview-tab" data-tab="impact">Impact</button>
                                </div>
                                
                                <div class="preview-content">
                                    <div class="preview-panel active" id="preview-overview">
                                        <div class="before-after-comparison">
                                            <div class="comparison-section">
                                                <h3>Current State</h3>
                                                <div id="current-metrics"></div>
                                            </div>
                                            <div class="comparison-arrow">→</div>
                                            <div class="comparison-section">
                                                <h3>After Fix</h3>
                                                <div id="projected-metrics"></div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="preview-panel" id="preview-changes">
                                        <div class="changes-list"></div>
                                    </div>
                                    
                                    <div class="preview-panel" id="preview-impact">
                                        <div class="impact-analysis"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="modal-footer">
                                <button class="button button-secondary cancel-preview">Cancel</button>
                                <button class="button button-primary apply-after-preview">Apply Fix</button>
                            </div>
                        </div>
                    </div>
                `;
                
                $('body').append(previewModalHtml);
            },
            
            /**
             * Initialize scheduling system
             */
            initSchedulingSystem: function() {
                this.createSchedulingModal();
            },
            
            /**
             * Create scheduling modal
             */
            createSchedulingModal: function() {
                if ($('#redco-scheduling-modal').length > 0) return;
                
                const schedulingModalHtml = `
                    <div id="redco-scheduling-modal" class="redco-modal" style="display: none;">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h2>Schedule Fix Application</h2>
                                <button class="modal-close">&times;</button>
                            </div>
                            
                            <div class="modal-body">
                                <div class="schedule-options">
                                    <label class="schedule-option">
                                        <input type="radio" name="schedule_type" value="immediate" checked>
                                        <span class="option-content">
                                            <strong>Apply Immediately</strong>
                                            <small>Execute the fix right now</small>
                                        </span>
                                    </label>
                                    
                                    <label class="schedule-option">
                                        <input type="radio" name="schedule_type" value="maintenance_window">
                                        <span class="option-content">
                                            <strong>Next Maintenance Window</strong>
                                            <small>Sunday 2:00 AM (in 3 days)</small>
                                        </span>
                                    </label>
                                    
                                    <label class="schedule-option">
                                        <input type="radio" name="schedule_type" value="low_traffic">
                                        <span class="option-content">
                                            <strong>Low Traffic Period</strong>
                                            <small>Tonight at 3:00 AM</small>
                                        </span>
                                    </label>
                                    
                                    <label class="schedule-option">
                                        <input type="radio" name="schedule_type" value="custom_time">
                                        <span class="option-content">
                                            <strong>Custom Date & Time</strong>
                                            <small>Choose specific date and time</small>
                                        </span>
                                    </label>
                                </div>
                                
                                <div class="custom-datetime" style="display: none;">
                                    <input type="datetime-local" id="custom-datetime" class="regular-text">
                                </div>
                                
                                <div class="notification-settings">
                                    <h3>Notifications</h3>
                                    <label>
                                        <input type="checkbox" id="email-notifications" checked>
                                        Email notifications
                                    </label>
                                    <input type="email" id="notification-email" placeholder="<EMAIL>" class="regular-text">
                                </div>
                            </div>
                            
                            <div class="modal-footer">
                                <button class="button button-secondary cancel-schedule">Cancel</button>
                                <button class="button button-primary confirm-schedule">Schedule Fix</button>
                            </div>
                        </div>
                    </div>
                `;
                
                $('body').append(schedulingModalHtml);
            },
            
            /**
             * Bind enhanced event handlers
             */
            bindEnhancedEvents: function() {
                const self = this;
                
                // Tier tab switching
                $(document).on('click', '.redco-tier-tab', function() {
                    const tier = $(this).data('tier');
                    self.switchTier(tier);
                });
                
                // Category filtering
                $(document).on('change', '#category-filter', function() {
                    const category = $(this).val();
                    self.filterByCategory(category);
                });
                
                // Preview buttons
                $(document).on('click', '.preview-fix', function() {
                    const fixId = $(this).data('fix-id');
                    self.showFixPreview(fixId);
                });
                
                // Schedule buttons
                $(document).on('click', '.schedule-fix', function() {
                    const fixId = $(this).data('fix-id');
                    self.showSchedulingModal(fixId);
                });
                
                // Modal close buttons
                $(document).on('click', '.modal-close, .cancel-preview, .cancel-schedule', function() {
                    $(this).closest('.redco-modal').hide();
                });
                
                // Schedule type change
                $(document).on('change', 'input[name="schedule_type"]', function() {
                    const scheduleType = $(this).val();
                    $('.custom-datetime').toggle(scheduleType === 'custom_time');
                });
                
                // Confirm schedule
                $(document).on('click', '.confirm-schedule', function() {
                    self.confirmSchedule();
                });
                
                // Apply after preview
                $(document).on('click', '.apply-after-preview', function() {
                    self.applyAfterPreview();
                });
            },
            
            /**
             * Switch tier view
             */
            switchTier: function(tier) {
                $('.redco-tier-tab').removeClass('active');
                $(`.redco-tier-tab[data-tier="${tier}"]`).addClass('active');
                
                if (tier === 'all') {
                    $('.issue-item').show();
                } else {
                    $('.issue-item').hide();
                    $(`.issue-item[data-tier="${tier}"]`).show();
                }
            },
            
            /**
             * Filter by category
             */
            filterByCategory: function(category) {
                if (category === 'all') {
                    $('.issue-item').show();
                } else {
                    $('.issue-item').hide();
                    $(`.issue-item[data-category="${category}"]`).show();
                }
            },
            
            /**
             * Show fix preview
             */
            showFixPreview: function(fixId) {
                const self = this;
                
                // Show loading state
                $('#redco-preview-modal').show();
                $('.preview-content').html('<div class="loading">Loading preview...</div>');
                
                // Make AJAX request for preview
                $.ajax({
                    url: redcoAjax.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'redco_preview_fix',
                        nonce: redcoAjax.nonce,
                        fix_id: fixId
                    },
                    success: function(response) {
                        if (response.success) {
                            self.displayPreviewResults(response.data);
                        } else {
                            self.showError('Preview failed: ' + response.data);
                        }
                    },
                    error: function() {
                        self.showError('Preview request failed');
                    }
                });
            },
            
            /**
             * Display preview results
             */
            displayPreviewResults: function(previewData) {
                // Update overview tab
                $('#current-metrics').html(this.formatMetrics(previewData.before_metrics));
                $('#projected-metrics').html(this.formatMetrics(previewData.simulated_after_metrics));
                
                // Update changes tab
                $('.changes-list').html(this.formatChanges(previewData));
                
                // Update impact tab
                $('.impact-analysis').html(this.formatImpact(previewData.estimated_impact));
                
                // Store preview data for later use
                this.currentPreviewData = previewData;
            },
            
            /**
             * Show scheduling modal
             */
            showSchedulingModal: function(fixId) {
                this.currentScheduleFixId = fixId;
                $('#redco-scheduling-modal').show();
            },
            
            /**
             * Confirm schedule
             */
            confirmSchedule: function() {
                const self = this;
                const scheduleType = $('input[name="schedule_type"]:checked').val();
                const customDatetime = $('#custom-datetime').val();
                const emailNotifications = $('#email-notifications').is(':checked');
                const notificationEmail = $('#notification-email').val();
                
                $.ajax({
                    url: redcoAjax.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'redco_schedule_fix',
                        nonce: redcoAjax.nonce,
                        fix_id: self.currentScheduleFixId,
                        schedule_type: scheduleType,
                        custom_datetime: customDatetime,
                        email_notifications: emailNotifications,
                        notification_email: notificationEmail
                    },
                    success: function(response) {
                        if (response.success) {
                            self.showToast('Fix Scheduled', 'Fix has been scheduled successfully', 'success');
                            $('#redco-scheduling-modal').hide();
                        } else {
                            self.showError('Scheduling failed: ' + response.data);
                        }
                    },
                    error: function() {
                        self.showError('Scheduling request failed');
                    }
                });
            },
            
            /**
             * Helper methods
             */
            getTierConfig: function(tier) {
                const tierConfigs = {
                    'safe': { color: '#28a745', icon: '🟢' },
                    'moderate': { color: '#ffc107', icon: '🟡' },
                    'advanced': { color: '#dc3545', icon: '🔴' }
                };
                
                return tierConfigs[tier];
            },
            
            findIssueById: function(issueId) {
                if (!this.currentResults || !this.currentResults.issues) {
                    return null;
                }
                
                return this.currentResults.issues.find(issue => issue.id === issueId);
            },
            
            updateTierCounts: function() {
                if (!this.currentResults || !this.currentResults.issues) {
                    return;
                }
                
                const counts = { all: 0, safe: 0, moderate: 0, advanced: 0 };
                
                this.currentResults.issues.forEach(issue => {
                    counts.all++;
                    const tier = issue.tier || 'safe';
                    if (counts[tier] !== undefined) {
                        counts[tier]++;
                    }
                });
                
                Object.keys(counts).forEach(tier => {
                    $(`#${tier}-count`).text(counts[tier]);
                });
            },
            
            enhanceResultsWithTierData: function() {
                // Update tier counts when results are loaded
                this.updateTierCounts();
                
                // Update fix cards with tier information
                this.updateFixCards();
            }
        });
    }
    
})(jQuery);
