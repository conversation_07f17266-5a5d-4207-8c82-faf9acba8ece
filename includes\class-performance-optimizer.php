<?php
/**
 * Performance Optimizer for Redco Optimizer
 *
 * Optimizes plugin performance by reducing overhead and database queries
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Performance_Optimizer {

    /**
     * Initialize performance optimizations
     */
    public function init() {
        // Optimize database queries
        add_action('init', array($this, 'optimize_database_queries'), 1);
        
        // Reduce admin overhead
        add_action('admin_init', array($this, 'optimize_admin_performance'));
        
        // Optimize frontend performance
        add_action('wp', array($this, 'optimize_frontend_performance'));
        
        // Clean up transients
        add_action('wp_scheduled_delete', array($this, 'cleanup_expired_transients'));
    }

    /**
     * Optimize database queries
     */
    public function optimize_database_queries() {
        // Cache module settings to reduce repeated queries
        $this->cache_module_settings();
        
        // Optimize option autoloading
        $this->optimize_option_autoloading();
    }

    /**
     * Cache module settings to reduce database queries
     */
    private function cache_module_settings() {
        $cache_key = 'redco_cached_module_settings';
        $cached_settings = wp_cache_get($cache_key);
        
        if ($cached_settings === false) {
            $enabled_modules = get_option('redco_optimizer_options', array())['modules_enabled'] ?? array();
            
            $module_settings = array();
            foreach ($enabled_modules as $module) {
                $module_settings[$module] = get_option("redco_{$module}_options", array());
            }
            
            // Cache for 1 hour
            wp_cache_set($cache_key, $module_settings, '', HOUR_IN_SECONDS);
        }
    }

    /**
     * PERFORMANCE OPTIMIZED: Optimize option autoloading
     */
    private function optimize_option_autoloading() {
        // PERFORMANCE: Expanded list of large options that should not autoload
        $large_options = array(
            'redco_minifier_stats',
            'redco_lazy_load_stats',
            'redco_page_cache_stats',
            'redco_performance_metrics',
            'redco_webp_conversion_stats',
            'redco_database_cleanup_stats',
            'redco_diagnostic_results',
            'redco_performance_history',
            'redco_slow_queries',
            'redco_performance_issues',
            'redco_module_usage_stats',
            'redco_api_cache_data',
            'redco_pagespeed_history'
        );

        // PERFORMANCE: Batch update to reduce database calls
        global $wpdb;
        $option_names = "'" . implode("','", array_map('esc_sql', $large_options)) . "'";

        $wpdb->query("
            UPDATE {$wpdb->options}
            SET autoload = 'no'
            WHERE option_name IN ({$option_names})
            AND autoload = 'yes'
        ");

        // PERFORMANCE: Clear object cache for these options
        foreach ($large_options as $option) {
            wp_cache_delete($option, 'options');
        }
    }

    /**
     * Optimize admin performance
     */
    public function optimize_admin_performance() {
        // Disable heavy admin features on non-plugin pages
        if (!$this->is_redco_admin_page()) {
            remove_action('admin_enqueue_scripts', array($this, 'enqueue_heavy_scripts'));
        }
        
        // Reduce admin AJAX frequency
        add_filter('heartbeat_settings', array($this, 'optimize_heartbeat_settings'));
    }

    /**
     * Optimize frontend performance
     */
    public function optimize_frontend_performance() {
        // Skip optimization for admin users to reduce overhead
        if (current_user_can('manage_options')) {
            return;
        }
        
        // Disable debug logging on frontend for non-debug mode
        if (!defined('WP_DEBUG') || !WP_DEBUG) {
            add_filter('redco_enable_logging', '__return_false');
        }
        
        // Optimize module loading
        $this->optimize_module_loading();
    }

    /**
     * Optimize module loading
     */
    private function optimize_module_loading() {
        // Only load modules that are actually needed for current request
        $enabled_modules = get_option('redco_optimizer_options', array())['modules_enabled'] ?? array();

        // Skip heavy modules on AJAX requests unless specifically needed
        if (wp_doing_ajax()) {
            $current_action = $_POST['action'] ?? '';
            $heavy_modules = array('asset-optimization', 'smart-webp-conversion');

            // Only skip if the AJAX action doesn't require these modules
            $asset_actions = array('redco_clear_asset_cache', 'redco_generate_critical_css', 'redco_optimize_assets');
            $webp_actions = array('redco_webp_convert_batch', 'redco_webp_get_stats', 'redco_webp_restore_images');

            if (!in_array($current_action, array_merge($asset_actions, $webp_actions))) {
                foreach ($heavy_modules as $module) {
                    if (($key = array_search($module, $enabled_modules)) !== false) {
                        unset($enabled_modules[$key]);
                    }
                }
            }
        }

        // Skip non-essential modules on frontend for better performance
        if (!is_admin() && !wp_doing_ajax()) {
            $admin_only_modules = array('diagnostic-autofix', 'database-cleanup');
            foreach ($admin_only_modules as $module) {
                if (($key = array_search($module, $enabled_modules)) !== false) {
                    unset($enabled_modules[$key]);
                }
            }
        }

        // Cache the optimized module list
        wp_cache_set('redco_optimized_modules', $enabled_modules, '', HOUR_IN_SECONDS);

        return $enabled_modules;
    }

    /**
     * Get optimized module list for current request
     */
    public static function get_optimized_modules() {
        // Check cache first
        $cached_modules = wp_cache_get('redco_optimized_modules', '');
        if ($cached_modules !== false) {
            return $cached_modules;
        }

        // Create instance and get optimized modules
        $optimizer = new self();
        return $optimizer->optimize_module_loading();
    }

    /**
     * Optimize heartbeat settings
     */
    public function optimize_heartbeat_settings($settings) {
        // Reduce heartbeat frequency to reduce server load
        $settings['interval'] = 60; // 60 seconds instead of default 15
        return $settings;
    }

    /**
     * Check if current admin page is Redco Optimizer page
     */
    private function is_redco_admin_page() {
        $screen = get_current_screen();
        return $screen && strpos($screen->id, 'redco-optimizer') !== false;
    }

    /**
     * Clean up expired transients
     */
    public function cleanup_expired_transients() {
        global $wpdb;
        
        // Clean up Redco-specific transients
        $wpdb->query("
            DELETE FROM {$wpdb->options} 
            WHERE option_name LIKE '_transient_timeout_redco_%' 
            AND option_value < UNIX_TIMESTAMP()
        ");
        
        $wpdb->query("
            DELETE FROM {$wpdb->options} 
            WHERE option_name LIKE '_transient_redco_%' 
            AND option_name NOT IN (
                SELECT CONCAT('_transient_', SUBSTRING(option_name, 19))
                FROM {$wpdb->options} o2
                WHERE o2.option_name LIKE '_transient_timeout_redco_%'
            )
        ");
    }

    /**
     * Get performance metrics
     */
    public function get_performance_metrics() {
        return array(
            'database_queries' => get_num_queries(),
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
            'execution_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'],
            'cached_objects' => wp_cache_get_stats(),
            'optimizations_active' => $this->count_active_optimizations()
        );
    }

    /**
     * Count active optimizations
     */
    private function count_active_optimizations() {
        $optimizations = 0;
        
        // Check if caching is working
        if (wp_cache_get('redco_cached_module_settings') !== false) {
            $optimizations++;
        }
        
        // Check if transient cleanup is scheduled
        if (wp_next_scheduled('wp_scheduled_delete')) {
            $optimizations++;
        }
        
        // Check if heartbeat is optimized
        if (has_filter('heartbeat_settings')) {
            $optimizations++;
        }
        
        return $optimizations;
    }

    /**
     * Emergency performance mode - disable heavy features
     */
    public function enable_emergency_mode() {
        // Disable heavy modules temporarily
        $heavy_modules = array(
            'css-js-minifier',
            'critical-resource-optimizer',
            'database-cleanup'
        );
        
        $options = get_option('redco_optimizer_options', array());
        $options['emergency_mode'] = true;
        $options['disabled_modules'] = $heavy_modules;
        
        update_option('redco_optimizer_options', $options);
        
        // Clear all caches
        wp_cache_flush();
        
        // Set emergency mode transient
        set_transient('redco_emergency_mode', true, HOUR_IN_SECONDS);
    }

    /**
     * Disable emergency mode
     */
    public function disable_emergency_mode() {
        $options = get_option('redco_optimizer_options', array());
        unset($options['emergency_mode']);
        unset($options['disabled_modules']);
        
        update_option('redco_optimizer_options', $options);
        delete_transient('redco_emergency_mode');
    }

    /**
     * Check if emergency mode is active
     */
    public function is_emergency_mode() {
        return get_transient('redco_emergency_mode') !== false;
    }
}

// Initialize performance optimizer
$redco_performance_optimizer = new Redco_Performance_Optimizer();
$redco_performance_optimizer->init();
