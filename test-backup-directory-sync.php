<?php
// Test backup directory synchronization between engine and main diagnostic class
require_once('d:/xampp/htdocs/wordpress/wp-config.php');
require_once('includes/helpers.php');
require_once('modules/diagnostic-autofix/class-diagnostic-autofix-engine.php');
require_once('modules/diagnostic-autofix/class-diagnostic-autofix.php');

echo "=== TESTING BACKUP DIRECTORY SYNCHRONIZATION ===\n";

// Create engine instance
$engine = new Redco_Diagnostic_AutoFix_Engine();

// Create diagnostic instance
$diagnostic = new Redco_Diagnostic_AutoFix();

// Use reflection to access private methods
$engine_reflection = new ReflectionClass($engine);
$diagnostic_reflection = new ReflectionClass($diagnostic);

// Get engine backup directory
$engine_backup_dir_property = $engine_reflection->getProperty('backup_dir');
$engine_backup_dir_property->setAccessible(true);
$engine_backup_dir = $engine_backup_dir_property->getValue($engine);

echo "Engine backup directory: $engine_backup_dir\n";

// Get engine candidates
$engine_candidates_method = $engine_reflection->getMethod('get_backup_directory_candidates');
$engine_candidates_method->setAccessible(true);
$engine_candidates = $engine_candidates_method->invoke($engine);

echo "\nEngine backup directory candidates:\n";
foreach ($engine_candidates as $candidate) {
    echo "  - {$candidate['path']} ({$candidate['reason']})\n";
}

// Get diagnostic possible directories
$diagnostic_dirs_method = $diagnostic_reflection->getMethod('get_possible_backup_directories');
$diagnostic_dirs_method->setAccessible(true);
$diagnostic_dirs = $diagnostic_dirs_method->invoke($diagnostic);

echo "\nDiagnostic possible backup directories:\n";
foreach ($diagnostic_dirs as $dir) {
    echo "  - $dir\n";
}

// Check if engine backup directory is in diagnostic possible directories
$engine_backup_normalized = wp_normalize_path(trailingslashit($engine_backup_dir));
$is_synchronized = in_array($engine_backup_normalized, $diagnostic_dirs);

echo "\nSynchronization check:\n";
echo "Engine backup dir (normalized): $engine_backup_normalized\n";
echo "Is in diagnostic possible dirs: " . ($is_synchronized ? 'YES' : 'NO') . "\n";

if (!$is_synchronized) {
    echo "\n❌ SYNCHRONIZATION ISSUE DETECTED!\n";
    echo "The engine is using a backup directory that the diagnostic class doesn't check.\n";
    echo "This will cause rollback validation failures.\n";
} else {
    echo "\n✅ SYNCHRONIZATION OK!\n";
    echo "The engine backup directory is included in diagnostic possible directories.\n";
}

// Test actual backup validation
echo "\n=== TESTING BACKUP VALIDATION ===\n";

// Check if the engine backup directory exists and has any backups
if (is_dir($engine_backup_dir)) {
    $backups = glob($engine_backup_dir . '*');
    echo "Found " . count($backups) . " items in engine backup directory\n";
    
    if (!empty($backups)) {
        // Test validation on the first backup
        $first_backup = $backups[0];
        if (is_dir($first_backup)) {
            $backup_id = basename($first_backup);
            echo "Testing validation on backup: $backup_id\n";
            
            // Test engine validation
            $engine_validate_method = $engine_reflection->getMethod('validate_backup_for_rollback');
            $engine_validate_method->setAccessible(true);
            $engine_validation = $engine_validate_method->invoke($engine, $backup_id);
            
            echo "Engine validation result: " . ($engine_validation['valid'] ? 'VALID' : 'INVALID') . "\n";
            if (!$engine_validation['valid']) {
                echo "Engine validation reason: " . $engine_validation['reason'] . "\n";
            }
            
            // Test diagnostic validation
            $diagnostic_validate_method = $diagnostic_reflection->getMethod('validate_backup_exists');
            $diagnostic_validate_method->setAccessible(true);
            $diagnostic_validation = $diagnostic_validate_method->invoke($diagnostic, $backup_id);
            
            echo "Diagnostic validation result: " . ($diagnostic_validation ? 'VALID' : 'INVALID') . "\n";
            
            if ($engine_validation['valid'] !== $diagnostic_validation) {
                echo "\n❌ VALIDATION MISMATCH DETECTED!\n";
                echo "Engine and diagnostic classes have different validation results.\n";
            } else {
                echo "\n✅ VALIDATION SYNCHRONIZED!\n";
                echo "Both engine and diagnostic classes agree on backup validation.\n";
            }
        }
    } else {
        echo "No backups found to test validation.\n";
    }
} else {
    echo "Engine backup directory does not exist: $engine_backup_dir\n";
}

echo "\n=== TEST COMPLETE ===\n";
