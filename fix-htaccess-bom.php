<?php
/**
 * Emergency BOM Removal Script for .htaccess
 * This script removes the Byte Order Mark (BOM) from .htaccess file
 * Run this script once and then delete it immediately for security
 */

// Security check - only run if accessed directly
if (!isset($_GET['run']) || $_GET['run'] !== 'fix-bom') {
    die('🚨 EMERGENCY BOM FIX SCRIPT<br><br>
    <strong>WARNING:</strong> This script will fix the BOM issue in your .htaccess file.<br><br>
    <a href="?run=fix-bom" style="background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
        🔧 Click here to fix .htaccess BOM issue
    </a><br><br>
    <small>After running this script, <strong>DELETE THIS FILE IMMEDIATELY</strong> for security!</small>');
}

echo "<h2>🔧 Emergency .htaccess BOM Fix</h2>";
echo "<p>Starting BOM removal process...</p>";

$htaccess_file = '.htaccess';

// Check if .htaccess exists
if (!file_exists($htaccess_file)) {
    echo "<p style='color: red;'>❌ Error: .htaccess file not found!</p>";
    exit;
}

// Read current content
$original_content = file_get_contents($htaccess_file);
if ($original_content === false) {
    echo "<p style='color: red;'>❌ Error: Could not read .htaccess file!</p>";
    exit;
}

echo "<p>📄 Original file size: " . strlen($original_content) . " bytes</p>";

// Check for BOM
$has_bom = false;
if (substr($original_content, 0, 3) === "\xEF\xBB\xBF") {
    $has_bom = true;
    echo "<p style='color: orange;'>⚠️ BOM detected at beginning of file!</p>";
} else {
    echo "<p style='color: green;'>✅ No BOM detected at beginning of file</p>";
}

// Remove BOM from beginning
$cleaned_content = $original_content;
if ($has_bom) {
    $cleaned_content = substr($original_content, 3);
    echo "<p style='color: blue;'>🧹 Removed BOM from beginning of file</p>";
}

// Also remove any other potential BOM occurrences
$bom_count = 0;
$cleaned_content = str_replace("\xEF\xBB\xBF", '', $cleaned_content, $bom_count);
if ($bom_count > 0) {
    echo "<p style='color: blue;'>🧹 Removed $bom_count additional BOM occurrences</p>";
}

// Clean up any extra whitespace at the beginning
$cleaned_content = ltrim($cleaned_content);

echo "<p>📄 Cleaned file size: " . strlen($cleaned_content) . " bytes</p>";

// Create backup
$backup_file = '.htaccess.backup.' . date('Y-m-d-H-i-s');
if (file_put_contents($backup_file, $original_content)) {
    echo "<p style='color: green;'>💾 Backup created: $backup_file</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Warning: Could not create backup file</p>";
}

// Write cleaned content
if (file_put_contents($htaccess_file, $cleaned_content)) {
    echo "<p style='color: green;'>✅ Successfully wrote cleaned .htaccess file!</p>";
    
    // Verify the fix
    $verify_content = file_get_contents($htaccess_file);
    if (substr($verify_content, 0, 3) !== "\xEF\xBB\xBF") {
        echo "<p style='color: green;'>✅ Verification: BOM successfully removed!</p>";
        echo "<p style='color: green;'><strong>🎉 Your website should now work properly!</strong></p>";
    } else {
        echo "<p style='color: red;'>❌ Verification failed: BOM still present</p>";
    }
} else {
    echo "<p style='color: red;'>❌ Error: Could not write to .htaccess file!</p>";
    echo "<p>Please check file permissions and try again.</p>";
}

echo "<hr>";
echo "<h3>📋 Current .htaccess content preview:</h3>";
echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: auto;'>";
echo htmlspecialchars(substr($cleaned_content, 0, 1000));
if (strlen($cleaned_content) > 1000) {
    echo "\n... (content truncated)";
}
echo "</pre>";

echo "<hr>";
echo "<p style='color: red; font-weight: bold; font-size: 18px;'>🚨 IMPORTANT: DELETE THIS SCRIPT FILE IMMEDIATELY!</p>";
echo "<p>For security reasons, delete this file (fix-htaccess-bom.php) from your server right now.</p>";
echo "<p><a href='/' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 Go to your website</a></p>";
?>
