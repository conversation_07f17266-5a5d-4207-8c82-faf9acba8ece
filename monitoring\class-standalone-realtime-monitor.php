<?php
/**
 * Standalone Real-time Monitor for Redco Diagnostic & Auto-Fix
 * 
 * Phase 2 Enhancement: Real-time performance monitoring and alerting (standalone version)
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Standalone_Realtime_Monitor {
    
    private $table_name;
    private $alert_thresholds;
    
    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'redco_diagnostic_performance_metrics';
        
        $this->alert_thresholds = array(
            'page_load_time' => 3.0, // seconds
            'memory_usage' => 128 * 1024 * 1024, // 128MB
            'database_queries' => 50,
            'error_rate' => 0.05 // 5%
        );
    }
    
    /**
     * Initialize the real-time monitor
     */
    public function init() {
        // AJAX handlers
        add_action('wp_ajax_redco_diagnostic_get_realtime_metrics', array($this, 'ajax_get_realtime_metrics'));
        add_action('wp_ajax_redco_diagnostic_get_performance_history', array($this, 'ajax_get_performance_history'));
        
        // Hooks for metric collection
        add_action('wp_footer', array($this, 'collect_frontend_metrics'));
        add_action('admin_footer', array($this, 'collect_admin_metrics'));
        add_action('wp_ajax_redco_diagnostic_record_metric', array($this, 'ajax_record_metric'));
        add_action('wp_ajax_nopriv_redco_diagnostic_record_metric', array($this, 'ajax_record_metric'));
        
        // Scheduled monitoring
        add_action('redco_diagnostic_collect_metrics', array($this, 'collect_scheduled_metrics'));
        
        // Schedule metric collection if not already scheduled
        if (!wp_next_scheduled('redco_diagnostic_collect_metrics')) {
            wp_schedule_event(time(), 'hourly', 'redco_diagnostic_collect_metrics');
        }
        
        // Create database table
        $this->maybe_create_table();
    }
    
    /**
     * Collect current metrics
     */
    public function collect_current_metrics() {
        $metrics = array(
            'timestamp' => time(),
            'page_load_time' => $this->measure_page_load_time(),
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
            'database_queries' => $this->count_database_queries(),
            'cache_hit_ratio' => $this->calculate_cache_hit_ratio(),
            'error_rate' => $this->calculate_error_rate(),
            'active_plugins' => count(get_option('active_plugins', array())),
            'theme' => get_template(),
            'php_version' => PHP_VERSION,
            'wordpress_version' => get_bloginfo('version'),
            'server_response_time' => $this->measure_server_response_time(),
            'database_size' => redco_diagnostic_get_database_size(),
            'autoload_size' => redco_diagnostic_get_autoload_size()
        );
        
        // Add Core Web Vitals if available
        if (class_exists('Redco_Standalone_Core_Web_Vitals')) {
            $vitals = new Redco_Standalone_Core_Web_Vitals();
            $metrics['core_web_vitals'] = $vitals->get_current_vitals();
        }
        
        return $metrics;
    }
    
    /**
     * AJAX: Get real-time metrics
     */
    public function ajax_get_realtime_metrics() {
        if (!redco_diagnostic_verify_nonce($_POST['nonce'] ?? '')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $metrics = $this->collect_current_metrics();
        
        // Store metrics in database
        $this->store_metrics($metrics);
        
        wp_send_json_success($metrics);
    }
    
    /**
     * Store metrics in database
     */
    private function store_metrics($metrics) {
        global $wpdb;
        
        $wpdb->insert(
            $this->table_name,
            array(
                'timestamp' => current_time('mysql'),
                'page_load_time' => $metrics['page_load_time'],
                'memory_usage' => $metrics['memory_usage'],
                'peak_memory' => $metrics['peak_memory'],
                'database_queries' => $metrics['database_queries'],
                'cache_hit_ratio' => $metrics['cache_hit_ratio'],
                'error_rate' => $metrics['error_rate'],
                'server_response_time' => $metrics['server_response_time'],
                'metrics_data' => json_encode($metrics)
            ),
            array('%s', '%f', '%d', '%d', '%d', '%f', '%f', '%f', '%s')
        );
        
        // Cleanup old metrics (keep last 7 days)
        $wpdb->query(
            "DELETE FROM {$this->table_name} 
             WHERE timestamp < DATE_SUB(NOW(), INTERVAL 7 DAY)"
        );
    }
    
    /**
     * Measure page load time
     */
    private function measure_page_load_time() {
        // Use server timing if available
        if (isset($_SERVER['REQUEST_TIME_FLOAT'])) {
            return microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'];
        }
        
        // Fallback estimation
        return 0.5; // Default estimate
    }
    
    /**
     * Count database queries
     */
    private function count_database_queries() {
        global $wpdb;
        
        if (defined('SAVEQUERIES') && SAVEQUERIES && isset($wpdb->queries)) {
            return count($wpdb->queries);
        }
        
        return 0;
    }
    
    /**
     * Calculate cache hit ratio
     */
    private function calculate_cache_hit_ratio() {
        // Check if object cache is available
        if (wp_using_ext_object_cache()) {
            global $wp_object_cache;
            
            if (isset($wp_object_cache->cache_hits) && isset($wp_object_cache->cache_misses)) {
                $total = $wp_object_cache->cache_hits + $wp_object_cache->cache_misses;
                if ($total > 0) {
                    return ($wp_object_cache->cache_hits / $total) * 100;
                }
            }
        }
        
        return 0;
    }
    
    /**
     * Calculate error rate
     */
    private function calculate_error_rate() {
        return 0; // Simplified for now
    }
    
    /**
     * Measure server response time
     */
    private function measure_server_response_time() {
        $start_time = microtime(true);
        
        // Make a simple request to the site
        $response = wp_remote_get(home_url(), array(
            'timeout' => 10,
            'headers' => array('Cache-Control' => 'no-cache')
        ));
        
        $end_time = microtime(true);
        
        if (!is_wp_error($response)) {
            return $end_time - $start_time;
        }
        
        return 0;
    }
    
    /**
     * AJAX: Get performance history
     */
    public function ajax_get_performance_history() {
        if (!redco_diagnostic_verify_nonce($_POST['nonce'] ?? '')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $period = sanitize_text_field($_POST['period'] ?? '24h');
        $history = $this->get_performance_history($period);
        
        wp_send_json_success($history);
    }
    
    /**
     * Get performance history
     */
    public function get_performance_history($period = '24h') {
        global $wpdb;
        
        $interval_map = array(
            '1h' => 'INTERVAL 1 HOUR',
            '24h' => 'INTERVAL 24 HOUR',
            '7d' => 'INTERVAL 7 DAY',
            '30d' => 'INTERVAL 30 DAY'
        );
        
        $interval = $interval_map[$period] ?? 'INTERVAL 24 HOUR';
        
        $history = $wpdb->get_results(
            "SELECT * FROM {$this->table_name} 
             WHERE timestamp >= DATE_SUB(NOW(), {$interval})
             ORDER BY timestamp ASC",
            ARRAY_A
        );
        
        // Process data for charting
        $processed_history = array(
            'labels' => array(),
            'page_load_time' => array(),
            'memory_usage' => array(),
            'database_queries' => array(),
            'cache_hit_ratio' => array()
        );
        
        foreach ($history as $record) {
            $processed_history['labels'][] = date('H:i', strtotime($record['timestamp']));
            $processed_history['page_load_time'][] = floatval($record['page_load_time']);
            $processed_history['memory_usage'][] = intval($record['memory_usage']);
            $processed_history['database_queries'][] = intval($record['database_queries']);
            $processed_history['cache_hit_ratio'][] = floatval($record['cache_hit_ratio']);
        }
        
        return $processed_history;
    }
    
    /**
     * Collect frontend metrics via JavaScript
     */
    public function collect_frontend_metrics() {
        if (is_admin() || wp_doing_ajax()) {
            return;
        }
        
        ?>
        <script>
        (function() {
            // Collect performance metrics
            window.addEventListener('load', function() {
                if (window.performance && window.performance.timing) {
                    var timing = window.performance.timing;
                    var metrics = {
                        page_load_time: (timing.loadEventEnd - timing.navigationStart) / 1000,
                        dom_ready_time: (timing.domContentLoadedEventEnd - timing.navigationStart) / 1000,
                        first_byte_time: (timing.responseStart - timing.navigationStart) / 1000
                    };
                    
                    // Send metrics to server
                    fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: 'action=redco_diagnostic_record_metric&nonce=<?php echo redco_diagnostic_create_nonce(); ?>&metrics=' + encodeURIComponent(JSON.stringify(metrics))
                    });
                }
            });
        })();
        </script>
        <?php
    }
    
    /**
     * AJAX: Record metric from frontend
     */
    public function ajax_record_metric() {
        $metrics_json = $_POST['metrics'] ?? '';
        $metrics = json_decode(stripslashes($metrics_json), true);
        
        if ($metrics && is_array($metrics)) {
            // Store frontend metrics
            $this->store_frontend_metrics($metrics);
        }
        
        wp_die();
    }
    
    /**
     * Store frontend metrics
     */
    private function store_frontend_metrics($metrics) {
        global $wpdb;
        
        $frontend_table = $wpdb->prefix . 'redco_diagnostic_frontend_metrics';
        
        $wpdb->insert(
            $frontend_table,
            array(
                'timestamp' => current_time('mysql'),
                'page_url' => $_SERVER['HTTP_REFERER'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'metrics_data' => json_encode($metrics)
            ),
            array('%s', '%s', '%s', '%s')
        );
    }
    
    /**
     * Get monitoring statistics
     */
    public function get_monitoring_statistics() {
        global $wpdb;
        
        $stats = $wpdb->get_row(
            "SELECT 
                COUNT(*) as total_measurements,
                AVG(page_load_time) as avg_page_load_time,
                AVG(memory_usage) as avg_memory_usage,
                AVG(database_queries) as avg_database_queries,
                AVG(cache_hit_ratio) as avg_cache_hit_ratio
             FROM {$this->table_name}
             WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)",
            ARRAY_A
        );
        
        return $stats ?: array(
            'total_measurements' => 0,
            'avg_page_load_time' => 0,
            'avg_memory_usage' => 0,
            'avg_database_queries' => 0,
            'avg_cache_hit_ratio' => 0
        );
    }
    
    /**
     * Maybe create table
     */
    private function maybe_create_table() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Performance metrics table
        if ($wpdb->get_var("SHOW TABLES LIKE '{$this->table_name}'") != $this->table_name) {
            $sql = "CREATE TABLE {$this->table_name} (
                id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                timestamp datetime NOT NULL,
                page_load_time float NOT NULL DEFAULT 0,
                memory_usage bigint(20) unsigned NOT NULL DEFAULT 0,
                peak_memory bigint(20) unsigned NOT NULL DEFAULT 0,
                database_queries int(11) NOT NULL DEFAULT 0,
                cache_hit_ratio float NOT NULL DEFAULT 0,
                error_rate float NOT NULL DEFAULT 0,
                server_response_time float NOT NULL DEFAULT 0,
                metrics_data longtext,
                PRIMARY KEY (id),
                KEY timestamp (timestamp)
            ) {$charset_collate};";
            
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
        }
        
        // Frontend metrics table
        $frontend_table = $wpdb->prefix . 'redco_diagnostic_frontend_metrics';
        if ($wpdb->get_var("SHOW TABLES LIKE '{$frontend_table}'") != $frontend_table) {
            $sql = "CREATE TABLE {$frontend_table} (
                id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                timestamp datetime NOT NULL,
                page_url varchar(500),
                user_agent text,
                metrics_data longtext,
                PRIMARY KEY (id),
                KEY timestamp (timestamp)
            ) {$charset_collate};";
            
            dbDelta($sql);
        }
    }
    
    // Placeholder methods
    public function collect_admin_metrics() {}
    public function collect_scheduled_metrics() {}
}
