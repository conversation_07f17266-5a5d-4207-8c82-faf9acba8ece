/**
 * CRITICAL FIX: Redco Optimizer Initialization Script
 * Coordinates all modules and utilities for optimal performance
 * Replaces scattered initialization code throughout the application
 */

(function($) {
    'use strict';

    /**
     * Main Redco Optimizer Application
     */
    window.RedcoOptimizer = {
        
        /**
         * Configuration
         */
        config: {
            version: '1.0.0',
            initialized: false,
            modules: []
        },
        
        /**
         * Initialize the application
         */
        init: function() {
            if (this.config.initialized) {
                return;
            }
            
            // CRITICAL FIX: Use debug utility for initialization logging
            if (typeof redcoDebug !== 'undefined' && redcoDebug.enabled) {
                redcoDebug.log('Initializing Redco Optimizer v' + this.config.version);
            }
            
            // Initialize core utilities first
            this.initCoreUtilities();
            
            // Initialize modules
            this.initModules();
            
            // Initialize UI components
            this.initUI();
            
            // Set up global event handlers
            this.bindGlobalEvents();
            
            // Mark as initialized
            this.config.initialized = true;
            
            // Trigger initialization complete event
            $(document).trigger('redco:initialized');
            
            if (typeof redcoDebug !== 'undefined' && redcoDebug.enabled) {
                redcoDebug.log('Redco Optimizer initialization complete');
            }
        },
        
        /**
         * Initialize core utilities
         */
        initCoreUtilities: function() {
            // Initialize debug utilities (already loaded)
            if (typeof redcoDebug !== 'undefined') {
                this.config.modules.push('debug');
            }
            
            // Initialize AJAX utilities
            if (typeof RedcoAjax !== 'undefined') {
                this.config.modules.push('ajax');
            }
            
            // Initialize validation utilities
            if (typeof RedcoValidation !== 'undefined') {
                this.config.modules.push('validation');
            }
            
            // Initialize progress utilities
            if (typeof RedcoProgress !== 'undefined') {
                this.config.modules.push('progress');
            }
        },
        
        /**
         * Initialize modules
         */
        initModules: function() {
            // Initialize auto-save module
            if (typeof RedcoAutoSave !== 'undefined') {
                RedcoAutoSave.init();
                this.config.modules.push('auto-save');
            }
            
            // Initialize performance monitor
            if (typeof RedcoPerformanceMonitor !== 'undefined') {
                RedcoPerformanceMonitor.init();
                this.config.modules.push('performance-monitor');
            }
            
            // Initialize UI utilities
            if (typeof RedcoUIUtils !== 'undefined') {
                RedcoUIUtils.init();
                this.config.modules.push('ui-utils');
            }
        },
        
        /**
         * Initialize UI components
         */
        initUI: function() {
            // Initialize tabs
            this.initTabs();
            
            // Initialize toggles
            this.initToggles();
            
            // Initialize forms
            this.initForms();
            
            // Initialize tooltips and help system
            this.initHelpSystem();
        },
        
        /**
         * Initialize tab system
         */
        initTabs: function() {
            $(document).on('click', '.nav-tab', function(e) {
                e.preventDefault();
                
                const $tab = $(this);
                const targetId = $tab.attr('href');
                
                // Update active tab
                $('.nav-tab').removeClass('nav-tab-active');
                $tab.addClass('nav-tab-active');
                
                // Update active content
                $('.tab-content').removeClass('active');
                $(targetId).addClass('active');
                
                // Update URL hash
                if (history.pushState) {
                    history.pushState(null, null, targetId);
                }
                
                // Trigger tab change event
                $(document).trigger('redco:tab-changed', [targetId]);
            });
            
            // Handle initial tab from URL hash
            if (window.location.hash) {
                const $targetTab = $('.nav-tab[href="' + window.location.hash + '"]');
                if ($targetTab.length) {
                    $targetTab.trigger('click');
                }
            }
        },
        
        /**
         * Initialize toggle switches
         */
        initToggles: function() {
            $(document).on('change', '.toggle-switch input[type="checkbox"]', function() {
                const $checkbox = $(this);
                const $toggle = $checkbox.closest('.toggle-switch');
                
                if ($checkbox.is(':checked')) {
                    $toggle.addClass('active');
                } else {
                    $toggle.removeClass('active');
                }
                
                // Trigger toggle event
                $(document).trigger('redco:toggle-changed', [$checkbox, $checkbox.is(':checked')]);
            });
        },
        
        /**
         * Initialize forms
         */
        initForms: function() {
            // Set up form validation
            $('.redco-form').each(function() {
                const $form = $(this);
                
                if (typeof RedcoValidation !== 'undefined') {
                    RedcoValidation.setupRealTimeValidation($form);
                }
            });
            
            // Handle form submissions
            $(document).on('submit', '.redco-form', function(e) {
                const $form = $(this);
                
                // Validate form if validation utility is available
                if (typeof RedcoValidation !== 'undefined') {
                    const validation = RedcoValidation.validateForm($form);
                    
                    if (!validation.valid) {
                        e.preventDefault();
                        
                        if (typeof showToast === 'function') {
                            showToast('Please fix the form errors before submitting', 'error');
                        }
                        
                        return false;
                    }
                }
            });
        },
        
        /**
         * Initialize help system
         */
        initHelpSystem: function() {
            // Help button handlers
            $(document).on('click', '.help-button', function(e) {
                e.preventDefault();
                
                const $button = $(this);
                const helpContent = $button.data('help') || $button.attr('title');
                
                if (helpContent) {
                    this.showHelp(helpContent, $button);
                }
            });
            
            // Context-sensitive help
            $(document).on('focus', '[data-help]', function() {
                const $field = $(this);
                const helpText = $field.data('help');
                
                if (helpText) {
                    this.showContextHelp(helpText, $field);
                }
            });
        },
        
        /**
         * Show help content
         */
        showHelp: function(content, $trigger) {
            // Implementation would depend on your help system design
            if (typeof showToast === 'function') {
                showToast(content, 'info', 5000);
            }
        },
        
        /**
         * Show context-sensitive help
         */
        showContextHelp: function(content, $field) {
            // Implementation would depend on your help system design
            // Could show in a sidebar, tooltip, or dedicated help area
        },
        
        /**
         * Bind global event handlers
         */
        bindGlobalEvents: function() {
            // Handle window resize
            $(window).on('resize', this.debounce(function() {
                $(document).trigger('redco:window-resized');
            }, 250));
            
            // Handle visibility change
            $(document).on('visibilitychange', function() {
                if (document.hidden) {
                    $(document).trigger('redco:page-hidden');
                } else {
                    $(document).trigger('redco:page-visible');
                }
            });
            
            // Handle before unload
            $(window).on('beforeunload', function() {
                $(document).trigger('redco:before-unload');
            });
        },
        
        /**
         * Get module status
         */
        getModuleStatus: function(moduleName) {
            return this.config.modules.includes(moduleName);
        },
        
        /**
         * Get all loaded modules
         */
        getLoadedModules: function() {
            return this.config.modules.slice(); // Return copy
        },
        
        /**
         * Check if application is initialized
         */
        isInitialized: function() {
            return this.config.initialized;
        },
        
        /**
         * Debounce utility
         */
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    };

    // Auto-initialize when DOM is ready
    $(document).ready(function() {
        RedcoOptimizer.init();
    });

})(jQuery);
