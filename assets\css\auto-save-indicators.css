/**
 * Global Auto-Save Status Indicators
 * Static, non-animated UI elements for auto-save feedback
 */

/* Auto-save status indicator container */
.redco-auto-save-status {
    display: inline-flex;
    align-items: center;
    margin-left: 8px;
    font-size: 12px;
    line-height: 1;
    vertical-align: middle;
}

/* Status indicator icons and text */
.redco-auto-save-status .status-icon {
    width: 14px;
    height: 14px;
    margin-right: 4px;
    display: inline-block;
    border-radius: 50%;
    position: relative;
}

.redco-auto-save-status .status-text {
    color: #666;
    font-weight: 500;
    white-space: nowrap;
}

/* Saving state */
.redco-auto-save-status.saving .status-icon {
    background-color: #ffa500;
    border: 2px solid #ff8c00;
}

.redco-auto-save-status.saving .status-text {
    color: #ff8c00;
}

/* Success state */
.redco-auto-save-status.saved .status-icon {
    background-color: #28a745;
    border: 2px solid #1e7e34;
}

.redco-auto-save-status.saved .status-text {
    color: #1e7e34;
}

/* Error state */
.redco-auto-save-status.error .status-icon {
    background-color: #dc3545;
    border: 2px solid #c82333;
}

.redco-auto-save-status.error .status-text {
    color: #c82333;
}

/* Retry state */
.redco-auto-save-status.retrying .status-icon {
    background-color: #6c757d;
    border: 2px solid #5a6268;
}

.redco-auto-save-status.retrying .status-text {
    color: #5a6268;
}

/* Queued state */
.redco-auto-save-status.queued .status-icon {
    background-color: #17a2b8;
    border: 2px solid #138496;
}

.redco-auto-save-status.queued .status-text {
    color: #138496;
}

/* Warning state */
.redco-auto-save-status.warning .status-icon {
    background-color: #ffc107;
    border: 2px solid #e0a800;
}

.redco-auto-save-status.warning .status-text {
    color: #e0a800;
}

/* Global auto-save notification */
.redco-global-auto-save-notification {
    position: fixed;
    top: 32px;
    right: 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 12px 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 9999;
    max-width: 300px;
    font-size: 13px;
    line-height: 1.4;
}

.redco-global-auto-save-notification.success {
    border-left: 4px solid #28a745;
    background-color: #f8fff9;
}

.redco-global-auto-save-notification.error {
    border-left: 4px solid #dc3545;
    background-color: #fff8f8;
}

.redco-global-auto-save-notification.warning {
    border-left: 4px solid #ffa500;
    background-color: #fffaf0;
}

/* Form field containers with auto-save status */
.redco-form-field-container {
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
}

.redco-form-field-container .form-field {
    flex: 1;
}

.redco-form-field-container .redco-auto-save-status {
    flex-shrink: 0;
}

/* Integration with existing form styles */
.setting-item .redco-auto-save-status,
.form-group .redco-auto-save-status {
    margin-left: 8px;
    vertical-align: middle;
}

/* Accessibility improvements */
.redco-auto-save-status[aria-live] {
    position: absolute;
    left: -10000px;
    width: 1px;
    height: 1px;
    overflow: hidden;
}

.redco-auto-save-status .sr-only {
    position: absolute;
    left: -10000px;
    width: 1px;
    height: 1px;
    overflow: hidden;
}

/* Network status indicator */
.redco-network-status {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 9998;
    display: none;
}

.redco-network-status.offline {
    display: block;
    border-left: 4px solid #dc3545;
    background-color: #fff8f8;
    color: #c82333;
}

.redco-network-status.reconnected {
    display: block;
    border-left: 4px solid #28a745;
    background-color: #f8fff9;
    color: #1e7e34;
}

/* Auto-save progress indicator for forms */
.redco-form-auto-save-progress {
    position: absolute;
    top: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: #f0f0f0;
    border-radius: 1px;
    overflow: hidden;
    opacity: 0;
}

.redco-form-auto-save-progress.active {
    opacity: 1;
}

.redco-form-auto-save-progress .progress-bar {
    height: 100%;
    background: #007cba;
    width: 0%;
    border-radius: 1px;
}

.redco-form-auto-save-progress.saving .progress-bar {
    width: 100%;
    background: #ffa500;
}

.redco-form-auto-save-progress.saved .progress-bar {
    width: 100%;
    background: #28a745;
}

.redco-form-auto-save-progress.error .progress-bar {
    width: 100%;
    background: #dc3545;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .redco-global-auto-save-notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .redco-network-status {
        right: 10px;
        bottom: 10px;
    }
    
    .redco-auto-save-status .status-text {
        display: none;
    }
    
    .redco-auto-save-status .status-icon {
        margin-right: 0;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .redco-auto-save-status .status-icon {
        border-width: 3px;
    }
    
    .redco-global-auto-save-notification {
        border-width: 2px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .redco-auto-save-status,
    .redco-global-auto-save-notification,
    .redco-form-auto-save-progress {
        transition: none;
    }
}

/* Print styles */
@media print {
    .redco-auto-save-status,
    .redco-global-auto-save-notification,
    .redco-network-status,
    .redco-form-auto-save-progress {
        display: none !important;
    }
}
