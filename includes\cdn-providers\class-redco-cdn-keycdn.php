<?php
/**
 * KeyCDN Provider - Phase 4 Implementation
 * 
 * KeyCDN integration with RESTful API and real-time analytics.
 * High-performance CDN with competitive pricing.
 * 
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Load base CDN class
require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/cdn-providers/class-redco-cdn-base.php';

class Redco_CDN_KeyCDN extends Redco_CDN_Base {

    /**
     * Provider name
     */
    protected $provider_name = 'keycdn';

    /**
     * KeyCDN API base URL
     */
    private $api_base = 'https://api.keycdn.com';

    /**
     * Provider capabilities
     */
    protected $capabilities = array(
        'url_rewriting' => true,
        'cache_purging' => true,
        'real_time_analytics' => true,
        'ssl_support' => true,
        'image_optimization' => true,
        'video_streaming' => false,
        'edge_computing' => false,
        'security_features' => true,
        'api_integration' => true
    );

    /**
     * Get configuration fields
     */
    public function get_configuration_fields() {
        return array(
            'api_key' => array(
                'type' => 'password',
                'label' => __('KeyCDN API Key', 'redco-optimizer'),
                'description' => __('Your KeyCDN API key from the account settings', 'redco-optimizer'),
                'required' => true,
                'placeholder' => 'Enter your KeyCDN API key'
            ),
            'zone_id' => array(
                'type' => 'text',
                'label' => __('Zone ID', 'redco-optimizer'),
                'description' => __('Your KeyCDN Zone ID', 'redco-optimizer'),
                'required' => true,
                'placeholder' => 'Enter your Zone ID'
            ),
            'zone_url' => array(
                'type' => 'text',
                'label' => __('Zone URL', 'redco-optimizer'),
                'description' => __('Your KeyCDN zone URL (e.g., myzone.kxcdn.com)', 'redco-optimizer'),
                'required' => true,
                'placeholder' => 'myzone.kxcdn.com'
            ),
            'custom_domain' => array(
                'type' => 'text',
                'label' => __('Custom Domain', 'redco-optimizer'),
                'description' => __('Optional: Custom domain for your CDN (e.g., cdn.mysite.com)', 'redco-optimizer'),
                'required' => false,
                'placeholder' => 'cdn.mysite.com'
            ),
            'ssl_enabled' => array(
                'type' => 'checkbox',
                'label' => __('Enable SSL', 'redco-optimizer'),
                'description' => __('Enable HTTPS for your CDN URLs', 'redco-optimizer'),
                'default' => true
            ),
            'image_processing' => array(
                'type' => 'checkbox',
                'label' => __('Enable Image Processing', 'redco-optimizer'),
                'description' => __('Enable automatic image optimization and WebP conversion', 'redco-optimizer'),
                'default' => true
            ),
            'gzip_compression' => array(
                'type' => 'checkbox',
                'label' => __('Enable Gzip Compression', 'redco-optimizer'),
                'description' => __('Enable Gzip compression for text-based files', 'redco-optimizer'),
                'default' => true
            ),
            'cache_expiry' => array(
                'type' => 'select',
                'label' => __('Cache Expiry', 'redco-optimizer'),
                'description' => __('How long content should be cached', 'redco-optimizer'),
                'options' => array(
                    '3600' => '1 hour',
                    '14400' => '4 hours',
                    '86400' => '1 day',
                    '604800' => '1 week',
                    '2592000' => '1 month',
                    '31536000' => '1 year'
                ),
                'default' => '86400'
            )
        );
    }

    /**
     * Validate configuration
     */
    public function validate_configuration($config) {
        // Check required fields
        if (empty($config['api_key']) || empty($config['zone_id']) || empty($config['zone_url'])) {
            return false;
        }

        // Test API connection
        return $this->test_api_connection($config);
    }

    /**
     * Test API connection
     */
    private function test_api_connection($config = null) {
        $config = $config ?: $this->config;
        
        if (empty($config['api_key']) || empty($config['zone_id'])) {
            return false;
        }

        $headers = array(
            'Authorization' => 'Basic ' . base64_encode($config['api_key'] . ':'),
            'Content-Type' => 'application/json'
        );

        $response = $this->make_api_request(
            $this->api_base . '/zones/' . $config['zone_id'] . '.json',
            array('headers' => $headers)
        );

        return $response['success'];
    }

    /**
     * Test connection
     */
    public function test_connection() {
        return $this->test_api_connection();
    }

    /**
     * Rewrite URL for KeyCDN
     */
    public function rewrite_url($url) {
        if (!$this->is_configured()) {
            return $url;
        }

        $site_url = get_site_url();
        $zone_url = !empty($this->config['custom_domain']) ? 
            $this->config['custom_domain'] : 
            $this->config['zone_url'];

        // Only rewrite URLs that belong to this site
        if (strpos($url, $site_url) === 0) {
            $relative_url = str_replace($site_url, '', $url);
            $protocol = !empty($this->config['ssl_enabled']) ? 'https' : 'http';
            return $protocol . '://' . $zone_url . $relative_url;
        }

        return $url;
    }

    /**
     * Purge cache
     */
    public function purge_cache($urls = array()) {
        if (!$this->is_configured()) {
            return array('success' => false, 'message' => 'KeyCDN not configured');
        }

        $headers = array(
            'Authorization' => 'Basic ' . base64_encode($this->config['api_key'] . ':'),
            'Content-Type' => 'application/json'
        );

        if (empty($urls)) {
            // Purge entire zone
            $response = $this->make_api_request(
                $this->api_base . '/zones/purge/' . $this->config['zone_id'] . '.json',
                array(
                    'method' => 'GET',
                    'headers' => $headers
                )
            );
        } else {
            // Purge specific URLs
            $purge_data = array('urls' => $urls);
            
            $response = $this->make_api_request(
                $this->api_base . '/zones/purgeurl/' . $this->config['zone_id'] . '.json',
                array(
                    'method' => 'POST',
                    'headers' => $headers,
                    'body' => json_encode($purge_data)
                )
            );
        }

        if ($response['success']) {
            $this->log('Cache purged successfully');
            return array('success' => true, 'message' => 'Cache purged successfully');
        } else {
            $this->log('Cache purge failed: ' . $response['error'], 'error');
            return array('success' => false, 'message' => $response['error']);
        }
    }

    /**
     * Get analytics data
     */
    public function get_analytics($period = '24h') {
        if (!$this->is_configured()) {
            return array('success' => false, 'message' => 'KeyCDN not configured');
        }

        $headers = array(
            'Authorization' => 'Basic ' . base64_encode($this->config['api_key'] . ':'),
            'Content-Type' => 'application/json'
        );

        // Calculate time range
        $end_time = current_time('timestamp');
        $start_time = $end_time - $this->parse_period($period);

        $query_params = array(
            'start' => date('Y-m-d', $start_time),
            'end' => date('Y-m-d', $end_time)
        );

        $url = $this->api_base . '/reports/traffic.json?' . http_build_query($query_params);

        $response = $this->make_api_request($url, array('headers' => $headers));

        if ($response['success']) {
            return array(
                'success' => true,
                'data' => $this->format_analytics_data($response['data'])
            );
        } else {
            return array('success' => false, 'message' => $response['error']);
        }
    }

    /**
     * Get cache statistics
     */
    public function get_cache_stats() {
        $analytics = $this->get_analytics('24h');
        
        if (!$analytics['success']) {
            return parent::get_cache_stats();
        }

        $data = $analytics['data'];
        
        return array(
            'cache_hit_ratio' => $data['cache_hit_ratio'] ?? 0,
            'bandwidth_saved' => $data['bandwidth_saved'] ?? 0,
            'requests_served' => $data['requests_served'] ?? 0,
            'data_transferred' => $data['data_transferred'] ?? 0
        );
    }

    /**
     * Get edge locations
     */
    public function get_edge_locations() {
        // KeyCDN edge locations (as of 2024)
        return array(
            'North America' => array('New York', 'Los Angeles', 'Chicago', 'Dallas', 'Miami', 'Seattle'),
            'Europe' => array('Frankfurt', 'London', 'Amsterdam', 'Paris', 'Stockholm', 'Zurich'),
            'Asia Pacific' => array('Singapore', 'Tokyo', 'Sydney', 'Hong Kong'),
            'South America' => array('São Paulo'),
            'Africa' => array('Johannesburg')
        );
    }

    /**
     * Parse period string to seconds
     */
    private function parse_period($period) {
        $periods = array(
            '1h' => 3600,
            '24h' => 86400,
            '7d' => 604800,
            '30d' => 2592000
        );

        return isset($periods[$period]) ? $periods[$period] : 86400;
    }

    /**
     * Format analytics data
     */
    private function format_analytics_data($raw_data) {
        if (empty($raw_data) || !isset($raw_data['data'])) {
            return array(
                'cache_hit_ratio' => 0,
                'bandwidth_saved' => 0,
                'requests_served' => 0,
                'data_transferred' => 0
            );
        }

        $data = $raw_data['data'];
        $total_requests = 0;
        $cache_hits = 0;
        $total_bandwidth = 0;

        foreach ($data as $day_data) {
            $total_requests += $day_data['requests'] ?? 0;
            $cache_hits += $day_data['cache_hits'] ?? 0;
            $total_bandwidth += $day_data['bandwidth'] ?? 0;
        }

        $cache_hit_ratio = $total_requests > 0 ? round(($cache_hits / $total_requests) * 100, 2) : 0;
        $bandwidth_saved = $total_bandwidth * ($cache_hit_ratio / 100);

        return array(
            'cache_hit_ratio' => $cache_hit_ratio,
            'bandwidth_saved' => $this->format_bytes($bandwidth_saved),
            'requests_served' => $total_requests,
            'data_transferred' => $this->format_bytes($total_bandwidth)
        );
    }

    /**
     * Setup cache rules
     */
    public function setup_cache_rules($rules = array()) {
        if (!$this->is_configured()) {
            return array('success' => false, 'message' => 'KeyCDN not configured');
        }

        $headers = array(
            'Authorization' => 'Basic ' . base64_encode($this->config['api_key'] . ':'),
            'Content-Type' => 'application/json'
        );

        // Update zone settings
        $zone_config = array(
            'cache_expire' => intval($this->config['cache_expiry'] ?? 86400),
            'gzip' => !empty($this->config['gzip_compression']) ? 1 : 0,
            'image_processing' => !empty($this->config['image_processing']) ? 1 : 0
        );

        $response = $this->make_api_request(
            $this->api_base . '/zones/' . $this->config['zone_id'] . '.json',
            array(
                'method' => 'PUT',
                'headers' => $headers,
                'body' => json_encode($zone_config)
            )
        );

        if ($response['success']) {
            return array('success' => true, 'message' => 'Cache rules updated successfully');
        } else {
            return array('success' => false, 'message' => $response['error']);
        }
    }

    /**
     * Optimize for WordPress
     */
    public function optimize_for_wordpress() {
        $results = parent::optimize_for_wordpress();

        // Enable KeyCDN-specific optimizations
        if ($this->is_configured()) {
            $optimization_result = $this->setup_cache_rules();
            $results['keycdn_optimization'] = $optimization_result;
        }

        return $results;
    }
}
