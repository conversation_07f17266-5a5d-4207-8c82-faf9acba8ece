<?php
/**
 * Module Auditor for Redco Optimizer
 *
 * Comprehensive audit and optimization system for all plugin modules
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Optimizer_Module_Auditor {

    /**
     * Available modules
     */
    private $modules = array();

    /**
     * Audit results
     */
    private $audit_results = array();

    /**
     * Initialize auditor
     */
    public function init() {
        add_action('wp_ajax_redco_audit_modules', array($this, 'ajax_audit_modules'));
        add_action('wp_ajax_redco_test_module_action', array($this, 'ajax_test_module_action'));
        add_action('wp_ajax_redco_optimize_module', array($this, 'ajax_optimize_module'));
        add_action('wp_ajax_redco_get_real_stats', array($this, 'ajax_get_real_stats'));

        $this->load_modules();
    }

    /**
     * Load available modules
     */
    private function load_modules() {
        $this->modules = array(
            'page-cache' => array(
                'name' => 'Page Cache',
                'class' => 'Redco_Page_Cache',
                'file' => 'modules/page-cache/class-page-cache.php',
                'actions' => array('clear_cache', 'preload_cache'),
                'stats_method' => 'get_stats',
                'critical' => true
            ),
            'database-cleanup' => array(
                'name' => 'Database Cleanup',
                'class' => 'Redco_Database_Cleanup',
                'file' => 'modules/database-cleanup/class-database-cleanup.php',
                'actions' => array('run_cleanup'),
                'stats_method' => 'get_cleanup_stats',
                'critical' => true
            ),
            'asset-optimization' => array(
                'name' => 'Asset Optimization',
                'class' => 'Redco_Asset_Optimization',
                'file' => 'modules/asset-optimization/class-asset-optimization.php',
                'actions' => array('clear_all_cache', 'generate_critical_css', 'optimize_assets'),
                'stats_method' => 'get_stats',
                'critical' => true
            ),
            'lazy-load' => array(
                'name' => 'Lazy Load',
                'class' => 'Redco_Lazy_Load',
                'file' => 'modules/lazy-load/class-lazy-load.php',
                'actions' => array(),
                'stats_method' => 'get_stats',
                'critical' => true
            ),
            'heartbeat-control' => array(
                'name' => 'Heartbeat Control',
                'class' => 'Redco_Heartbeat_Control',
                'file' => 'modules/heartbeat-control/class-heartbeat-control.php',
                'actions' => array(),
                'stats_method' => 'get_stats',
                'critical' => false
            ),
            'autosave-reducer' => array(
                'name' => 'Autosave Reducer',
                'class' => 'Redco_Autosave_Reducer',
                'file' => 'modules/autosave-reducer/class-autosave-reducer.php',
                'actions' => array(),
                'stats_method' => 'get_stats',
                'critical' => false
            ),
            'smart-webp-conversion' => array(
                'name' => 'Smart WebP Conversion',
                'class' => 'Redco_Smart_WebP_Conversion',
                'file' => 'modules/smart-webp-conversion/class-smart-webp-conversion.php',
                'actions' => array('bulk_convert', 'test_conversion'),
                'stats_method' => 'get_stats',
                'critical' => false
            )
        );
    }

    /**
     * AJAX handler for comprehensive module audit
     */
    public function ajax_audit_modules() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_die('Security check failed');
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        $audit_results = $this->run_comprehensive_audit();

        wp_send_json_success(array(
            'audit_results' => $audit_results,
            'recommendations' => $this->get_optimization_recommendations($audit_results)
        ));
    }

    /**
     * Run comprehensive module audit
     */
    public function run_comprehensive_audit() {
        $results = array();

        foreach ($this->modules as $module_key => $module_info) {
            $results[$module_key] = $this->audit_single_module($module_key, $module_info);
        }

        // Store audit results
        update_option('redco_module_audit_results', $results);
        update_option('redco_module_audit_timestamp', time());

        return $results;
    }

    /**
     * Audit a single module
     */
    private function audit_single_module($module_key, $module_info) {
        $audit = array(
            'module' => $module_key,
            'name' => $module_info['name'],
            'enabled' => redco_is_module_enabled($module_key),
            'class_exists' => class_exists($module_info['class']),
            'file_exists' => file_exists(REDCO_OPTIMIZER_PLUGIN_DIR . $module_info['file']),
            'stats_available' => false,
            'stats_real' => false,
            'actions_working' => array(),
            'performance_impact' => 'unknown',
            'issues' => array(),
            'recommendations' => array()
        );

        // Test class instantiation
        if ($audit['class_exists'] && $audit['enabled']) {
            try {
                $instance = new $module_info['class']();
                $audit['instantiable'] = true;

                // Test statistics
                if (method_exists($instance, $module_info['stats_method'])) {
                    $stats = $instance->{$module_info['stats_method']}();
                    $audit['stats_available'] = !empty($stats);
                    $audit['stats_real'] = $this->validate_stats_authenticity($module_key, $stats);
                    $audit['current_stats'] = $stats;
                }

                // Test actions
                foreach ($module_info['actions'] as $action) {
                    if (method_exists($instance, $action)) {
                        $audit['actions_working'][$action] = true;
                    } else {
                        $audit['actions_working'][$action] = false;
                        $audit['issues'][] = "Action method '{$action}' not found";
                    }
                }

            } catch (Exception $e) {
                $audit['instantiable'] = false;
                $audit['issues'][] = "Failed to instantiate class: " . $e->getMessage();
            }
        } else {
            $audit['instantiable'] = false;
            if (!$audit['class_exists']) {
                $audit['issues'][] = "Class '{$module_info['class']}' not found";
            }
            if (!$audit['enabled']) {
                $audit['issues'][] = "Module is disabled";
            }
        }

        // Performance impact assessment
        $audit['performance_impact'] = $this->assess_performance_impact($module_key, $audit);

        // Generate recommendations
        $audit['recommendations'] = $this->generate_module_recommendations($module_key, $audit);

        return $audit;
    }

    /**
     * Validate statistics authenticity
     */
    private function validate_stats_authenticity($module_key, $stats) {
        if (empty($stats)) {
            return false;
        }

        switch ($module_key) {
            case 'database-cleanup':
                // Check if stats contain real database counts
                return isset($stats['revisions']) &&
                       isset($stats['auto_drafts']) &&
                       is_numeric($stats['revisions']) &&
                       is_numeric($stats['auto_drafts']);

            case 'page-cache':
                // Check if cache stats are real
                return isset($stats['hits']) &&
                       isset($stats['misses']) &&
                       is_numeric($stats['hits']) &&
                       is_numeric($stats['misses']);

            case 'css-js-minifier':
                // Check if minification stats are real
                return isset($stats['original_size']) &&
                       isset($stats['minified_size']) &&
                       is_numeric($stats['original_size']) &&
                       is_numeric($stats['minified_size']);

            case 'lazy-load':
                // Check if lazy load stats are real
                return isset($stats['images_processed']) &&
                       is_numeric($stats['images_processed']);

            default:
                return !empty($stats) && is_array($stats);
        }
    }

    /**
     * Assess performance impact
     */
    private function assess_performance_impact($module_key, $audit) {
        if (!$audit['enabled'] || !$audit['instantiable']) {
            return 'none';
        }

        // Get performance metrics if available
        $performance_metrics = get_option('redco_performance_metrics', array());

        if (empty($performance_metrics)) {
            return 'unknown';
        }

        // Analyze recent performance data
        $recent_metrics = array_slice($performance_metrics, -5);
        $avg_execution_time = array_sum(array_column($recent_metrics, 'execution_time')) / count($recent_metrics);
        $avg_memory_usage = array_sum(array_column($recent_metrics, 'memory_usage')) / count($recent_metrics);

        // Determine impact based on module type and metrics
        switch ($module_key) {
            case 'page-cache':
                return $avg_execution_time < 0.5 ? 'positive' : 'neutral';

            case 'css-js-minifier':
                return 'positive'; // Always positive if working

            case 'lazy-load':
                return 'positive'; // Always positive for page load

            case 'database-cleanup':
                return 'positive'; // Positive for database performance

            case 'heartbeat-control':
                return $avg_memory_usage < 50000000 ? 'positive' : 'neutral'; // 50MB threshold

            default:
                return 'neutral';
        }
    }

    /**
     * Generate module recommendations
     */
    private function generate_module_recommendations($module_key, $audit) {
        $recommendations = array();

        if (!$audit['enabled']) {
            if ($audit['file_exists'] && $audit['class_exists']) {
                $recommendations[] = array(
                    'type' => 'enable',
                    'priority' => 'high',
                    'message' => 'Enable this module for immediate performance benefits'
                );
            }
        }

        if ($audit['enabled'] && !$audit['stats_real']) {
            $recommendations[] = array(
                'type' => 'fix_stats',
                'priority' => 'medium',
                'message' => 'Statistics are not showing real data - needs implementation fix'
            );
        }

        if (!empty($audit['issues'])) {
            $recommendations[] = array(
                'type' => 'fix_issues',
                'priority' => 'high',
                'message' => 'Fix module issues: ' . implode(', ', $audit['issues'])
            );
        }

        if ($audit['performance_impact'] === 'unknown') {
            $recommendations[] = array(
                'type' => 'measure_performance',
                'priority' => 'low',
                'message' => 'Enable performance monitoring to measure impact'
            );
        }

        return $recommendations;
    }

    /**
     * AJAX handler for testing module actions
     */
    public function ajax_test_module_action() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_die('Security check failed');
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        $module = sanitize_text_field($_POST['module']);
        $action = sanitize_text_field($_POST['action']);

        $result = $this->test_module_action($module, $action);

        wp_send_json_success($result);
    }

    /**
     * Test a specific module action
     */
    public function test_module_action($module_key, $action) {
        if (!isset($this->modules[$module_key])) {
            return array(
                'success' => false,
                'message' => 'Module not found'
            );
        }

        $module_info = $this->modules[$module_key];

        if (!class_exists($module_info['class'])) {
            return array(
                'success' => false,
                'message' => 'Module class not found'
            );
        }

        try {
            $instance = new $module_info['class']();

            if (!method_exists($instance, $action)) {
                return array(
                    'success' => false,
                    'message' => "Action '{$action}' not available"
                );
            }

            // Get before stats
            $before_stats = null;
            if (method_exists($instance, $module_info['stats_method'])) {
                $before_stats = $instance->{$module_info['stats_method']}();
            }

            // Execute action
            $start_time = microtime(true);
            $action_result = $instance->$action();
            $execution_time = microtime(true) - $start_time;

            // Get after stats
            $after_stats = null;
            if (method_exists($instance, $module_info['stats_method'])) {
                $after_stats = $instance->{$module_info['stats_method']}();
            }

            return array(
                'success' => true,
                'message' => "Action '{$action}' executed successfully",
                'execution_time' => round($execution_time * 1000, 2) . 'ms',
                'before_stats' => $before_stats,
                'after_stats' => $after_stats,
                'action_result' => $action_result
            );

        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Action failed: ' . $e->getMessage()
            );
        }
    }

    /**
     * AJAX handler for getting real statistics
     */
    public function ajax_get_real_stats() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_die('Security check failed');
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        $module = sanitize_text_field($_POST['module']);
        $stats = $this->get_real_module_stats($module);

        wp_send_json_success(array(
            'module' => $module,
            'stats' => $stats,
            'timestamp' => current_time('mysql')
        ));
    }

    /**
     * Get real statistics for a module
     */
    public function get_real_module_stats($module_key) {
        if (!isset($this->modules[$module_key])) {
            return false;
        }

        $module_info = $this->modules[$module_key];

        if (!class_exists($module_info['class'])) {
            return false;
        }

        try {
            $instance = new $module_info['class']();

            if (method_exists($instance, $module_info['stats_method'])) {
                return $instance->{$module_info['stats_method']}();
            }

            return false;

        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get optimization recommendations
     */
    public function get_optimization_recommendations($audit_results) {
        $recommendations = array();

        foreach ($audit_results as $module_key => $audit) {
            foreach ($audit['recommendations'] as $rec) {
                $recommendations[] = array(
                    'module' => $module_key,
                    'module_name' => $audit['name'],
                    'type' => $rec['type'],
                    'priority' => $rec['priority'],
                    'message' => $rec['message']
                );
            }
        }

        // Sort by priority
        usort($recommendations, function($a, $b) {
            $priority_order = array('high' => 3, 'medium' => 2, 'low' => 1);
            return $priority_order[$b['priority']] - $priority_order[$a['priority']];
        });

        return $recommendations;
    }

    /**
     * Get audit summary
     */
    public function get_audit_summary() {
        $audit_results = get_option('redco_module_audit_results', array());
        $audit_timestamp = get_option('redco_module_audit_timestamp', 0);

        if (empty($audit_results)) {
            return array(
                'status' => 'not_run',
                'message' => 'Module audit has not been run yet'
            );
        }

        $total_modules = count($audit_results);
        $enabled_modules = 0;
        $working_modules = 0;
        $modules_with_real_stats = 0;
        $total_issues = 0;

        foreach ($audit_results as $audit) {
            if ($audit['enabled']) {
                $enabled_modules++;
            }
            if ($audit['enabled'] && $audit['instantiable']) {
                $working_modules++;
            }
            if ($audit['stats_real']) {
                $modules_with_real_stats++;
            }
            $total_issues += count($audit['issues']);
        }

        return array(
            'status' => 'completed',
            'timestamp' => $audit_timestamp,
            'total_modules' => $total_modules,
            'enabled_modules' => $enabled_modules,
            'working_modules' => $working_modules,
            'modules_with_real_stats' => $modules_with_real_stats,
            'total_issues' => $total_issues,
            'health_score' => round(($working_modules / $total_modules) * 100)
        );
    }
}
