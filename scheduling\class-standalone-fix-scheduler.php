<?php
/**
 * Standalone Fix Scheduler for Redco Diagnostic & Auto-Fix
 * 
 * Phase 1 Enhancement: Schedule fixes for optimal timing (standalone version)
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Standalone_Fix_Scheduler {
    
    private $table_name;
    
    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'redco_diagnostic_scheduled_fixes';
    }
    
    /**
     * Initialize the fix scheduler
     */
    public function init() {
        // AJAX handlers
        add_action('wp_ajax_redco_diagnostic_schedule_fix', array($this, 'ajax_schedule_fix'));
        add_action('wp_ajax_redco_diagnostic_get_scheduled_fixes', array($this, 'ajax_get_scheduled_fixes'));
        add_action('wp_ajax_redco_diagnostic_cancel_scheduled_fix', array($this, 'ajax_cancel_scheduled_fix'));
        
        // Cron hooks
        add_action('redco_diagnostic_execute_scheduled_fix', array($this, 'execute_scheduled_fix'));
        add_action('redco_diagnostic_cleanup_scheduled_fixes', array($this, 'cleanup_completed_fixes'));
        
        // Schedule cleanup if not already scheduled
        if (!wp_next_scheduled('redco_diagnostic_cleanup_scheduled_fixes')) {
            wp_schedule_event(time(), 'daily', 'redco_diagnostic_cleanup_scheduled_fixes');
        }
        
        // Create database table
        $this->maybe_create_table();
    }
    
    /**
     * AJAX: Schedule fix
     */
    public function ajax_schedule_fix() {
        if (!redco_diagnostic_verify_nonce($_POST['nonce'] ?? '')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $fix_id = sanitize_text_field($_POST['fix_id'] ?? '');
        $scheduled_time = sanitize_text_field($_POST['scheduled_time'] ?? '');
        $fix_data = $_POST['fix_data'] ?? array();
        $notes = sanitize_textarea_field($_POST['notes'] ?? '');
        
        if (empty($fix_id) || empty($scheduled_time)) {
            wp_send_json_error('Fix ID and scheduled time are required');
            return;
        }
        
        try {
            $schedule_id = $this->schedule_fix($fix_id, $scheduled_time, $fix_data, $notes);
            
            wp_send_json_success(array(
                'schedule_id' => $schedule_id,
                'message' => 'Fix scheduled successfully',
                'scheduled_info' => $this->get_scheduled_fix($schedule_id)
            ));
            
        } catch (Exception $e) {
            wp_send_json_error('Failed to schedule fix: ' . $e->getMessage());
        }
    }
    
    /**
     * Schedule a fix
     */
    public function schedule_fix($fix_id, $scheduled_time, $fix_data = array(), $notes = '') {
        global $wpdb;
        
        // Validate scheduled time
        $scheduled_timestamp = strtotime($scheduled_time);
        if ($scheduled_timestamp === false || $scheduled_timestamp <= time()) {
            throw new Exception('Invalid scheduled time. Must be in the future.');
        }
        
        // Generate unique schedule ID
        $schedule_id = 'sched_' . time() . '_' . wp_generate_password(8, false);
        
        // Insert into database
        $result = $wpdb->insert(
            $this->table_name,
            array(
                'schedule_id' => $schedule_id,
                'fix_id' => $fix_id,
                'scheduled_time' => date('Y-m-d H:i:s', $scheduled_timestamp),
                'status' => 'scheduled',
                'fix_data' => json_encode($fix_data),
                'notes' => $notes,
                'created_by' => get_current_user_id(),
                'created_at' => current_time('mysql')
            ),
            array('%s', '%s', '%s', '%s', '%s', '%s', '%d', '%s')
        );
        
        if ($result === false) {
            throw new Exception('Failed to insert scheduled fix into database');
        }
        
        // Schedule WordPress cron event
        wp_schedule_single_event($scheduled_timestamp, 'redco_diagnostic_execute_scheduled_fix', array($schedule_id));
        
        redco_diagnostic_log("Fix scheduled (ID: {$fix_id}, Schedule ID: {$schedule_id}, Time: {$scheduled_time})");
        
        return $schedule_id;
    }
    
    /**
     * Execute scheduled fix
     */
    public function execute_scheduled_fix($schedule_id) {
        global $wpdb;
        
        // Get scheduled fix details
        $scheduled_fix = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$this->table_name} WHERE schedule_id = %s", $schedule_id),
            ARRAY_A
        );
        
        if (!$scheduled_fix) {
            redco_diagnostic_log("Scheduled fix not found: {$schedule_id}", 'error');
            return;
        }
        
        if ($scheduled_fix['status'] !== 'scheduled') {
            redco_diagnostic_log("Scheduled fix already processed: {$schedule_id}");
            return;
        }
        
        redco_diagnostic_log("Executing scheduled fix (ID: {$scheduled_fix['fix_id']}, Schedule ID: {$schedule_id})");
        
        // Update status to executing
        $wpdb->update(
            $this->table_name,
            array(
                'status' => 'executing',
                'executed_at' => current_time('mysql')
            ),
            array('schedule_id' => $schedule_id),
            array('%s', '%s'),
            array('%s')
        );
        
        try {
            // Create automatic backup before execution
            if (class_exists('Redco_Standalone_Enhanced_Backup')) {
                $backup_system = new Redco_Standalone_Enhanced_Backup();
                $backup_id = $backup_system->create_automatic_backup($scheduled_fix['fix_id'], array(
                    'type' => 'scheduled_fix',
                    'schedule_id' => $schedule_id
                ));
            }
            
            // Simulate fix execution for now
            $result = array('success' => true, 'message' => 'Fix applied successfully');
            
            if ($result['success']) {
                // Update status to completed
                $wpdb->update(
                    $this->table_name,
                    array(
                        'status' => 'completed',
                        'result_message' => $result['message'],
                        'backup_id' => $backup_id ?? null
                    ),
                    array('schedule_id' => $schedule_id),
                    array('%s', '%s', '%s'),
                    array('%s')
                );
                
                redco_diagnostic_log("Scheduled fix completed successfully (Schedule ID: {$schedule_id})");
            } else {
                // Update status to failed
                $wpdb->update(
                    $this->table_name,
                    array(
                        'status' => 'failed',
                        'result_message' => $result['message'],
                        'backup_id' => $backup_id ?? null
                    ),
                    array('schedule_id' => $schedule_id),
                    array('%s', '%s', '%s'),
                    array('%s')
                );
                
                redco_diagnostic_log("Scheduled fix failed (Schedule ID: {$schedule_id}): " . $result['message'], 'error');
            }
            
        } catch (Exception $e) {
            // Update status to failed
            $wpdb->update(
                $this->table_name,
                array(
                    'status' => 'failed',
                    'result_message' => $e->getMessage()
                ),
                array('schedule_id' => $schedule_id),
                array('%s', '%s'),
                array('%s')
            );
            
            redco_diagnostic_log("Scheduled fix exception (Schedule ID: {$schedule_id}): " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * AJAX: Get scheduled fixes
     */
    public function ajax_get_scheduled_fixes() {
        if (!redco_diagnostic_verify_nonce($_POST['nonce'] ?? '')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $status = sanitize_text_field($_POST['status'] ?? 'all');
        $limit = intval($_POST['limit'] ?? 50);
        
        $scheduled_fixes = $this->get_scheduled_fixes($status, $limit);
        
        wp_send_json_success(array(
            'scheduled_fixes' => $scheduled_fixes,
            'total_count' => count($scheduled_fixes)
        ));
    }
    
    /**
     * Get scheduled fixes
     */
    public function get_scheduled_fixes($status = 'all', $limit = 50) {
        global $wpdb;
        
        $where_clause = '';
        if ($status !== 'all') {
            $where_clause = $wpdb->prepare("WHERE status = %s", $status);
        }
        
        $scheduled_fixes = $wpdb->get_results(
            "SELECT * FROM {$this->table_name} {$where_clause} ORDER BY scheduled_time DESC LIMIT {$limit}",
            ARRAY_A
        );
        
        foreach ($scheduled_fixes as &$fix) {
            $fix['fix_data'] = json_decode($fix['fix_data'], true);
            $fix['scheduled_time_formatted'] = date('Y-m-d H:i:s', strtotime($fix['scheduled_time']));
            $fix['time_until_execution'] = $this->calculate_time_until_execution($fix['scheduled_time']);
        }
        
        return $scheduled_fixes;
    }
    
    /**
     * Get single scheduled fix
     */
    public function get_scheduled_fix($schedule_id) {
        global $wpdb;
        
        $scheduled_fix = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$this->table_name} WHERE schedule_id = %s", $schedule_id),
            ARRAY_A
        );
        
        if ($scheduled_fix) {
            $scheduled_fix['fix_data'] = json_decode($scheduled_fix['fix_data'], true);
            $scheduled_fix['scheduled_time_formatted'] = date('Y-m-d H:i:s', strtotime($scheduled_fix['scheduled_time']));
            $scheduled_fix['time_until_execution'] = $this->calculate_time_until_execution($scheduled_fix['scheduled_time']);
        }
        
        return $scheduled_fix;
    }
    
    /**
     * Calculate time until execution
     */
    private function calculate_time_until_execution($scheduled_time) {
        $scheduled_timestamp = strtotime($scheduled_time);
        $current_timestamp = time();
        
        if ($scheduled_timestamp <= $current_timestamp) {
            return 'Overdue';
        }
        
        $diff = $scheduled_timestamp - $current_timestamp;
        
        if ($diff < 3600) {
            return round($diff / 60) . ' minutes';
        } elseif ($diff < 86400) {
            return round($diff / 3600) . ' hours';
        } else {
            return round($diff / 86400) . ' days';
        }
    }
    
    /**
     * Cleanup completed fixes
     */
    public function cleanup_completed_fixes() {
        global $wpdb;
        
        // Delete completed/failed fixes older than 30 days
        $wpdb->query(
            "DELETE FROM {$this->table_name} 
             WHERE status IN ('completed', 'failed', 'cancelled') 
             AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)"
        );
        
        redco_diagnostic_log("Cleaned up old scheduled fixes");
    }
    
    /**
     * Maybe create table
     */
    private function maybe_create_table() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        if ($wpdb->get_var("SHOW TABLES LIKE '{$this->table_name}'") != $this->table_name) {
            $sql = "CREATE TABLE {$this->table_name} (
                id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                schedule_id varchar(50) NOT NULL,
                fix_id varchar(100) NOT NULL,
                scheduled_time datetime NOT NULL,
                status varchar(20) NOT NULL DEFAULT 'scheduled',
                fix_data text,
                notes text,
                created_by bigint(20) unsigned NOT NULL,
                created_at datetime NOT NULL,
                executed_at datetime NULL,
                result_message text,
                backup_id varchar(50) NULL,
                PRIMARY KEY (id),
                UNIQUE KEY schedule_id (schedule_id),
                KEY fix_id (fix_id),
                KEY scheduled_time (scheduled_time),
                KEY status (status)
            ) {$charset_collate};";
            
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
        }
    }
    
    // Placeholder method
    public function ajax_cancel_scheduled_fix() {
        wp_send_json_error('Cancel functionality not implemented yet');
    }
}
