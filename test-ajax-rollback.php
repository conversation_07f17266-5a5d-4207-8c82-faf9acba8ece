<?php
/**
 * Test AJAX Rollback Endpoint
 * This script directly tests the rollback AJAX endpoint to see if it's working
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// Security check
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

echo "<h1>🧪 Test AJAX Rollback Endpoint</h1>\n";

// Get current fix history
$fix_history = get_option('redco_diagnostic_fix_history', array());

echo "<h2>📊 Current State</h2>\n";
echo "<div style='background: #f0f0f1; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
echo "<ul>\n";
echo "<li><strong>Fix History Sessions:</strong> " . count($fix_history) . "</li>\n";
echo "</ul>\n";
echo "</div>\n";

if (empty($fix_history)) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>⚠️ No Test Data Available</h3>\n";
    echo "<p>There are no fix sessions to test rollback with. Please apply some fixes first.</p>\n";
    echo "<p><a href='test-rollback-validation.php?create_test_data=yes'>Create Test Data</a></p>\n";
    echo "</div>\n";
    exit;
}

// Show available sessions
echo "<h2>🔧 Available Sessions for Testing</h2>\n";
foreach ($fix_history as $index => $session) {
    $timestamp = $session['timestamp'] ?? time();
    $rollback_id = $session['rollback_id'] ?? $session['backup_id'] ?? 'none';
    $message = $session['message'] ?? 'No message';
    
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px 0; border-radius: 3px;'>\n";
    echo "<h4>Session " . ($index + 1) . "</h4>\n";
    echo "<ul>\n";
    echo "<li><strong>Timestamp:</strong> " . date('Y-m-d H:i:s', $timestamp) . " (" . human_time_diff($timestamp) . " ago)</li>\n";
    echo "<li><strong>Rollback ID:</strong> {$rollback_id}</li>\n";
    echo "<li><strong>Message:</strong> {$message}</li>\n";
    echo "</ul>\n";
    
    if ($rollback_id !== 'none') {
        echo "<p><a href='?test_ajax=" . urlencode($rollback_id) . "' style='background: #dc3232; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px; font-size: 12px;'>🧪 Test AJAX Rollback</a></p>\n";
    }
    echo "</div>\n";
}

// Handle AJAX test
$test_ajax_id = $_GET['test_ajax'] ?? '';
if (!empty($test_ajax_id)) {
    echo "<h2>🧪 Testing AJAX Rollback for ID: {$test_ajax_id}</h2>\n";
    
    // Simulate the exact AJAX request that the frontend would make
    $ajax_url = admin_url('admin-ajax.php');
    $nonce = wp_create_nonce('redco_diagnostic_nonce');
    
    $post_data = array(
        'action' => 'redco_rollback_fixes',
        'backup_id' => $test_ajax_id,
        'nonce' => $nonce
    );
    
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>📡 AJAX Request Details</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>URL:</strong> {$ajax_url}</li>\n";
    echo "<li><strong>Action:</strong> redco_rollback_fixes</li>\n";
    echo "<li><strong>Backup ID:</strong> {$test_ajax_id}</li>\n";
    echo "<li><strong>Nonce:</strong> {$nonce}</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    // Make the AJAX request using WordPress HTTP API
    $response = wp_remote_post($ajax_url, array(
        'body' => $post_data,
        'timeout' => 30,
        'cookies' => $_COOKIE // Pass current session cookies
    ));
    
    if (is_wp_error($response)) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>❌ AJAX Request Failed</h3>\n";
        echo "<p><strong>Error:</strong> " . $response->get_error_message() . "</p>\n";
        echo "</div>\n";
    } else {
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>📡 AJAX Response</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Response Code:</strong> {$response_code}</li>\n";
        echo "<li><strong>Response Length:</strong> " . strlen($response_body) . " characters</li>\n";
        echo "</ul>\n";
        
        if ($response_code === 200) {
            echo "<h4>Response Body:</h4>\n";
            echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; max-height: 300px;'>" . htmlspecialchars($response_body) . "</pre>\n";
            
            // Try to parse as JSON
            $json_response = json_decode($response_body, true);
            if ($json_response) {
                echo "<h4>Parsed JSON Response:</h4>\n";
                if ($json_response['success']) {
                    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; border-radius: 3px;'>\n";
                    echo "<p><strong>✅ SUCCESS:</strong> Rollback was successful!</p>\n";
                    if (isset($json_response['data'])) {
                        echo "<ul>\n";
                        foreach ($json_response['data'] as $key => $value) {
                            if (is_array($value)) {
                                echo "<li><strong>{$key}:</strong> " . json_encode($value) . "</li>\n";
                            } else {
                                echo "<li><strong>{$key}:</strong> {$value}</li>\n";
                            }
                        }
                        echo "</ul>\n";
                    }
                    echo "</div>\n";
                    
                    // Check if the session was actually removed
                    $updated_fix_history = get_option('redco_diagnostic_fix_history', array());
                    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>\n";
                    echo "<h4>📊 Database State After Rollback</h4>\n";
                    echo "<ul>\n";
                    echo "<li><strong>Fix History Sessions Before:</strong> " . count($fix_history) . "</li>\n";
                    echo "<li><strong>Fix History Sessions After:</strong> " . count($updated_fix_history) . "</li>\n";
                    echo "<li><strong>Sessions Removed:</strong> " . (count($fix_history) - count($updated_fix_history)) . "</li>\n";
                    echo "</ul>\n";
                    
                    if (count($fix_history) === count($updated_fix_history)) {
                        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; border-radius: 3px;'>\n";
                        echo "<p><strong>❌ ISSUE DETECTED:</strong> The rollback was reported as successful, but no sessions were actually removed from the fix history!</p>\n";
                        echo "<p>This confirms that there's a bug in the rollback database update logic.</p>\n";
                        echo "</div>\n";
                    } else {
                        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; border-radius: 3px;'>\n";
                        echo "<p><strong>✅ SUCCESS:</strong> The rollback properly removed " . (count($fix_history) - count($updated_fix_history)) . " session(s) from the fix history.</p>\n";
                        echo "</div>\n";
                    }
                    echo "</div>\n";
                    
                } else {
                    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; border-radius: 3px;'>\n";
                    echo "<p><strong>❌ FAILURE:</strong> Rollback failed!</p>\n";
                    echo "<p><strong>Error:</strong> " . ($json_response['data'] ?? 'Unknown error') . "</p>\n";
                    echo "</div>\n";
                }
            } else {
                echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; border-radius: 3px;'>\n";
                echo "<p><strong>⚠️ WARNING:</strong> Response is not valid JSON. This might indicate a PHP error or unexpected output.</p>\n";
                echo "</div>\n";
            }
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; border-radius: 3px;'>\n";
            echo "<p><strong>❌ HTTP ERROR:</strong> Received response code {$response_code}</p>\n";
            echo "</div>\n";
        }
        echo "</div>\n";
    }
    
    echo "<h3>🎯 Next Steps</h3>\n";
    echo "<ol>\n";
    echo "<li>Check the WordPress error logs for detailed information about the rollback process</li>\n";
    echo "<li>Go to the <a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>Diagnostic & Auto-Fix module</a> to see if the Recent Fixes list was updated</li>\n";
    echo "<li>If the issue persists, there may be a bug in the rollback database update logic</li>\n";
    echo "</ol>\n";
    
    echo "<p><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🚀 Go to Diagnostic Module</a></p>\n";
    echo "<p><a href='test-ajax-rollback.php' style='background: #666; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔄 Test Another Session</a></p>\n";
}

echo "<h2>🔧 Additional Tools</h2>\n";
echo "<ul>\n";
echo "<li><a href='check-fix-history.php'>📋 Check Fix History State</a></li>\n";
echo "<li><a href='debug-rollback-process.php'>🐛 Debug Rollback Process</a></li>\n";
echo "<li><a href='immediate-diagnostic-reset.php'>🗑️ Reset All Data</a></li>\n";
echo "</ul>\n";
?>
