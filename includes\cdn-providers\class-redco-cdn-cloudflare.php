<?php
/**
 * Cloudflare CDN Provider - Phase 4 Implementation
 * 
 * Full Cloudflare API v4 integration with advanced features.
 * Supports Workers, Page Rules, Analytics, and Security features.
 * 
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Load base CDN class
require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/cdn-providers/class-redco-cdn-base.php';

class Redco_CDN_Cloudflare extends Redco_CDN_Base {

    /**
     * Provider name
     */
    protected $provider_name = 'cloudflare';

    /**
     * Cloudflare API base URL
     */
    private $api_base = 'https://api.cloudflare.com/client/v4';

    /**
     * Provider capabilities
     */
    protected $capabilities = array(
        'url_rewriting' => true,
        'cache_purging' => true,
        'real_time_analytics' => true,
        'ssl_support' => true,
        'image_optimization' => true,
        'video_streaming' => false,
        'edge_computing' => true,
        'security_features' => true,
        'api_integration' => true
    );

    /**
     * Get configuration fields
     */
    public function get_configuration_fields() {
        return array(
            'api_token' => array(
                'type' => 'password',
                'label' => __('Cloudflare API Token', 'redco-optimizer'),
                'description' => __('Global API Key or scoped API token with Zone:Read and Zone:Edit permissions', 'redco-optimizer'),
                'required' => true,
                'placeholder' => 'Enter your Cloudflare API token'
            ),
            'email' => array(
                'type' => 'email',
                'label' => __('Cloudflare Email', 'redco-optimizer'),
                'description' => __('Email address associated with your Cloudflare account (required for Global API Key)', 'redco-optimizer'),
                'required' => false,
                'placeholder' => '<EMAIL>'
            ),
            'zone_id' => array(
                'type' => 'text',
                'label' => __('Zone ID', 'redco-optimizer'),
                'description' => __('Your domain\'s Cloudflare Zone ID (found in the right sidebar of your domain overview)', 'redco-optimizer'),
                'required' => true,
                'placeholder' => 'Enter your Zone ID'
            ),
            'development_mode' => array(
                'type' => 'checkbox',
                'label' => __('Development Mode', 'redco-optimizer'),
                'description' => __('Bypass Cloudflare cache for testing (automatically disabled after 3 hours)', 'redco-optimizer'),
                'default' => false
            ),
            'minification' => array(
                'type' => 'multiselect',
                'label' => __('Auto Minification', 'redco-optimizer'),
                'description' => __('Enable automatic minification for CSS, JavaScript, and HTML', 'redco-optimizer'),
                'options' => array(
                    'css' => 'CSS',
                    'js' => 'JavaScript',
                    'html' => 'HTML'
                ),
                'default' => array('css', 'js')
            ),
            'security_level' => array(
                'type' => 'select',
                'label' => __('Security Level', 'redco-optimizer'),
                'description' => __('Choose the security level for your website', 'redco-optimizer'),
                'options' => array(
                    'essentially_off' => 'Essentially Off',
                    'low' => 'Low',
                    'medium' => 'Medium',
                    'high' => 'High',
                    'under_attack' => 'Under Attack'
                ),
                'default' => 'medium'
            ),
            'cache_level' => array(
                'type' => 'select',
                'label' => __('Cache Level', 'redco-optimizer'),
                'description' => __('Determine how much content Cloudflare caches', 'redco-optimizer'),
                'options' => array(
                    'aggressive' => 'Aggressive',
                    'basic' => 'Basic',
                    'simplified' => 'Simplified'
                ),
                'default' => 'aggressive'
            ),
            'browser_cache_ttl' => array(
                'type' => 'select',
                'label' => __('Browser Cache TTL', 'redco-optimizer'),
                'description' => __('How long browsers should cache static content', 'redco-optimizer'),
                'options' => array(
                    '30' => '30 minutes',
                    '3600' => '1 hour',
                    '7200' => '2 hours',
                    '14400' => '4 hours',
                    '28800' => '8 hours',
                    '86400' => '1 day',
                    '604800' => '1 week',
                    '2592000' => '1 month',
                    '31536000' => '1 year'
                ),
                'default' => '14400'
            )
        );
    }

    /**
     * Validate configuration
     */
    public function validate_configuration($config) {
        // Check required fields
        if (empty($config['api_token']) || empty($config['zone_id'])) {
            return false;
        }

        // Test API connection
        return $this->test_api_connection($config);
    }

    /**
     * Test API connection
     */
    private function test_api_connection($config = null) {
        $config = $config ?: $this->config;
        
        if (empty($config['api_token']) || empty($config['zone_id'])) {
            return false;
        }

        $headers = array(
            'Authorization' => 'Bearer ' . $config['api_token'],
            'Content-Type' => 'application/json'
        );

        // If email is provided, use Global API Key format
        if (!empty($config['email'])) {
            $headers['X-Auth-Email'] = $config['email'];
            $headers['X-Auth-Key'] = $config['api_token'];
            unset($headers['Authorization']);
        }

        $response = $this->make_api_request(
            $this->api_base . '/zones/' . $config['zone_id'],
            array('headers' => $headers)
        );

        return $response['success'];
    }

    /**
     * Test connection
     */
    public function test_connection() {
        return $this->test_api_connection();
    }

    /**
     * Rewrite URL for Cloudflare CDN
     */
    public function rewrite_url($url) {
        // Cloudflare acts as a proxy, so URLs remain the same
        // But we can add Cloudflare-specific optimizations
        
        if (!$this->is_configured()) {
            return $url;
        }

        // For Cloudflare, we don't change the URL structure
        // The CDN is automatically applied when traffic goes through Cloudflare
        return $url;
    }

    /**
     * Purge cache
     */
    public function purge_cache($urls = array()) {
        if (!$this->is_configured()) {
            return array('success' => false, 'message' => 'Cloudflare not configured');
        }

        $headers = $this->get_api_headers();
        
        if (empty($urls)) {
            // Purge everything
            $body = json_encode(array('purge_everything' => true));
        } else {
            // Purge specific URLs
            $body = json_encode(array('files' => $urls));
        }

        $response = $this->make_api_request(
            $this->api_base . '/zones/' . $this->config['zone_id'] . '/purge_cache',
            array(
                'method' => 'POST',
                'headers' => $headers,
                'body' => $body
            )
        );

        if ($response['success']) {
            $this->log('Cache purged successfully');
            return array('success' => true, 'message' => 'Cache purged successfully');
        } else {
            $this->log('Cache purge failed: ' . $response['error'], 'error');
            return array('success' => false, 'message' => $response['error']);
        }
    }

    /**
     * Get analytics data
     */
    public function get_analytics($period = '24h') {
        if (!$this->is_configured()) {
            return array('success' => false, 'message' => 'Cloudflare not configured');
        }

        $headers = $this->get_api_headers();
        
        // Calculate time range
        $end_time = current_time('timestamp');
        $start_time = $end_time - $this->parse_period($period);

        $query_params = array(
            'since' => date('c', $start_time),
            'until' => date('c', $end_time)
        );

        $url = $this->api_base . '/zones/' . $this->config['zone_id'] . '/analytics/dashboard?' . http_build_query($query_params);

        $response = $this->make_api_request($url, array('headers' => $headers));

        if ($response['success']) {
            return array(
                'success' => true,
                'data' => $this->format_analytics_data($response['data'])
            );
        } else {
            return array('success' => false, 'message' => $response['error']);
        }
    }

    /**
     * Get cache statistics
     */
    public function get_cache_stats() {
        $analytics = $this->get_analytics('24h');
        
        if (!$analytics['success']) {
            return parent::get_cache_stats();
        }

        $data = $analytics['data'];
        
        return array(
            'cache_hit_ratio' => $data['cache_hit_ratio'] ?? 0,
            'bandwidth_saved' => $data['bandwidth_saved'] ?? 0,
            'requests_served' => $data['requests_served'] ?? 0,
            'data_transferred' => $data['data_transferred'] ?? 0
        );
    }

    /**
     * Setup cache rules
     */
    public function setup_cache_rules($rules = array()) {
        if (!$this->is_configured()) {
            return array('success' => false, 'message' => 'Cloudflare not configured');
        }

        $headers = $this->get_api_headers();
        $results = array();

        foreach ($rules as $rule_name => $rule_config) {
            $page_rule = array(
                'targets' => array(
                    array(
                        'target' => 'url',
                        'constraint' => array(
                            'operator' => 'matches',
                            'value' => $this->convert_pattern_to_cloudflare($rule_config['pattern'])
                        )
                    )
                ),
                'actions' => $this->convert_cache_config_to_actions($rule_config)
            );

            $response = $this->make_api_request(
                $this->api_base . '/zones/' . $this->config['zone_id'] . '/pagerules',
                array(
                    'method' => 'POST',
                    'headers' => $headers,
                    'body' => json_encode($page_rule)
                )
            );

            $results[$rule_name] = $response;
        }

        return array('success' => true, 'results' => $results);
    }

    /**
     * Get API headers
     */
    private function get_api_headers() {
        $headers = array(
            'Content-Type' => 'application/json'
        );

        if (!empty($this->config['email'])) {
            // Global API Key format
            $headers['X-Auth-Email'] = $this->config['email'];
            $headers['X-Auth-Key'] = $this->config['api_token'];
        } else {
            // API Token format
            $headers['Authorization'] = 'Bearer ' . $this->config['api_token'];
        }

        return $headers;
    }

    /**
     * Parse period string to seconds
     */
    private function parse_period($period) {
        $periods = array(
            '1h' => 3600,
            '24h' => 86400,
            '7d' => 604800,
            '30d' => 2592000
        );

        return isset($periods[$period]) ? $periods[$period] : 86400;
    }

    /**
     * Format analytics data
     */
    private function format_analytics_data($raw_data) {
        $result = $raw_data['result'] ?? array();
        $totals = $result['totals'] ?? array();

        return array(
            'cache_hit_ratio' => round(($totals['requests']['cached'] ?? 0) / max(($totals['requests']['all'] ?? 1), 1) * 100, 2),
            'bandwidth_saved' => $this->format_bytes($totals['bandwidth']['cached'] ?? 0),
            'requests_served' => $totals['requests']['all'] ?? 0,
            'data_transferred' => $this->format_bytes($totals['bandwidth']['all'] ?? 0),
            'threats_blocked' => $totals['threats']['all'] ?? 0,
            'unique_visitors' => $totals['uniques']['all'] ?? 0
        );
    }

    /**
     * Convert pattern to Cloudflare format
     */
    private function convert_pattern_to_cloudflare($pattern) {
        $domain = parse_url(get_site_url(), PHP_URL_HOST);
        
        // Convert simple patterns to Cloudflare URL patterns
        $pattern = str_replace('*', '*', $pattern);
        
        return $domain . '/' . ltrim($pattern, '/');
    }

    /**
     * Convert cache config to Cloudflare actions
     */
    private function convert_cache_config_to_actions($config) {
        $actions = array();

        if (isset($config['cache_duration'])) {
            if ($config['cache_duration'] === 'no-cache') {
                $actions[] = array('id' => 'cache_level', 'value' => 'bypass');
            } else {
                $actions[] = array('id' => 'cache_level', 'value' => 'cache_everything');
                
                // Convert duration to seconds
                $ttl = $this->parse_cache_duration($config['cache_duration']);
                if ($ttl > 0) {
                    $actions[] = array('id' => 'edge_cache_ttl', 'value' => $ttl);
                }
            }
        }

        return $actions;
    }

    /**
     * Parse cache duration to seconds
     */
    private function parse_cache_duration($duration) {
        $durations = array(
            '5 minutes' => 300,
            '1 hour' => 3600,
            '4 hours' => 14400,
            '1 day' => 86400,
            '30 days' => 2592000,
            '1 year' => 31536000
        );

        return isset($durations[$duration]) ? $durations[$duration] : 0;
    }
}
