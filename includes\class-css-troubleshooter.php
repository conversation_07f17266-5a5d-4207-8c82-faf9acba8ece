<?php
/**
 * CSS Troubleshooter for Redco Optimizer
 *
 * Helps diagnose and fix CSS loading issues
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Optimizer_CSS_Troubleshooter {

    /**
     * Initialize troubleshooter
     */
    public function init() {
        add_action('wp_ajax_redco_css_troubleshoot', array($this, 'ajax_troubleshoot'));
        add_action('wp_ajax_redco_clear_css_cache', array($this, 'ajax_clear_css_cache'));
        // DISABLED to prevent layout shifts in diagnostic module
        // add_action('admin_notices', array($this, 'show_css_notices'));
    }

    /**
     * AJAX handler for CSS troubleshooting
     */
    public function ajax_troubleshoot() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_die('Security check failed');
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        $results = $this->run_css_diagnostics();

        wp_send_json_success(array(
            'diagnostics' => $results,
            'message' => 'CSS diagnostics completed'
        ));
    }

    /**
     * Run comprehensive CSS diagnostics
     */
    public function run_css_diagnostics() {
        $results = array();

        // Check if CSS files exist
        $css_files = array(
            'admin-style.css' => REDCO_OPTIMIZER_PLUGIN_DIR . 'assets/css/admin-style.css'
        );

        foreach ($css_files as $name => $path) {
            $results['files'][$name] = array(
                'exists' => file_exists($path),
                'readable' => is_readable($path),
                'size' => file_exists($path) ? filesize($path) : 0,
                'modified' => file_exists($path) ? filemtime($path) : 0,
                'url' => REDCO_OPTIMIZER_PLUGIN_URL . 'assets/css/' . $name
            );
        }

        // Check WordPress enqueue system
        global $wp_styles;
        $results['enqueued'] = array();

        if (isset($wp_styles->registered['redco-admin-style'])) {
            $style = $wp_styles->registered['redco-admin-style'];
            $results['enqueued']['redco-admin-style'] = array(
                'src' => $style->src,
                'version' => $style->ver,
                'deps' => $style->deps
            );
        }



        // Check for conflicting plugins
        $results['conflicts'] = $this->check_plugin_conflicts();

        // Check server configuration
        $results['server'] = array(
            'php_version' => PHP_VERSION,
            'wp_version' => get_bloginfo('version'),
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'gzip_enabled' => extension_loaded('zlib')
        );

        // Check cache plugins
        $results['cache_plugins'] = $this->detect_cache_plugins();

        return $results;
    }

    /**
     * Check for plugin conflicts
     */
    private function check_plugin_conflicts() {
        $conflicts = array();

        // Known conflicting plugins
        $known_conflicts = array(
            'autoptimize/autoptimize.php' => 'Autoptimize',
            'wp-rocket/wp-rocket.php' => 'WP Rocket',
            'w3-total-cache/w3-total-cache.php' => 'W3 Total Cache',
            'wp-super-cache/wp-cache.php' => 'WP Super Cache',
            'litespeed-cache/litespeed-cache.php' => 'LiteSpeed Cache'
        );

        foreach ($known_conflicts as $plugin_file => $plugin_name) {
            if (is_plugin_active($plugin_file)) {
                $conflicts[] = array(
                    'name' => $plugin_name,
                    'file' => $plugin_file,
                    'potential_issue' => 'May minify or cache CSS files'
                );
            }
        }

        return $conflicts;
    }

    /**
     * Detect cache plugins
     */
    private function detect_cache_plugins() {
        $cache_plugins = array();

        // Check for common cache plugins
        if (function_exists('wp_cache_flush')) {
            $cache_plugins[] = 'Object Cache detected';
        }

        if (defined('WP_CACHE') && WP_CACHE) {
            $cache_plugins[] = 'WP_CACHE enabled';
        }

        if (function_exists('w3tc_flush_all')) {
            $cache_plugins[] = 'W3 Total Cache';
        }

        if (function_exists('wp_cache_clear_cache')) {
            $cache_plugins[] = 'WP Super Cache';
        }

        return $cache_plugins;
    }

    /**
     * Clear CSS cache
     */
    public function ajax_clear_css_cache() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_die('Security check failed');
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        $cleared = array();

        // Clear WordPress object cache
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
            $cleared[] = 'WordPress Object Cache';
        }

        // Clear common cache plugins
        if (function_exists('w3tc_flush_all')) {
            w3tc_flush_all();
            $cleared[] = 'W3 Total Cache';
        }

        if (function_exists('wp_cache_clear_cache')) {
            wp_cache_clear_cache();
            $cleared[] = 'WP Super Cache';
        }

        if (function_exists('rocket_clean_domain')) {
            rocket_clean_domain();
            $cleared[] = 'WP Rocket';
        }

        // Clear browser cache headers
        if (!headers_sent()) {
            header('Cache-Control: no-cache, must-revalidate, max-age=0');
            header('Pragma: no-cache');
            header('Expires: Wed, 11 Jan 1984 05:00:00 GMT');
        }

        wp_send_json_success(array(
            'cleared' => $cleared,
            'message' => 'Cache cleared successfully'
        ));
    }

    /**
     * Show CSS-related admin notices - REMOVED: Using global toast system only
     */
    public function show_css_notices() {
        // Legacy admin notices removed - CSS issues now handled through global toast notifications
        // This prevents layout shifts and provides better user experience
    }

    /**
     * Get troubleshooting recommendations
     */
    public function get_recommendations($diagnostics) {
        $recommendations = array();

        // File existence issues
        foreach ($diagnostics['files'] as $file => $info) {
            if (!$info['exists']) {
                $recommendations[] = array(
                    'type' => 'error',
                    'message' => "CSS file {$file} is missing. Please reinstall the plugin."
                );
            } elseif (!$info['readable']) {
                $recommendations[] = array(
                    'type' => 'error',
                    'message' => "CSS file {$file} is not readable. Check file permissions."
                );
            } elseif ($info['size'] < 1000) {
                $recommendations[] = array(
                    'type' => 'warning',
                    'message' => "CSS file {$file} appears to be corrupted or incomplete."
                );
            }
        }

        // Cache plugin conflicts
        if (!empty($diagnostics['conflicts'])) {
            $recommendations[] = array(
                'type' => 'info',
                'message' => 'Cache plugins detected. Try clearing cache if styles are not loading.'
            );
        }

        // Browser cache
        $recommendations[] = array(
            'type' => 'info',
            'message' => 'Try hard refresh (Ctrl+F5) or clear browser cache if styles appear outdated.'
        );

        return $recommendations;
    }
}
