<?php
/**
 * Check current fix history state
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// Security check
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

echo "<h1>🔍 Current Fix History State</h1>\n";

$fix_history = get_option('redco_diagnostic_fix_history', array());
$fixed_issues = get_option('redco_fixed_issues', array());

echo "<h2>📊 Database State</h2>\n";
echo "<div style='background: #f0f0f1; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
echo "<ul>\n";
echo "<li><strong>Fix History Sessions:</strong> " . count($fix_history) . "</li>\n";
echo "<li><strong>Fixed Issues:</strong> " . count($fixed_issues) . "</li>\n";
echo "</ul>\n";
echo "</div>\n";

if (!empty($fix_history)) {
    echo "<h2>📋 Fix History Sessions</h2>\n";
    
    foreach ($fix_history as $index => $session) {
        $timestamp = $session['timestamp'] ?? time();
        $rollback_id = $session['rollback_id'] ?? 'none';
        $backup_id = $session['backup_id'] ?? 'none';
        $session_id = $session['session_id'] ?? 'none';
        $message = $session['message'] ?? 'none';
        $details_count = isset($session['details']) ? count($session['details']) : 0;
        
        echo "<div style='border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px;'>\n";
        echo "<h3>Session " . ($index + 1) . "</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Timestamp:</strong> " . date('Y-m-d H:i:s', $timestamp) . " (" . human_time_diff($timestamp) . " ago)</li>\n";
        echo "<li><strong>Rollback ID:</strong> {$rollback_id}</li>\n";
        echo "<li><strong>Backup ID:</strong> {$backup_id}</li>\n";
        echo "<li><strong>Session ID:</strong> {$session_id}</li>\n";
        echo "<li><strong>Message:</strong> {$message}</li>\n";
        echo "<li><strong>Details Count:</strong> {$details_count}</li>\n";
        echo "</ul>\n";
        
        if (isset($session['details']) && is_array($session['details'])) {
            echo "<h4>Fix Details:</h4>\n";
            echo "<ul>\n";
            foreach ($session['details'] as $detail_index => $detail) {
                $issue_id = $detail['issue_id'] ?? 'unknown';
                $success = $detail['success'] ?? false;
                $detail_rollback_id = $detail['rollback_id'] ?? 'none';
                echo "<li>Detail " . ($detail_index + 1) . ": Issue ID: {$issue_id}, Success: " . ($success ? 'Yes' : 'No') . ", Rollback ID: {$detail_rollback_id}</li>\n";
            }
            echo "</ul>\n";
        }
        
        echo "</div>\n";
    }
} else {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>\n";
    echo "<p>No fix history sessions found.</p>\n";
    echo "</div>\n";
}

echo "<h2>🧪 Test Rollback Removal</h2>\n";

if (!empty($fix_history)) {
    $latest_session = end($fix_history);
    $test_rollback_id = $latest_session['rollback_id'] ?? $latest_session['backup_id'] ?? '';
    
    if (!empty($test_rollback_id)) {
        echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>🎯 Test Rollback for Latest Session</h3>\n";
        echo "<p><strong>Rollback ID to test:</strong> {$test_rollback_id}</p>\n";
        echo "<p><a href='?simulate_rollback=" . urlencode($test_rollback_id) . "' style='background: #dc3232; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🧪 Simulate Rollback</a></p>\n";
        echo "</div>\n";
    }
}

// Handle rollback simulation
$simulate_rollback_id = $_GET['simulate_rollback'] ?? '';
if (!empty($simulate_rollback_id)) {
    echo "<h2>🔄 Simulating Rollback for ID: {$simulate_rollback_id}</h2>\n";
    
    // Find and remove the session
    $updated_history = array();
    $removed_session = null;
    $rolled_back_issue_ids = array();
    
    foreach ($fix_history as $session) {
        $session_rollback_id = $session['rollback_id'] ?? '';
        $session_backup_id = $session['backup_id'] ?? '';
        $session_timestamp = $session['timestamp'] ?? '';
        
        $should_remove = false;
        
        // Multiple matching strategies
        if (!empty($session_rollback_id) && $session_rollback_id === $simulate_rollback_id) {
            $should_remove = true;
            echo "✅ Match found via rollback_id<br>\n";
        } elseif (!empty($session_backup_id) && $session_backup_id === $simulate_rollback_id) {
            $should_remove = true;
            echo "✅ Match found via backup_id<br>\n";
        } elseif (!empty($session_timestamp) && strpos($simulate_rollback_id, (string)$session_timestamp) !== false) {
            $should_remove = true;
            echo "✅ Match found via timestamp in backup_id<br>\n";
        } elseif (isset($session['details']) && is_array($session['details'])) {
            foreach ($session['details'] as $detail) {
                if (isset($detail['rollback_id']) && $detail['rollback_id'] === $simulate_rollback_id) {
                    $should_remove = true;
                    echo "✅ Match found via details rollback_id<br>\n";
                    break;
                }
            }
        }
        
        if ($should_remove) {
            $removed_session = $session;
            // Collect issue IDs
            if (isset($session['details']) && is_array($session['details'])) {
                foreach ($session['details'] as $detail) {
                    if (isset($detail['issue_id']) && $detail['success']) {
                        $rolled_back_issue_ids[] = $detail['issue_id'];
                    }
                }
            }
            echo "🗑️ Session will be removed<br>\n";
            continue; // Skip this session
        }
        
        $updated_history[] = $session;
    }
    
    if ($removed_session) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 15px 0;'>\n";
        echo "<h3>✅ Rollback Simulation Results</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>Sessions Before:</strong> " . count($fix_history) . "</li>\n";
        echo "<li><strong>Sessions After:</strong> " . count($updated_history) . "</li>\n";
        echo "<li><strong>Session Removed:</strong> Yes</li>\n";
        echo "<li><strong>Issue IDs to Remove:</strong> " . implode(', ', $rolled_back_issue_ids) . "</li>\n";
        echo "</ul>\n";
        
        // Actually apply the changes
        $history_updated = update_option('redco_diagnostic_fix_history', $updated_history);
        
        // Remove fixed issues
        $updated_fixed_issues = $fixed_issues;
        $removed_issues_count = 0;
        foreach ($rolled_back_issue_ids as $issue_id) {
            if (isset($updated_fixed_issues[$issue_id])) {
                unset($updated_fixed_issues[$issue_id]);
                $removed_issues_count++;
            }
        }
        $issues_updated = update_option('redco_fixed_issues', $updated_fixed_issues);
        
        // Clear caches
        delete_transient('redco_recent_fixes');
        wp_cache_delete('redco_recent_fixes');
        
        echo "<li><strong>Database Updated:</strong> " . ($history_updated ? 'Yes' : 'No') . "</li>\n";
        echo "<li><strong>Fixed Issues Updated:</strong> " . ($issues_updated ? 'Yes' : 'No') . "</li>\n";
        echo "<li><strong>Issues Removed:</strong> {$removed_issues_count}</li>\n";
        echo "<li><strong>Caches Cleared:</strong> Yes</li>\n";
        echo "</ul>\n";
        echo "</div>\n";
        
        echo "<h3>🎯 Next Steps</h3>\n";
        echo "<ol>\n";
        echo "<li>Go to the <a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>Diagnostic & Auto-Fix module</a></li>\n";
        echo "<li>Check the Recent Fixes section - it should now show " . count($updated_history) . " items</li>\n";
        echo "<li>The rolled-back session should no longer appear</li>\n";
        echo "</ol>\n";
        
        echo "<p><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🚀 Go to Diagnostic Module</a></p>\n";
        
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>❌ No Session Found</h3>\n";
        echo "<p>No session found with rollback ID: {$simulate_rollback_id}</p>\n";
        echo "</div>\n";
    }
}

echo "<p><a href='check-fix-history.php' style='background: #666; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔄 Refresh</a></p>\n";
?>
