/**
 * Force refresh Recent Fixes - Add this to browser console to manually refresh
 */

console.log('🔧 FORCING RECENT FIXES REFRESH...');

// Clear any cached data
if (typeof localStorage !== 'undefined') {
    try {
        localStorage.removeItem('redco_cache_redco_recent_fixes');
        console.log('✅ Cleared localStorage cache');
    } catch (e) {
        console.log('⚠️ Could not clear localStorage:', e);
    }
}

// Check if Recent Fixes container exists
const $container = jQuery('#recent-fixes-container');
if ($container.length === 0) {
    console.log('❌ Recent Fixes container not found');
    console.log('Available containers:', jQuery('[id*="recent"], [class*="recent"]').map(function() {
        return this.id || this.className;
    }).get());
} else {
    console.log('✅ Recent Fixes container found');
    
    // Show loading state
    $container.html(`
        <div class="recent-fixes-loading">
            <span class="dashicons dashicons-update"></span>
            Loading recent fixes...
        </div>
    `);
    
    // Make direct AJAX request
    jQuery.ajax({
        url: redcoDiagnosticAjax.ajaxurl,
        type: 'POST',
        data: {
            action: 'redco_load_recent_fixes',
            nonce: redcoDiagnosticAjax.nonce
        },
        success: function(response) {
            console.log('🔧 AJAX Response:', response);
            
            if (response.success && response.data && response.data.html) {
                console.log('✅ Recent Fixes data received');
                console.log('HTML length:', response.data.html.length);
                console.log('Fix items:', (response.data.html.match(/class="fix-item"/g) || []).length);
                
                // Update container
                $container.html(response.data.html);
                
                // Verify update
                setTimeout(function() {
                    const fixItems = $container.find('.fix-item').length;
                    console.log('✅ Recent Fixes updated - Fix items displayed:', fixItems);
                    
                    if (fixItems === 0) {
                        console.log('⚠️ No fix items displayed - showing raw HTML:');
                        console.log(response.data.html.substring(0, 500));
                    }
                }, 100);
                
            } else {
                console.log('❌ Invalid response:', response);
                $container.html('<div class="no-fixes-message"><p>No recent fixes available.</p></div>');
            }
        },
        error: function(xhr, status, error) {
            console.log('❌ AJAX Error:', {xhr, status, error});
            $container.html('<div class="error-message"><p>Error loading recent fixes.</p></div>');
        }
    });
}

// Also check if the main diagnostic object exists
if (typeof RedcoDiagnosticAdmin !== 'undefined') {
    console.log('✅ RedcoDiagnosticAdmin object exists');
    
    // Try to call the loadRecentFixes method directly
    if (typeof RedcoDiagnosticAdmin.loadRecentFixes === 'function') {
        console.log('🔄 Calling RedcoDiagnosticAdmin.loadRecentFixes(true)...');
        RedcoDiagnosticAdmin.loadRecentFixes(true);
    } else {
        console.log('⚠️ loadRecentFixes method not found');
        console.log('Available methods:', Object.keys(RedcoDiagnosticAdmin));
    }
} else {
    console.log('❌ RedcoDiagnosticAdmin object not found');
    console.log('Available global objects:', Object.keys(window).filter(key => key.includes('redco') || key.includes('Redco')));
}

console.log('🔧 Recent Fixes refresh attempt complete');

// Instructions for user
console.log(`
📋 MANUAL REFRESH INSTRUCTIONS:
1. Copy and paste this entire script into your browser console
2. Press Enter to execute
3. Check if Recent Fixes appear in the sidebar
4. If still empty, check Network tab for AJAX requests
5. Look for any JavaScript errors in console
`);

// Test function to check Recent Fixes status
window.checkRecentFixesStatus = function() {
    const $container = jQuery('#recent-fixes-container');
    const hasContainer = $container.length > 0;
    const hasContent = $container.find('.fix-item').length > 0;
    const hasLoading = $container.find('.recent-fixes-loading').length > 0;
    
    console.log('📊 RECENT FIXES STATUS:');
    console.log('Container exists:', hasContainer);
    console.log('Has fix items:', hasContent);
    console.log('Still loading:', hasLoading);
    console.log('Container HTML:', $container.html());
    
    return {
        hasContainer,
        hasContent,
        hasLoading,
        html: $container.html()
    };
};
