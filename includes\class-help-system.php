<?php
/**
 * Help System Class
 *
 * Manages the contextual help system for Redco Optimizer modules.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Help_System {

    /**
     * Initialize the help system
     */
    public function __construct() {
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_ajax_redco_get_help_content', array($this, 'ajax_get_help_content'));
    }

    /**
     * Enqueue help system scripts and styles
     */
    public function enqueue_scripts($hook) {
        // Ensure hook is not null before using strpos
        $hook = (string) $hook;
        if (empty($hook) || strpos($hook, 'redco-optimizer') === false) {
            return;
        }

        wp_enqueue_script(
            'redco-help-system',
            REDCO_OPTIMIZER_PLUGIN_URL . 'assets/js/help-system.js',
            array('jquery'),
            REDCO_OPTIMIZER_VERSION,
            true
        );

        wp_localize_script('redco-help-system', 'redcoHelp', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('redco_help_nonce'),
            'strings' => array(
                'loading' => __('Loading help content...', 'redco-optimizer'),
                'error' => __('Error loading help content', 'redco-optimizer'),
                'search_placeholder' => __('Search help topics...', 'redco-optimizer'),
                'no_results' => __('No help topics found', 'redco-optimizer'),
            )
        ));
    }

    /**
     * AJAX handler for getting help content
     */
    public function ajax_get_help_content() {
        check_ajax_referer('redco_help_nonce', 'nonce');

        $module = sanitize_text_field($_POST['module'] ?? '');
        $section = sanitize_text_field($_POST['section'] ?? '');
        $field = sanitize_text_field($_POST['field'] ?? '');

        $help_data = $this->get_help_data($module, $section, $field);

        if ($help_data) {
            $rendered_content = $this->render_help_content($help_data, $section, $field);
            wp_send_json_success(array(
                'content' => $rendered_content,
                'title' => $help_data['title'] ?? __('Help', 'redco-optimizer')
            ));
        } else {
            wp_send_json_error(__('Help content not found', 'redco-optimizer'));
        }
    }

    /**
     * Get help data for a specific module, section, and field
     */
    public function get_help_data($module, $section = '', $field = '') {
        $all_help_data = $this->get_all_help_data();

        if (!isset($all_help_data[$module])) {
            return false;
        }

        $module_data = $all_help_data[$module];

        // If no section specified, return module overview
        if (empty($section)) {
            return $module_data;
        }

        // If section specified but doesn't exist, return module overview
        if (!isset($module_data['sections'][$section])) {
            return $module_data;
        }

        $section_data = $module_data['sections'][$section];

        // If field specified and exists, return field-specific data
        if (!empty($field) && isset($section_data['fields'][$field])) {
            $field_data = $section_data['fields'][$field];
            // Merge section context with field data
            return array_merge($section_data, array(
                'title' => $field_data['title'],
                'field_data' => $field_data,
                'is_field_help' => true
            ));
        }

        // Return section data with module context
        return array_merge($module_data, array(
            'title' => $section_data['title'],
            'section_data' => $section_data,
            'is_section_help' => true
        ));
    }

    /**
     * Get help content for a specific module and section (legacy method)
     */
    public function get_help_content($module, $section = '') {
        return $this->get_help_data($module, $section);
    }

    /**
     * Get all help data for modules
     */
    public function get_all_help_data() {
        return array(
            'page-cache' => array(
                'title' => __('Page Cache Help', 'redco-optimizer'),
                'overview' => __('Page caching creates static HTML versions of your dynamic WordPress pages, dramatically reducing server processing time and database queries. When a visitor requests a page, the cached version is served instantly instead of regenerating the page from scratch.', 'redco-optimizer'),
                'how_it_works' => __('When a page is first visited, WordPress processes PHP code, queries the database, and generates HTML. Page caching saves this final HTML output to disk. Subsequent visitors receive the pre-generated HTML file directly, bypassing PHP processing entirely.', 'redco-optimizer'),
                'performance_impact' => __('Can improve page load times by 50-90% and reduce server CPU usage by up to 80%. Enables your server to handle 5-10x more concurrent visitors.', 'redco-optimizer'),
                'sections' => array(
                    'cache-settings' => array(
                        'title' => __('Cache Settings', 'redco-optimizer'),
                        'content' => __('Configure cache behavior and expiration policies to balance performance with content freshness.', 'redco-optimizer'),
                        'how_it_works' => __('Cache expiration determines when cached files are considered stale and need regeneration. Shorter expiration ensures fresh content but increases server load. Longer expiration maximizes performance but may show outdated content.', 'redco-optimizer'),
                        'fields' => array(
                            'cache_expiration' => array(
                                'title' => __('Cache Expiration Time', 'redco-optimizer'),
                                'help' => __('Sets the maximum age of cached files before they expire and need regeneration. This is crucial for balancing performance with content freshness.', 'redco-optimizer'),
                                'how_it_works' => __('When a cached file reaches its expiration time, the next visitor triggers regeneration. The new visitor waits for generation while subsequent visitors get the fresh cache.', 'redco-optimizer'),
                                'performance_impact' => __('Longer expiration = Better performance but potentially stale content. Shorter expiration = Fresh content but more server processing.', 'redco-optimizer'),
                                'technical_details' => array(
                                    __('Cache files are stored with timestamps in the filesystem', 'redco-optimizer'),
                                    __('Expiration check happens on every page request', 'redco-optimizer'),
                                    __('Expired cache triggers background regeneration process', 'redco-optimizer'),
                                    __('New cache overwrites old cache atomically to prevent corruption', 'redco-optimizer')
                                ),
                                'value_options' => array(
                                    '1800' => array(
                                        'label' => __('30 minutes', 'redco-optimizer'),
                                        'use_case' => __('High-frequency content updates, news sites, live blogs', 'redco-optimizer'),
                                        'server_impact' => __('High - frequent regeneration, more CPU usage', 'redco-optimizer'),
                                        'user_experience' => __('Always fresh content, minimal staleness risk', 'redco-optimizer')
                                    ),
                                    '3600' => array(
                                        'label' => __('1 hour', 'redco-optimizer'),
                                        'use_case' => __('Regular content updates, business blogs, moderate traffic', 'redco-optimizer'),
                                        'server_impact' => __('Medium - balanced regeneration frequency', 'redco-optimizer'),
                                        'user_experience' => __('Good balance of freshness and performance', 'redco-optimizer')
                                    ),
                                    '7200' => array(
                                        'label' => __('2 hours', 'redco-optimizer'),
                                        'use_case' => __('Standard business sites, portfolios, service pages', 'redco-optimizer'),
                                        'server_impact' => __('Low-Medium - reduced server load', 'redco-optimizer'),
                                        'user_experience' => __('Good performance with acceptable freshness', 'redco-optimizer')
                                    ),
                                    '21600' => array(
                                        'label' => __('6 hours', 'redco-optimizer'),
                                        'use_case' => __('Corporate sites, documentation, product catalogs', 'redco-optimizer'),
                                        'server_impact' => __('Low - minimal regeneration overhead', 'redco-optimizer'),
                                        'user_experience' => __('Excellent performance, content may be hours old', 'redco-optimizer')
                                    ),
                                    '43200' => array(
                                        'label' => __('12 hours', 'redco-optimizer'),
                                        'use_case' => __('Static sites, landing pages, marketing sites', 'redco-optimizer'),
                                        'server_impact' => __('Very Low - maximum performance benefit', 'redco-optimizer'),
                                        'user_experience' => __('Maximum speed, content freshness less critical', 'redco-optimizer')
                                    ),
                                    '86400' => array(
                                        'label' => __('24 hours', 'redco-optimizer'),
                                        'use_case' => __('Rarely updated sites, archives, reference materials', 'redco-optimizer'),
                                        'server_impact' => __('Minimal - almost no regeneration overhead', 'redco-optimizer'),
                                        'user_experience' => __('Ultimate performance, daily content updates acceptable', 'redco-optimizer')
                                    )
                                ),
                                'recommendations' => array(
                                    __('News/Blog sites: 1-2 hours (frequent updates)', 'redco-optimizer'),
                                    __('Business sites: 6-12 hours (moderate updates)', 'redco-optimizer'),
                                    __('Static sites: 24 hours (rare updates)', 'redco-optimizer'),
                                    __('E-commerce: 2-6 hours (inventory/price changes)', 'redco-optimizer'),
                                    __('High-traffic sites: Longer expiration for stability', 'redco-optimizer')
                                ),
                                'pros' => array(
                                    __('Longer expiration: Maximum performance, reduced server load, better stability', 'redco-optimizer'),
                                    __('Shorter expiration: Fresher content, better for dynamic sites', 'redco-optimizer')
                                ),
                                'cons' => array(
                                    __('Longer expiration: Potentially outdated content, slower content updates', 'redco-optimizer'),
                                    __('Shorter expiration: Higher server load, more frequent regeneration', 'redco-optimizer')
                                ),
                                'troubleshooting' => array(
                                    __('If content seems stale: Reduce expiration time or clear cache manually', 'redco-optimizer'),
                                    __('If server is slow: Increase expiration time to reduce regeneration frequency', 'redco-optimizer'),
                                    __('If cache not working: Check file permissions and disk space', 'redco-optimizer')
                                ),
                                'advanced_tips' => array(
                                    __('Use shorter expiration during content-heavy periods', 'redco-optimizer'),
                                    __('Monitor server resources to find optimal balance', 'redco-optimizer'),
                                    __('Consider time-based cache clearing for scheduled updates', 'redco-optimizer')
                                )
                            )
                        )
                    ),
                    'excluded-pages' => array(
                        'title' => __('Excluded Pages', 'redco-optimizer'),
                        'content' => __('Specify pages that should never be cached to preserve dynamic functionality and user-specific content.', 'redco-optimizer'),
                        'how_it_works' => __('Excluded pages bypass the cache entirely. Every visit generates the page fresh from PHP and database, ensuring dynamic content works correctly but at the cost of performance.', 'redco-optimizer'),
                        'performance_impact' => __('Excluded pages load slower but maintain full functionality. Each exclusion reduces overall cache effectiveness by removing potential performance gains.', 'redco-optimizer'),
                        'fields' => array(
                            'excluded_pages' => array(
                                'title' => __('Pages to Exclude from Cache', 'redco-optimizer'),
                                'help' => __('Select specific pages that should never be cached. Use this for pages with dynamic content, user-specific information, or interactive functionality.', 'redco-optimizer'),
                                'how_it_works' => __('Selected pages are added to an exclusion list. When a request comes for these pages, the caching system is bypassed entirely and the page is generated fresh each time.', 'redco-optimizer'),
                                'selection_criteria' => array(
                                    'dynamic_content' => array(
                                        'title' => __('Dynamic Content Pages', 'redco-optimizer'),
                                        'description' => __('Pages that change content based on user actions or real-time data', 'redco-optimizer'),
                                        'examples' => array(
                                            __('Search results pages', 'redco-optimizer'),
                                            __('User dashboards', 'redco-optimizer'),
                                            __('Live chat pages', 'redco-optimizer'),
                                            __('Real-time data displays', 'redco-optimizer')
                                        ),
                                        'why_exclude' => __('Content changes frequently and must be current for each user', 'redco-optimizer')
                                    ),
                                    'user_specific' => array(
                                        'title' => __('User-Specific Pages', 'redco-optimizer'),
                                        'description' => __('Pages that display different content for different users', 'redco-optimizer'),
                                        'examples' => array(
                                            __('User profiles', 'redco-optimizer'),
                                            __('Account settings', 'redco-optimizer'),
                                            __('Personalized recommendations', 'redco-optimizer'),
                                            __('User-specific notifications', 'redco-optimizer')
                                        ),
                                        'why_exclude' => __('Caching would show one user\'s content to another user', 'redco-optimizer')
                                    ),
                                    'interactive_forms' => array(
                                        'title' => __('Interactive Forms', 'redco-optimizer'),
                                        'description' => __('Pages with forms that process user input', 'redco-optimizer'),
                                        'examples' => array(
                                            __('Contact forms', 'redco-optimizer'),
                                            __('Registration forms', 'redco-optimizer'),
                                            __('Survey forms', 'redco-optimizer'),
                                            __('File upload pages', 'redco-optimizer')
                                        ),
                                        'why_exclude' => __('Form tokens and CSRF protection require fresh page generation', 'redco-optimizer')
                                    ),
                                    'ecommerce_pages' => array(
                                        'title' => __('E-commerce Pages', 'redco-optimizer'),
                                        'description' => __('Shopping-related pages with dynamic pricing or inventory', 'redco-optimizer'),
                                        'examples' => array(
                                            __('Shopping cart', 'redco-optimizer'),
                                            __('Checkout process', 'redco-optimizer'),
                                            __('Wishlist pages', 'redco-optimizer'),
                                            __('Order tracking', 'redco-optimizer')
                                        ),
                                        'why_exclude' => __('Cart contents and pricing must be current and user-specific', 'redco-optimizer')
                                    ),
                                    'authentication_pages' => array(
                                        'title' => __('Authentication Pages', 'redco-optimizer'),
                                        'description' => __('Pages related to user login and security', 'redco-optimizer'),
                                        'examples' => array(
                                            __('Login page', 'redco-optimizer'),
                                            __('Password reset', 'redco-optimizer'),
                                            __('Two-factor authentication', 'redco-optimizer'),
                                            __('Account verification', 'redco-optimizer')
                                        ),
                                        'why_exclude' => __('Security tokens and session data must be fresh', 'redco-optimizer')
                                    )
                                ),
                                'page_types_analysis' => array(
                                    'homepage' => array(
                                        'should_cache' => true,
                                        'reason' => __('Usually static content, high traffic benefit', 'redco-optimizer'),
                                        'exceptions' => __('If personalized content or user-specific widgets', 'redco-optimizer')
                                    ),
                                    'blog_posts' => array(
                                        'should_cache' => true,
                                        'reason' => __('Static content, infrequent changes', 'redco-optimizer'),
                                        'exceptions' => __('If comments are dynamic or user-specific content', 'redco-optimizer')
                                    ),
                                    'product_pages' => array(
                                        'should_cache' => 'conditional',
                                        'reason' => __('Depends on inventory updates and personalization', 'redco-optimizer'),
                                        'considerations' => __('Cache if inventory is stable, exclude if real-time stock levels', 'redco-optimizer')
                                    ),
                                    'contact_pages' => array(
                                        'should_cache' => false,
                                        'reason' => __('Contains forms with CSRF tokens', 'redco-optimizer'),
                                        'impact' => __('Forms may break if cached', 'redco-optimizer')
                                    )
                                ),
                                'performance_considerations' => array(
                                    'exclusion_impact' => __('Each excluded page reduces cache effectiveness by 5-15% depending on traffic distribution', 'redco-optimizer'),
                                    'server_load' => __('Excluded pages consume 3-10x more server resources than cached pages', 'redco-optimizer'),
                                    'user_experience' => __('Excluded pages load 200-500ms slower than cached equivalents', 'redco-optimizer'),
                                    'optimization_tips' => array(
                                        __('Minimize exclusions to essential pages only', 'redco-optimizer'),
                                        __('Consider partial caching for semi-dynamic pages', 'redco-optimizer'),
                                        __('Use AJAX for dynamic elements instead of excluding entire pages', 'redco-optimizer')
                                    )
                                )
                            )
                        ),
                        'recommendations' => array(
                            __('Always exclude: Login, registration, password reset pages', 'redco-optimizer'),
                            __('E-commerce: Cart, checkout, account pages, wishlist', 'redco-optimizer'),
                            __('Forms: Contact forms, search results, user submissions', 'redco-optimizer'),
                            __('User-specific: Dashboards, profiles, personalized content', 'redco-optimizer'),
                            __('Dynamic: AJAX-heavy pages, real-time content', 'redco-optimizer')
                        ),
                        'pros' => array(
                            __('Preserves dynamic functionality', 'redco-optimizer'),
                            __('Ensures user-specific content accuracy', 'redco-optimizer'),
                            __('Prevents form submission issues', 'redco-optimizer'),
                            __('Maintains security for sensitive pages', 'redco-optimizer')
                        ),
                        'cons' => array(
                            __('Excluded pages load slower', 'redco-optimizer'),
                            __('Reduces overall cache effectiveness', 'redco-optimizer'),
                            __('Higher server load for excluded pages', 'redco-optimizer')
                        ),
                        'warnings' => array(
                            __('Never cache pages with user-specific content', 'redco-optimizer'),
                            __('Test thoroughly after excluding pages', 'redco-optimizer'),
                            __('Over-exclusion reduces cache benefits', 'redco-optimizer')
                        )
                    ),
                    'cache-statistics' => array(
                        'title' => __('Cache Statistics', 'redco-optimizer'),
                        'content' => __('Monitor cache performance metrics to optimize your caching strategy and identify potential issues.', 'redco-optimizer'),
                        'how_it_works' => __('Statistics track cache effectiveness by measuring hits vs misses, storage usage, and performance gains. Higher hit rates indicate better cache effectiveness.', 'redco-optimizer'),
                        'fields' => array(
                            'cache_hits' => array(
                                'title' => __('Cache Hits', 'redco-optimizer'),
                                'help' => __('Number of times pages were served directly from cache without regeneration. This is the primary indicator of cache effectiveness.', 'redco-optimizer'),
                                'how_it_works' => __('When a visitor requests a page, if a valid cached version exists, it\'s served immediately. This counts as a cache hit.', 'redco-optimizer'),
                                'interpretation' => array(
                                    'excellent' => array(
                                        'range' => '90%+',
                                        'meaning' => __('Outstanding cache performance, maximum server resource savings', 'redco-optimizer'),
                                        'action' => __('Maintain current settings, monitor for consistency', 'redco-optimizer')
                                    ),
                                    'good' => array(
                                        'range' => '80-90%',
                                        'meaning' => __('Good cache performance with room for optimization', 'redco-optimizer'),
                                        'action' => __('Consider longer expiration times or reducing exclusions', 'redco-optimizer')
                                    ),
                                    'fair' => array(
                                        'range' => '60-80%',
                                        'meaning' => __('Moderate cache effectiveness, significant improvement possible', 'redco-optimizer'),
                                        'action' => __('Review exclusion list and expiration settings', 'redco-optimizer')
                                    ),
                                    'poor' => array(
                                        'range' => '<60%',
                                        'meaning' => __('Low cache effectiveness, major optimization needed', 'redco-optimizer'),
                                        'action' => __('Check cache configuration, reduce exclusions, increase expiration', 'redco-optimizer')
                                    )
                                ),
                                'factors_affecting' => array(
                                    __('Cache expiration time - shorter times = more misses', 'redco-optimizer'),
                                    __('Number of excluded pages - more exclusions = fewer hits', 'redco-optimizer'),
                                    __('Traffic patterns - new visitors create more misses', 'redco-optimizer'),
                                    __('Content update frequency - frequent updates invalidate cache', 'redco-optimizer')
                                )
                            ),
                            'cache_misses' => array(
                                'title' => __('Cache Misses', 'redco-optimizer'),
                                'help' => __('Number of times pages had to be generated fresh because no valid cache existed. Some misses are normal and expected.', 'redco-optimizer'),
                                'how_it_works' => __('When a page is requested but no cached version exists or the cache has expired, the page must be generated fresh. This counts as a cache miss.', 'redco-optimizer'),
                                'types_of_misses' => array(
                                    'cold_miss' => array(
                                        'title' => __('Cold Miss', 'redco-optimizer'),
                                        'description' => __('First-time page request with no existing cache', 'redco-optimizer'),
                                        'normal' => true,
                                        'action' => __('Expected behavior, no action needed', 'redco-optimizer')
                                    ),
                                    'expiration_miss' => array(
                                        'title' => __('Expiration Miss', 'redco-optimizer'),
                                        'description' => __('Cache expired and needs regeneration', 'redco-optimizer'),
                                        'normal' => true,
                                        'action' => __('Consider longer expiration if content allows', 'redco-optimizer')
                                    ),
                                    'invalidation_miss' => array(
                                        'title' => __('Invalidation Miss', 'redco-optimizer'),
                                        'description' => __('Cache cleared due to content updates', 'redco-optimizer'),
                                        'normal' => true,
                                        'action' => __('Normal when content is updated', 'redco-optimizer')
                                    ),
                                    'exclusion_miss' => array(
                                        'title' => __('Exclusion Miss', 'redco-optimizer'),
                                        'description' => __('Page is excluded from caching', 'redco-optimizer'),
                                        'normal' => 'conditional',
                                        'action' => __('Review if exclusion is necessary', 'redco-optimizer')
                                    )
                                ),
                                'optimization_strategies' => array(
                                    __('Increase cache expiration time for stable content', 'redco-optimizer'),
                                    __('Reduce number of excluded pages where possible', 'redco-optimizer'),
                                    __('Implement cache preloading for popular pages', 'redco-optimizer'),
                                    __('Use partial caching for semi-dynamic content', 'redco-optimizer')
                                )
                            ),
                            'cache_size' => array(
                                'title' => __('Cache Size', 'redco-optimizer'),
                                'help' => __('Total disk space consumed by cached files. Monitor to ensure adequate server storage and optimal performance.', 'redco-optimizer'),
                                'how_it_works' => __('Each cached page is stored as a file on disk. The total size depends on number of cached pages, page complexity, and cache expiration settings.', 'redco-optimizer'),
                                'size_guidelines' => array(
                                    'small_site' => array(
                                        'pages' => '< 100 pages',
                                        'expected_size' => '10-50 MB',
                                        'considerations' => __('Minimal storage impact, focus on hit ratio', 'redco-optimizer')
                                    ),
                                    'medium_site' => array(
                                        'pages' => '100-1000 pages',
                                        'expected_size' => '50-500 MB',
                                        'considerations' => __('Monitor growth, consider cleanup policies', 'redco-optimizer')
                                    ),
                                    'large_site' => array(
                                        'pages' => '1000+ pages',
                                        'expected_size' => '500 MB - 5 GB',
                                        'considerations' => __('Implement size limits and automated cleanup', 'redco-optimizer')
                                    )
                                ),
                                'management_strategies' => array(
                                    __('Set maximum cache size limits', 'redco-optimizer'),
                                    __('Implement automatic cleanup of old cache files', 'redco-optimizer'),
                                    __('Monitor disk space usage regularly', 'redco-optimizer'),
                                    __('Use cache compression to reduce storage needs', 'redco-optimizer')
                                ),
                                'warning_signs' => array(
                                    __('Rapid size growth may indicate cache not expiring properly', 'redco-optimizer'),
                                    __('Very large cache size may slow down cache operations', 'redco-optimizer'),
                                    __('Disk space warnings require immediate attention', 'redco-optimizer')
                                )
                            ),
                            'hit_ratio' => array(
                                'title' => __('Cache Hit Ratio', 'redco-optimizer'),
                                'help' => __('Percentage of requests served from cache vs total requests. The most important metric for cache performance evaluation.', 'redco-optimizer'),
                                'how_it_works' => __('Calculated as (Cache Hits / Total Requests) × 100. Higher percentages indicate better cache effectiveness and performance gains.', 'redco-optimizer'),
                                'calculation' => __('Hit Ratio = (Cache Hits ÷ (Cache Hits + Cache Misses)) × 100', 'redco-optimizer'),
                                'performance_correlation' => array(
                                    '95%+' => array(
                                        'performance_gain' => '80-90%',
                                        'server_load_reduction' => '85-95%',
                                        'description' => __('Exceptional performance, maximum optimization achieved', 'redco-optimizer')
                                    ),
                                    '85-95%' => array(
                                        'performance_gain' => '60-80%',
                                        'server_load_reduction' => '70-85%',
                                        'description' => __('Excellent performance with minor optimization opportunities', 'redco-optimizer')
                                    ),
                                    '70-85%' => array(
                                        'performance_gain' => '40-60%',
                                        'server_load_reduction' => '50-70%',
                                        'description' => __('Good performance but significant improvement possible', 'redco-optimizer')
                                    ),
                                    '50-70%' => array(
                                        'performance_gain' => '20-40%',
                                        'server_load_reduction' => '30-50%',
                                        'description' => __('Moderate performance, requires optimization', 'redco-optimizer')
                                    ),
                                    '<50%' => array(
                                        'performance_gain' => '0-20%',
                                        'server_load_reduction' => '0-30%',
                                        'description' => __('Poor performance, major configuration review needed', 'redco-optimizer')
                                    )
                                ),
                                'improvement_strategies' => array(
                                    'increase_expiration' => __('Extend cache expiration time for stable content', 'redco-optimizer'),
                                    'reduce_exclusions' => __('Minimize excluded pages to essential ones only', 'redco-optimizer'),
                                    'optimize_content' => __('Reduce dynamic elements that prevent caching', 'redco-optimizer'),
                                    'preload_cache' => __('Implement cache warming for popular pages', 'redco-optimizer')
                                )
                            )
                        ),
                        'metrics' => array(
                            'cache_hits' => __('Successful cache serves - pages delivered from cache without regeneration. Higher is better for performance.', 'redco-optimizer'),
                            'cache_misses' => __('Cache failures - pages that had to be regenerated. Lower is better, but some misses are normal.', 'redco-optimizer'),
                            'cache_size' => __('Total disk space used by cached files. Monitor to ensure adequate server storage.', 'redco-optimizer'),
                            'hit_ratio' => __('Percentage of requests served from cache. Aim for 80%+ for optimal performance.', 'redco-optimizer')
                        ),
                        'recommendations' => array(
                            __('Target 80%+ cache hit ratio for optimal performance', 'redco-optimizer'),
                            __('Monitor cache size to prevent disk space issues', 'redco-optimizer'),
                            __('High miss rates may indicate short expiration times', 'redco-optimizer'),
                            __('Sudden hit rate drops may indicate cache clearing or issues', 'redco-optimizer')
                        )
                    )
                )
            ),
            'lazy-load' => array(
                'title' => __('Lazy Load Help', 'redco-optimizer'),
                'overview' => __('Lazy loading defers the loading of images, videos, and iframes until they are about to enter the viewport. This dramatically reduces initial page load time and bandwidth usage by only loading content when users actually need to see it.', 'redco-optimizer'),
                'how_it_works' => __('Instead of loading all images immediately, lazy loading replaces image sources with placeholders. As users scroll and images approach the viewport, JavaScript detects their proximity and loads the actual images. Modern browsers also support native lazy loading.', 'redco-optimizer'),
                'performance_impact' => __('Can reduce initial page load time by 30-70% and bandwidth usage by 50-80%. Particularly effective on image-heavy sites, galleries, and long-form content.', 'redco-optimizer'),
                'sections' => array(
                    'image-settings' => array(
                        'title' => __('Image Lazy Loading', 'redco-optimizer'),
                        'content' => __('Configure how images are lazy loaded throughout your website to optimize loading performance.', 'redco-optimizer'),
                        'how_it_works' => __('Images are replaced with lightweight placeholders. When users scroll near an image, it loads automatically. This prevents loading dozens of images that users may never see.', 'redco-optimizer'),
                        'performance_impact' => __('Reduces initial page weight by 60-90% on image-heavy pages. Saves bandwidth for users and reduces server load.', 'redco-optimizer'),
                        'fields' => array(
                            'enable_images' => array(
                                'title' => __('Enable Image Lazy Loading', 'redco-optimizer'),
                                'help' => __('Activates lazy loading for all images on your site, excluding those above the fold or marked as critical.', 'redco-optimizer'),
                                'how_it_works' => __('Scans page content and applies lazy loading attributes to img tags. Uses intersection observer API for efficient viewport detection.', 'redco-optimizer'),
                                'performance_impact' => __('Major improvement for image-heavy sites. Minimal impact on sites with few images.', 'redco-optimizer'),
                                'technical_implementation' => array(
                                    __('Uses native browser lazy loading (loading="lazy") when supported', 'redco-optimizer'),
                                    __('Falls back to JavaScript Intersection Observer API', 'redco-optimizer'),
                                    __('Replaces src with data-src until image enters viewport', 'redco-optimizer'),
                                    __('Maintains image dimensions to prevent layout shift', 'redco-optimizer')
                                ),
                                'browser_support' => array(
                                    'native_support' => array(
                                        'chrome' => '76+',
                                        'firefox' => '75+',
                                        'safari' => '15.4+',
                                        'edge' => '79+',
                                        'coverage' => '85%+ of users'
                                    ),
                                    'fallback_support' => array(
                                        'method' => 'JavaScript Intersection Observer',
                                        'coverage' => '95%+ of users',
                                        'performance' => 'Excellent with minimal overhead'
                                    )
                                ),
                                'image_types_affected' => array(
                                    'content_images' => array(
                                        'description' => __('Images within post/page content', 'redco-optimizer'),
                                        'lazy_loaded' => true,
                                        'considerations' => __('Automatically processed, maintains SEO value', 'redco-optimizer')
                                    ),
                                    'featured_images' => array(
                                        'description' => __('Post thumbnails and featured images', 'redco-optimizer'),
                                        'lazy_loaded' => 'conditional',
                                        'considerations' => __('Can be excluded if above the fold', 'redco-optimizer')
                                    ),
                                    'gallery_images' => array(
                                        'description' => __('WordPress gallery and media grid images', 'redco-optimizer'),
                                        'lazy_loaded' => true,
                                        'considerations' => __('Excellent candidate for lazy loading', 'redco-optimizer')
                                    ),
                                    'background_images' => array(
                                        'description' => __('CSS background images', 'redco-optimizer'),
                                        'lazy_loaded' => false,
                                        'considerations' => __('Requires additional CSS/JS implementation', 'redco-optimizer')
                                    )
                                ),
                                'pros' => array(
                                    __('Dramatically faster initial page loads', 'redco-optimizer'),
                                    __('Reduced bandwidth usage', 'redco-optimizer'),
                                    __('Better mobile experience', 'redco-optimizer'),
                                    __('Improved Core Web Vitals scores', 'redco-optimizer'),
                                    __('SEO benefits from faster loading', 'redco-optimizer')
                                ),
                                'cons' => array(
                                    __('Slight delay when scrolling to new images', 'redco-optimizer'),
                                    __('May affect layout if image dimensions not specified', 'redco-optimizer'),
                                    __('Requires JavaScript for fallback support', 'redco-optimizer')
                                ),
                                'best_practices' => array(
                                    __('Always specify width and height attributes', 'redco-optimizer'),
                                    __('Use appropriate placeholder or blur effects', 'redco-optimizer'),
                                    __('Exclude above-the-fold images from lazy loading', 'redco-optimizer'),
                                    __('Test on slow connections to verify experience', 'redco-optimizer')
                                )
                            ),
                            'threshold' => array(
                                'title' => __('Loading Threshold', 'redco-optimizer'),
                                'help' => __('Distance from viewport when images start loading. Higher values load images earlier, lower values save more bandwidth.', 'redco-optimizer'),
                                'how_it_works' => __('Measured in pixels from viewport edge. When an image gets within this distance, loading begins. Balances user experience with performance.', 'redco-optimizer'),
                                'technical_details' => array(
                                    __('Uses Intersection Observer rootMargin property', 'redco-optimizer'),
                                    __('Triggers when image boundary intersects expanded viewport', 'redco-optimizer'),
                                    __('Accounts for user scrolling speed and connection quality', 'redco-optimizer'),
                                    __('Prevents loading images user may never see', 'redco-optimizer')
                                ),
                                'value_analysis' => array(
                                    '0px' => array(
                                        'description' => __('Load only when image enters viewport', 'redco-optimizer'),
                                        'bandwidth_saving' => 'Maximum',
                                        'user_experience' => 'May see loading delay',
                                        'use_case' => __('Very slow connections, data-sensitive users', 'redco-optimizer')
                                    ),
                                    '100px' => array(
                                        'description' => __('Load when image is 100px from viewport', 'redco-optimizer'),
                                        'bandwidth_saving' => 'High',
                                        'user_experience' => 'Minimal loading delay',
                                        'use_case' => __('Mobile users, moderate connections', 'redco-optimizer')
                                    ),
                                    '200px' => array(
                                        'description' => __('Load when image is 200px from viewport', 'redco-optimizer'),
                                        'bandwidth_saving' => 'Good',
                                        'user_experience' => 'Smooth scrolling experience',
                                        'use_case' => __('Balanced approach for most sites', 'redco-optimizer')
                                    ),
                                    '500px' => array(
                                        'description' => __('Load when image is 500px from viewport', 'redco-optimizer'),
                                        'bandwidth_saving' => 'Moderate',
                                        'user_experience' => 'Very smooth, no delays',
                                        'use_case' => __('Fast connections, image-heavy galleries', 'redco-optimizer')
                                    ),
                                    '1000px' => array(
                                        'description' => __('Load when image is 1000px from viewport', 'redco-optimizer'),
                                        'bandwidth_saving' => 'Low',
                                        'user_experience' => 'Instant image appearance',
                                        'use_case' => __('High-speed connections, premium user experience', 'redco-optimizer')
                                    )
                                ),
                                'factors_to_consider' => array(
                                    'connection_speed' => array(
                                        'fast' => __('Higher threshold (500-1000px) for instant loading', 'redco-optimizer'),
                                        'moderate' => __('Medium threshold (200-500px) for balance', 'redco-optimizer'),
                                        'slow' => __('Lower threshold (50-200px) for bandwidth saving', 'redco-optimizer')
                                    ),
                                    'content_type' => array(
                                        'galleries' => __('Higher threshold for smooth browsing experience', 'redco-optimizer'),
                                        'articles' => __('Medium threshold for reading flow', 'redco-optimizer'),
                                        'listings' => __('Lower threshold as users may not scroll far', 'redco-optimizer')
                                    ),
                                    'device_type' => array(
                                        'desktop' => __('Higher threshold due to faster scrolling', 'redco-optimizer'),
                                        'tablet' => __('Medium threshold for touch scrolling', 'redco-optimizer'),
                                        'mobile' => __('Variable based on connection and data costs', 'redco-optimizer')
                                    )
                                ),
                                'recommendations' => array(
                                    __('Fast connections: 200-500px (conservative loading)', 'redco-optimizer'),
                                    __('Slow connections: 100-200px (aggressive saving)', 'redco-optimizer'),
                                    __('Mobile-first: 300-600px (account for scrolling speed)', 'redco-optimizer'),
                                    __('Image galleries: 500-1000px (preload for smooth browsing)', 'redco-optimizer')
                                )
                            ),
                            'exclude_above_fold' => array(
                                'title' => __('Exclude Above-the-Fold Images', 'redco-optimizer'),
                                'help' => __('Prevents lazy loading of images that appear in the initial viewport to avoid delaying critical content rendering.', 'redco-optimizer'),
                                'how_it_works' => __('Identifies images likely to be visible without scrolling and excludes them from lazy loading to ensure immediate display.', 'redco-optimizer'),
                                'importance' => array(
                                    'core_web_vitals' => __('Critical for Largest Contentful Paint (LCP) optimization', 'redco-optimizer'),
                                    'user_experience' => __('Prevents blank space or loading indicators in initial view', 'redco-optimizer'),
                                    'seo_impact' => __('Google considers above-fold loading speed for rankings', 'redco-optimizer')
                                ),
                                'detection_methods' => array(
                                    'position_based' => __('Excludes first N images in content', 'redco-optimizer'),
                                    'css_analysis' => __('Analyzes CSS positioning and viewport size', 'redco-optimizer'),
                                    'manual_exclusion' => __('Allows manual specification of critical images', 'redco-optimizer')
                                ),
                                'recommended_exclusions' => array(
                                    __('Hero images and banners', 'redco-optimizer'),
                                    __('Logo and header images', 'redco-optimizer'),
                                    __('First content image in articles', 'redco-optimizer'),
                                    __('Featured images above the fold', 'redco-optimizer')
                                )
                            )
                        ),
                        'recommendations' => array(
                            __('Always specify image dimensions to prevent layout shift', 'redco-optimizer'),
                            __('Exclude above-the-fold images from lazy loading', 'redco-optimizer'),
                            __('Use appropriate threshold based on your audience', 'redco-optimizer'),
                            __('Test on slow connections to verify user experience', 'redco-optimizer'),
                            __('Consider using WebP format for better compression', 'redco-optimizer')
                        ),
                        'warnings' => array(
                            __('May cause layout shift if image dimensions not set', 'redco-optimizer'),
                            __('Can affect SEO if images are critical for content understanding', 'redco-optimizer'),
                            __('Test thoroughly with your theme and plugins', 'redco-optimizer')
                        )
                    ),
                    'iframe-settings' => array(
                        'title' => __('Iframe Lazy Loading', 'redco-optimizer'),
                        'content' => __('Apply lazy loading to embedded content like YouTube videos, maps, and other iframes to prevent them from blocking page rendering.', 'redco-optimizer'),
                        'how_it_works' => __('Iframes are replaced with placeholder elements until users interact with them or scroll nearby. This prevents heavy embeds from slowing initial page load.', 'redco-optimizer'),
                        'performance_impact' => __('Massive improvement for pages with video embeds or maps. Can reduce initial load time by 2-5 seconds per iframe.', 'redco-optimizer'),
                        'benefits' => array(
                            __('Prevents YouTube/Vimeo embeds from blocking page load', 'redco-optimizer'),
                            __('Reduces external HTTP requests', 'redco-optimizer'),
                            __('Improves privacy by not loading tracking scripts immediately', 'redco-optimizer'),
                            __('Better mobile performance', 'redco-optimizer')
                        ),
                        'recommendations' => array(
                            __('Essential for pages with multiple video embeds', 'redco-optimizer'),
                            __('Use custom thumbnails for better user experience', 'redco-optimizer'),
                            __('Consider click-to-load for privacy-sensitive content', 'redco-optimizer')
                        )
                    )
                )
            ),
            'css-js-minifier' => array(
                'title' => __('CSS/JS Minifier Help', 'redco-optimizer'),
                'overview' => __('Minification removes unnecessary whitespace, comments, and characters from CSS and JavaScript files while preserving functionality. This reduces file sizes by 20-60% and decreases the number of HTTP requests through file combination.', 'redco-optimizer'),
                'how_it_works' => __('The minifier processes CSS and JS files by removing spaces, line breaks, comments, and unnecessary characters. It can also combine multiple files into single files to reduce HTTP requests. The minified files maintain the same functionality while being significantly smaller.', 'redco-optimizer'),
                'performance_impact' => __('Reduces CSS/JS file sizes by 20-60% and HTTP requests by up to 80%. Can improve page load times by 15-40%, especially on sites with many stylesheets and scripts.', 'redco-optimizer'),
                'sections' => array(
                    'css-minification' => array(
                        'title' => __('CSS Minification', 'redco-optimizer'),
                        'content' => __('Optimize CSS files by removing unnecessary characters and optionally combining multiple files into one.', 'redco-optimizer'),
                        'how_it_works' => __('Removes whitespace, comments, and redundant code from CSS files. Can combine multiple CSS files into a single file to reduce HTTP requests. Preserves CSS functionality while dramatically reducing file size.', 'redco-optimizer'),
                        'performance_impact' => __('Reduces CSS file sizes by 30-70% and can eliminate 5-20 HTTP requests per page. Improves render-blocking resource optimization.', 'redco-optimizer'),
                        'fields' => array(
                            'minify_css' => array(
                                'title' => __('Enable CSS Minification', 'redco-optimizer'),
                                'help' => __('Removes whitespace, comments, and unnecessary characters from CSS files to reduce their size.', 'redco-optimizer'),
                                'how_it_works' => __('Processes all CSS files and removes spaces, line breaks, comments, and redundant semicolons while preserving functionality.', 'redco-optimizer'),
                                'performance_impact' => __('Typically reduces CSS file sizes by 30-50%. Minimal processing overhead with significant bandwidth savings.', 'redco-optimizer'),
                                'pros' => array(
                                    __('Smaller file sizes mean faster downloads', 'redco-optimizer'),
                                    __('Reduced bandwidth usage', 'redco-optimizer'),
                                    __('Better mobile performance', 'redco-optimizer'),
                                    __('Improved Core Web Vitals scores', 'redco-optimizer'),
                                    __('No functionality impact when done correctly', 'redco-optimizer')
                                ),
                                'cons' => array(
                                    __('Makes debugging more difficult', 'redco-optimizer'),
                                    __('Potential compatibility issues with poorly written CSS', 'redco-optimizer'),
                                    __('Slight server processing overhead', 'redco-optimizer')
                                )
                            ),
                            'combine_css' => array(
                                'title' => __('Combine CSS Files', 'redco-optimizer'),
                                'help' => __('Merges multiple CSS files into a single file to reduce HTTP requests and improve loading performance.', 'redco-optimizer'),
                                'how_it_works' => __('Combines all CSS files into one large file, reducing the number of HTTP requests. Files are combined in load order to preserve CSS cascade rules.', 'redco-optimizer'),
                                'performance_impact' => __('Can reduce HTTP requests from 10-20 down to 1-2. Major improvement on HTTP/1.1, moderate improvement on HTTP/2.', 'redco-optimizer'),
                                'pros' => array(
                                    __('Dramatically fewer HTTP requests', 'redco-optimizer'),
                                    __('Better performance on HTTP/1.1', 'redco-optimizer'),
                                    __('Simplified caching strategy', 'redco-optimizer'),
                                    __('Reduced server load', 'redco-optimizer')
                                ),
                                'cons' => array(
                                    __('Larger initial download even for simple pages', 'redco-optimizer'),
                                    __('Cache invalidation affects all CSS', 'redco-optimizer'),
                                    __('May break CSS that depends on specific load order', 'redco-optimizer'),
                                    __('Less effective with HTTP/2 multiplexing', 'redco-optimizer')
                                ),
                                'recommendations' => array(
                                    __('Best for HTTP/1.1 sites with many CSS files', 'redco-optimizer'),
                                    __('Consider disabling for HTTP/2 sites with few CSS files', 'redco-optimizer'),
                                    __('Test thoroughly for CSS cascade issues', 'redco-optimizer'),
                                    __('Monitor for increased cache invalidation', 'redco-optimizer')
                                )
                            )
                        ),
                        'warnings' => array(
                            __('May break CSS that relies on specific file order', 'redco-optimizer'),
                            __('Can cause issues with media queries in combined files', 'redco-optimizer'),
                            __('Test all pages after enabling combination', 'redco-optimizer')
                        )
                    ),
                    'js-minification' => array(
                        'title' => __('JavaScript Minification', 'redco-optimizer'),
                        'content' => __('Optimize JavaScript files by removing unnecessary characters and optionally combining files to reduce load times.', 'redco-optimizer'),
                        'how_it_works' => __('Removes whitespace, comments, and shortens variable names in JavaScript files. Can combine multiple JS files while preserving execution order and dependencies.', 'redco-optimizer'),
                        'performance_impact' => __('Reduces JS file sizes by 40-80% and can eliminate 10-30 HTTP requests. Significantly improves script loading performance.', 'redco-optimizer'),
                        'fields' => array(
                            'minify_js' => array(
                                'title' => __('Enable JavaScript Minification', 'redco-optimizer'),
                                'help' => __('Removes whitespace, comments, and optimizes JavaScript code to reduce file sizes while maintaining functionality.', 'redco-optimizer'),
                                'how_it_works' => __('Processes JavaScript files to remove unnecessary characters, shorten variable names, and optimize code structure without changing functionality.', 'redco-optimizer'),
                                'performance_impact' => __('Reduces JS file sizes by 40-70%. Particularly effective for large JavaScript libraries and custom scripts.', 'redco-optimizer'),
                                'pros' => array(
                                    __('Significant file size reduction', 'redco-optimizer'),
                                    __('Faster script parsing and execution', 'redco-optimizer'),
                                    __('Reduced bandwidth usage', 'redco-optimizer'),
                                    __('Better mobile performance', 'redco-optimizer')
                                ),
                                'cons' => array(
                                    __('Makes debugging extremely difficult', 'redco-optimizer'),
                                    __('Potential compatibility issues with complex scripts', 'redco-optimizer'),
                                    __('May break scripts that rely on specific formatting', 'redco-optimizer'),
                                    __('Higher processing overhead than CSS minification', 'redco-optimizer')
                                )
                            ),
                            'combine_js' => array(
                                'title' => __('Combine JavaScript Files', 'redco-optimizer'),
                                'help' => __('Merges multiple JavaScript files into fewer files to reduce HTTP requests while preserving execution order.', 'redco-optimizer'),
                                'how_it_works' => __('Combines JavaScript files while respecting dependencies and execution order. May create multiple combined files to handle different loading requirements (header vs footer).', 'redco-optimizer'),
                                'performance_impact' => __('Can reduce JS HTTP requests by 70-90%. Major performance improvement, especially for sites with many plugins.', 'redco-optimizer'),
                                'pros' => array(
                                    __('Dramatically fewer HTTP requests', 'redco-optimizer'),
                                    __('Faster overall script loading', 'redco-optimizer'),
                                    __('Reduced server load', 'redco-optimizer'),
                                    __('Better performance on slower connections', 'redco-optimizer')
                                ),
                                'cons' => array(
                                    __('Higher risk of breaking functionality', 'redco-optimizer'),
                                    __('Complex dependency management', 'redco-optimizer'),
                                    __('May delay execution of critical scripts', 'redco-optimizer'),
                                    __('Cache invalidation affects all combined scripts', 'redco-optimizer')
                                ),
                                'warnings' => array(
                                    __('High risk of breaking JavaScript functionality', 'redco-optimizer'),
                                    __('May conflict with plugins that expect specific load order', 'redco-optimizer'),
                                    __('Can break scripts that modify the DOM before it\'s ready', 'redco-optimizer'),
                                    __('Requires extensive testing across all site functionality', 'redco-optimizer')
                                )
                            )
                        ),
                        'recommendations' => array(
                            __('Start with minification only, add combination later', 'redco-optimizer'),
                            __('Exclude problematic scripts from combination', 'redco-optimizer'),
                            __('Test all interactive features thoroughly', 'redco-optimizer'),
                            __('Monitor for JavaScript errors in browser console', 'redco-optimizer'),
                            __('Keep development versions for debugging', 'redco-optimizer')
                        )
                    ),
                    'advanced-settings' => array(
                        'title' => __('Advanced Optimization', 'redco-optimizer'),
                        'content' => __('Advanced options for fine-tuning minification behavior and handling edge cases.', 'redco-optimizer'),
                        'fields' => array(
                            'exclude_files' => array(
                                'title' => __('Exclude Files', 'redco-optimizer'),
                                'help' => __('Specify CSS/JS files that should not be minified or combined, useful for problematic scripts or already optimized files.', 'redco-optimizer'),
                                'recommendations' => array(
                                    __('Exclude already minified files (*.min.css, *.min.js)', 'redco-optimizer'),
                                    __('Exclude problematic plugins or themes', 'redco-optimizer'),
                                    __('Exclude inline scripts that break when combined', 'redco-optimizer'),
                                    __('Exclude external CDN resources', 'redco-optimizer')
                                )
                            )
                        )
                    )
                )
            ),
            'database-cleanup' => array(
                'title' => __('Database Cleanup Help', 'redco-optimizer'),
                'overview' => __('Database cleanup removes unnecessary data that accumulates over time, including spam comments, post revisions, expired transients, and orphaned metadata. This optimizes database performance, reduces storage usage, and improves query speed.', 'redco-optimizer'),
                'how_it_works' => __('Scans your WordPress database for various types of unnecessary data and safely removes them. The cleanup process preserves all important content while eliminating bloat that slows down database operations.', 'redco-optimizer'),
                'performance_impact' => __('Can reduce database size by 20-80% and improve query performance by 15-50%. Particularly effective for older sites with lots of accumulated data.', 'redco-optimizer'),
                'sections' => array(
                    'cleanup-options' => array(
                        'title' => __('Cleanup Options', 'redco-optimizer'),
                        'content' => __('Select which types of unnecessary data to remove from your database to optimize performance and reduce storage usage.', 'redco-optimizer'),
                        'how_it_works' => __('Each cleanup option targets specific types of database bloat. You can select individual options based on your site\'s needs and risk tolerance.', 'redco-optimizer'),
                        'fields' => array(
                            'post_revisions' => array(
                                'title' => __('Post Revisions', 'redco-optimizer'),
                                'help' => __('Removes old post and page revisions that are no longer needed, keeping only the most recent versions.', 'redco-optimizer'),
                                'how_it_works' => __('WordPress saves every edit as a revision. This removes old revisions while preserving recent ones for recovery purposes.', 'redco-optimizer'),
                                'performance_impact' => __('Can reduce database size by 10-40% on content-heavy sites. Improves post query performance.', 'redco-optimizer'),
                                'pros' => array(
                                    __('Significantly reduces database size', 'redco-optimizer'),
                                    __('Improves post loading performance', 'redco-optimizer'),
                                    __('Reduces backup sizes', 'redco-optimizer'),
                                    __('Faster database searches', 'redco-optimizer')
                                ),
                                'cons' => array(
                                    __('Loses edit history beyond kept revisions', 'redco-optimizer'),
                                    __('Cannot recover deleted revisions', 'redco-optimizer'),
                                    __('May affect plugins that rely on revision data', 'redco-optimizer')
                                ),
                                'recommendations' => array(
                                    __('Keep 2-5 recent revisions for recovery', 'redco-optimizer'),
                                    __('Safe for most sites with regular content updates', 'redco-optimizer'),
                                    __('Essential for sites with many content editors', 'redco-optimizer')
                                )
                            ),
                            'spam_comments' => array(
                                'title' => __('Spam Comments', 'redco-optimizer'),
                                'help' => __('Removes comments marked as spam, including associated metadata and relationships.', 'redco-optimizer'),
                                'how_it_works' => __('Permanently deletes comments in spam status along with their metadata, reducing database bloat from spam accumulation.', 'redco-optimizer'),
                                'performance_impact' => __('Can reduce database size by 5-30% on sites with heavy spam. Improves comment query performance.', 'redco-optimizer'),
                                'pros' => array(
                                    __('Removes useless spam data', 'redco-optimizer'),
                                    __('Improves comment loading speed', 'redco-optimizer'),
                                    __('Reduces database maintenance overhead', 'redco-optimizer'),
                                    __('Cleaner admin interface', 'redco-optimizer')
                                ),
                                'cons' => array(
                                    __('Cannot recover deleted spam comments', 'redco-optimizer'),
                                    __('May remove false positives', 'redco-optimizer')
                                ),
                                'safety' => array(
                                    __('Review spam folder before cleanup', 'redco-optimizer'),
                                    __('Check for false positive spam detection', 'redco-optimizer')
                                )
                            ),
                            'expired_transients' => array(
                                'title' => __('Expired Transients', 'redco-optimizer'),
                                'help' => __('Removes expired transient data that WordPress and plugins use for temporary caching.', 'redco-optimizer'),
                                'how_it_works' => __('Transients are temporary data with expiration dates. This removes expired entries that WordPress hasn\'t automatically cleaned up.', 'redco-optimizer'),
                                'performance_impact' => __('Reduces database size by 5-20% and improves option table performance. Essential for sites with many plugins.', 'redco-optimizer'),
                                'pros' => array(
                                    __('Removes genuinely useless data', 'redco-optimizer'),
                                    __('Improves option table performance', 'redco-optimizer'),
                                    __('Safe cleanup with no functionality impact', 'redco-optimizer'),
                                    __('Reduces database backup sizes', 'redco-optimizer')
                                ),
                                'cons' => array(
                                    __('Minimal - expired transients are already useless', 'redco-optimizer')
                                ),
                                'recommendations' => array(
                                    __('Safe to run regularly', 'redco-optimizer'),
                                    __('Essential for plugin-heavy sites', 'redco-optimizer'),
                                    __('Should be part of regular maintenance', 'redco-optimizer')
                                )
                            ),
                            'orphaned_metadata' => array(
                                'title' => __('Orphaned Metadata', 'redco-optimizer'),
                                'help' => __('Removes metadata entries that no longer have associated posts, comments, or users.', 'redco-optimizer'),
                                'how_it_works' => __('When posts, comments, or users are deleted, their metadata sometimes remains. This cleanup removes these orphaned entries.', 'redco-optimizer'),
                                'performance_impact' => __('Reduces database size by 2-15% and improves metadata query performance.', 'redco-optimizer'),
                                'pros' => array(
                                    __('Removes truly useless data', 'redco-optimizer'),
                                    __('Improves metadata query speed', 'redco-optimizer'),
                                    __('Cleaner database structure', 'redco-optimizer')
                                ),
                                'cons' => array(
                                    __('Requires careful detection to avoid false positives', 'redco-optimizer'),
                                    __('May affect custom implementations', 'redco-optimizer')
                                )
                            )
                        ),
                        'safety' => array(
                            __('ALWAYS backup your database before cleanup', 'redco-optimizer'),
                            __('Test on staging site first', 'redco-optimizer'),
                            __('Review what will be deleted before proceeding', 'redco-optimizer'),
                            __('Start with safest options (expired transients)', 'redco-optimizer'),
                            __('Monitor site functionality after cleanup', 'redco-optimizer')
                        ),
                        'recommendations' => array(
                            __('Run cleanup during low-traffic periods', 'redco-optimizer'),
                            __('Start with conservative settings', 'redco-optimizer'),
                            __('Schedule regular maintenance cleanups', 'redco-optimizer'),
                            __('Monitor database size trends', 'redco-optimizer')
                        ),
                        'warnings' => array(
                            __('Database cleanup is irreversible without backups', 'redco-optimizer'),
                            __('Some plugins may store important data in unexpected places', 'redco-optimizer'),
                            __('Large cleanups may temporarily slow your site', 'redco-optimizer')
                        )
                    )
                )
            ),
            'heartbeat-control' => array(
                'title' => __('Heartbeat Control Help', 'redco-optimizer'),
                'overview' => __('WordPress Heartbeat API enables real-time features like autosave, post locking, and admin notifications. However, it can consume significant server resources through constant AJAX requests. This module optimizes heartbeat frequency to balance functionality with performance.', 'redco-optimizer'),
                'how_it_works' => __('The Heartbeat API sends AJAX requests to the server at regular intervals (default 15-60 seconds). This module allows you to modify these intervals or disable heartbeat entirely in specific areas to reduce server load.', 'redco-optimizer'),
                'performance_impact' => __('Can reduce server CPU usage by 10-30% and decrease AJAX requests by up to 90%. Particularly beneficial for high-traffic sites and shared hosting environments.', 'redco-optimizer'),
                'sections' => array(
                    'heartbeat-settings' => array(
                        'title' => __('Heartbeat Settings', 'redco-optimizer'),
                        'content' => __('Configure heartbeat frequency for different areas of WordPress to optimize performance while maintaining necessary functionality.', 'redco-optimizer'),
                        'how_it_works' => __('Controls heartbeat intervals in three areas: frontend (public pages), admin dashboard, and post editor. Each area can have different settings based on functionality needs.', 'redco-optimizer'),
                        'fields' => array(
                            'frontend_heartbeat' => array(
                                'title' => __('Frontend Heartbeat', 'redco-optimizer'),
                                'help' => __('Controls heartbeat on public-facing pages. Usually safe to disable as frontend rarely needs real-time features.', 'redco-optimizer'),
                                'how_it_works' => __('Frontend heartbeat is primarily used for logged-in user session management and some plugin features. Most sites don\'t need this.', 'redco-optimizer'),
                                'performance_impact' => __('Disabling can reduce server load by 20-40% for sites with many logged-in users.', 'redco-optimizer'),
                                'pros' => array(
                                    __('Significant server resource savings', 'redco-optimizer'),
                                    __('Reduced AJAX requests', 'redco-optimizer'),
                                    __('Better performance for logged-in users', 'redco-optimizer'),
                                    __('Lower hosting costs', 'redco-optimizer')
                                ),
                                'cons' => array(
                                    __('May affect some plugin real-time features', 'redco-optimizer'),
                                    __('Could impact session management', 'redco-optimizer'),
                                    __('Some admin bar features may not update', 'redco-optimizer')
                                ),
                                'recommendations' => array(
                                    __('Safe to disable for most sites', 'redco-optimizer'),
                                    __('Essential for high-traffic sites', 'redco-optimizer'),
                                    __('Test with your specific plugins', 'redco-optimizer')
                                )
                            ),
                            'admin_heartbeat' => array(
                                'title' => __('Admin Heartbeat', 'redco-optimizer'),
                                'help' => __('Controls heartbeat in WordPress admin dashboard. Affects admin notifications and some real-time features.', 'redco-optimizer'),
                                'how_it_works' => __('Admin heartbeat handles notifications, plugin updates, and dashboard widgets. Reducing frequency maintains functionality while improving performance.', 'redco-optimizer'),
                                'performance_impact' => __('Reducing frequency can decrease admin load times by 10-25% without losing functionality.', 'redco-optimizer'),
                                'recommendations' => array(
                                    __('Reduce to 60-120 seconds instead of disabling', 'redco-optimizer'),
                                    __('Safe to extend intervals for better performance', 'redco-optimizer'),
                                    __('Consider user workflow needs', 'redco-optimizer')
                                )
                            ),
                            'editor_heartbeat' => array(
                                'title' => __('Post Editor Heartbeat', 'redco-optimizer'),
                                'help' => __('Controls heartbeat in post/page editor. Critical for autosave and post locking features.', 'redco-optimizer'),
                                'how_it_works' => __('Editor heartbeat enables autosave, prevents editing conflicts, and provides real-time collaboration features. Most critical heartbeat function.', 'redco-optimizer'),
                                'performance_impact' => __('Moderate impact but essential for content editing safety. Extend intervals rather than disable.', 'redco-optimizer'),
                                'pros' => array(
                                    __('Prevents content loss through autosave', 'redco-optimizer'),
                                    __('Prevents editing conflicts', 'redco-optimizer'),
                                    __('Enables collaborative editing', 'redco-optimizer')
                                ),
                                'cons' => array(
                                    __('Constant server requests during editing', 'redco-optimizer'),
                                    __('Can slow down editor performance', 'redco-optimizer')
                                ),
                                'recommendations' => array(
                                    __('Extend to 30-60 seconds instead of disabling', 'redco-optimizer'),
                                    __('Keep enabled for content safety', 'redco-optimizer'),
                                    __('Consider editor usage patterns', 'redco-optimizer')
                                ),
                                'warnings' => array(
                                    __('Disabling may cause content loss', 'redco-optimizer'),
                                    __('Can break collaborative editing', 'redco-optimizer'),
                                    __('May affect plugin editor features', 'redco-optimizer')
                                )
                            )
                        ),
                        'recommendations' => array(
                            __('Start with frontend disable, admin/editor frequency reduction', 'redco-optimizer'),
                            __('Test thoroughly with your workflow', 'redco-optimizer'),
                            __('Monitor for any lost functionality', 'redco-optimizer'),
                            __('Consider user needs vs performance gains', 'redco-optimizer')
                        ),
                        'warnings' => array(
                            __('May affect real-time plugin features', 'redco-optimizer'),
                            __('Can impact collaborative editing', 'redco-optimizer'),
                            __('Test with all admin users', 'redco-optimizer')
                        )
                    )
                )
            ),
            'emoji-stripper' => array(
                'title' => __('Emoji Stripper Help', 'redco-optimizer'),
                'overview' => __('WordPress automatically loads emoji support scripts and styles on every page, even if you don\'t use emojis. This module removes these unnecessary resources to reduce HTTP requests and improve loading performance.', 'redco-optimizer'),
                'how_it_works' => __('Removes the wp-emoji-release.min.js script and related CSS that WordPress loads by default. Emojis will still display correctly using native browser support, but without the extra WordPress processing.', 'redco-optimizer'),
                'performance_impact' => __('Reduces HTTP requests by 1-2 per page and saves 10-15KB of resources. Small but measurable improvement, especially on mobile connections.', 'redco-optimizer'),
                'sections' => array(
                    'emoji-settings' => array(
                        'title' => __('Emoji Settings', 'redco-optimizer'),
                        'content' => __('Control whether WordPress loads emoji-related scripts and styles on your website.', 'redco-optimizer'),
                        'how_it_works' => __('Disables WordPress emoji processing while maintaining emoji display through native browser support. Modern browsers handle emojis natively without additional scripts.', 'redco-optimizer'),
                        'performance_impact' => __('Eliminates 1-2 HTTP requests and 10-15KB per page load. Cumulative benefit across all page views.', 'redco-optimizer'),
                        'pros' => array(
                            __('Reduces HTTP requests', 'redco-optimizer'),
                            __('Smaller page size', 'redco-optimizer'),
                            __('Faster loading times', 'redco-optimizer'),
                            __('Less JavaScript processing', 'redco-optimizer'),
                            __('Better mobile performance', 'redco-optimizer'),
                            __('Emojis still work in modern browsers', 'redco-optimizer')
                        ),
                        'cons' => array(
                            __('May affect emoji display in very old browsers', 'redco-optimizer'),
                            __('Some plugins might expect emoji scripts', 'redco-optimizer')
                        ),
                        'recommendations' => array(
                            __('Safe to enable for most modern websites', 'redco-optimizer'),
                            __('Essential for performance-focused sites', 'redco-optimizer'),
                            __('Test emoji display if you use them frequently', 'redco-optimizer'),
                            __('Particularly beneficial for mobile users', 'redco-optimizer')
                        )
                    )
                )
            ),
            'autosave-reducer' => array(
                'title' => __('Autosave Reducer Help', 'redco-optimizer'),
                'overview' => __('WordPress automatically saves post drafts every 60 seconds by default, which can create excessive database writes and server load. This module allows you to reduce autosave frequency to improve performance while maintaining content safety.', 'redco-optimizer'),
                'how_it_works' => __('Modifies WordPress autosave intervals by changing the AUTOSAVE_INTERVAL constant. Longer intervals mean fewer database writes and reduced server load during content editing.', 'redco-optimizer'),
                'performance_impact' => __('Reduces database writes by 50-80% during editing sessions and decreases server load by 10-20% for content-heavy sites.', 'redco-optimizer'),
                'sections' => array(
                    'autosave-settings' => array(
                        'title' => __('Autosave Settings', 'redco-optimizer'),
                        'content' => __('Configure how often WordPress automatically saves post drafts to balance performance with content safety.', 'redco-optimizer'),
                        'how_it_works' => __('Changes the frequency of automatic draft saves. WordPress default is 60 seconds, but this can be extended to reduce server load while still providing content protection.', 'redco-optimizer'),
                        'performance_impact' => __('Longer intervals significantly reduce database writes and server processing during editing sessions.', 'redco-optimizer'),
                        'fields' => array(
                            'autosave_interval' => array(
                                'title' => __('Autosave Interval', 'redco-optimizer'),
                                'help' => __('Time in seconds between automatic saves. Longer intervals improve performance but increase risk of content loss.', 'redco-optimizer'),
                                'how_it_works' => __('WordPress saves drafts at this interval. Longer intervals mean fewer database operations but larger potential content loss if browser crashes.', 'redco-optimizer'),
                                'performance_impact' => __('Each doubling of interval roughly halves database write load during editing.', 'redco-optimizer'),
                                'pros' => array(
                                    __('Reduced database writes', 'redco-optimizer'),
                                    __('Lower server load during editing', 'redco-optimizer'),
                                    __('Better performance for content editors', 'redco-optimizer'),
                                    __('Reduced hosting resource usage', 'redco-optimizer')
                                ),
                                'cons' => array(
                                    __('Higher risk of content loss', 'redco-optimizer'),
                                    __('Longer recovery time after crashes', 'redco-optimizer'),
                                    __('May affect user confidence', 'redco-optimizer')
                                ),
                                'recommendations' => array(
                                    __('120-180 seconds for most sites (2-3x default)', 'redco-optimizer'),
                                    __('300-600 seconds for high-performance needs', 'redco-optimizer'),
                                    __('Consider your editors\' working patterns', 'redco-optimizer'),
                                    __('Balance performance vs content safety', 'redco-optimizer'),
                                    __('Train editors to save manually more often', 'redco-optimizer')
                                )
                            )
                        ),
                        'recommendations' => array(
                            __('Start with 2-3x the default interval (120-180 seconds)', 'redco-optimizer'),
                            __('Monitor editor feedback about content safety', 'redco-optimizer'),
                            __('Consider your content creation workflow', 'redco-optimizer'),
                            __('Educate editors about manual saving', 'redco-optimizer'),
                            __('Test with your typical editing sessions', 'redco-optimizer')
                        ),
                        'warnings' => array(
                            __('Longer intervals increase content loss risk', 'redco-optimizer'),
                            __('May affect editor confidence and workflow', 'redco-optimizer'),
                            __('Consider your editors\' technical comfort level', 'redco-optimizer')
                        )
                    )
                )
            ),
            'css-js-minifier' => array(
                'title' => __('CSS/JS Minifier Help', 'redco-optimizer'),
                'overview' => __('Minifies CSS and JavaScript files by removing whitespace, comments, and unnecessary characters to reduce file sizes and improve loading performance.', 'redco-optimizer'),
                'how_it_works' => __('Processes CSS and JavaScript files to remove all non-essential characters including spaces, line breaks, comments, and redundant code while preserving functionality.', 'redco-optimizer'),
                'performance_impact' => __('Reduces file sizes by 20-60%, improving page load times by 10-30%. Particularly effective for sites with large CSS/JS files.', 'redco-optimizer'),
                'sections' => array(
                    'css-minification' => array(
                        'title' => __('CSS Minification', 'redco-optimizer'),
                        'content' => __('Configure CSS file minification to reduce stylesheet sizes and improve loading performance.', 'redco-optimizer'),
                        'how_it_works' => __('Removes whitespace, comments, and unnecessary characters from CSS files while preserving all styling rules and functionality.', 'redco-optimizer'),
                        'performance_impact' => __('Reduces CSS file sizes by 25-50%, improving initial page render times and reducing bandwidth usage.', 'redco-optimizer'),
                        'fields' => array(
                            'minify_css' => array(
                                'title' => __('Enable CSS Minification', 'redco-optimizer'),
                                'help' => __('Automatically minifies all CSS files to reduce their size and improve loading performance.', 'redco-optimizer'),
                                'how_it_works' => __('Processes CSS files during page load, removing whitespace, comments, and redundant code while maintaining all styling functionality.', 'redco-optimizer'),
                                'performance_impact' => __('Reduces CSS file sizes by 25-50%, improving page load times and reducing bandwidth usage.', 'redco-optimizer'),
                                'technical_details' => array(
                                    __('Removes all whitespace and line breaks', 'redco-optimizer'),
                                    __('Strips CSS comments and documentation', 'redco-optimizer'),
                                    __('Optimizes color codes and values', 'redco-optimizer'),
                                    __('Preserves all CSS functionality and rules', 'redco-optimizer')
                                ),
                                'pros' => array(
                                    __('Significantly smaller file sizes', 'redco-optimizer'),
                                    __('Faster page loading times', 'redco-optimizer'),
                                    __('Reduced bandwidth usage', 'redco-optimizer'),
                                    __('Better mobile performance', 'redco-optimizer')
                                ),
                                'cons' => array(
                                    __('CSS files become unreadable for debugging', 'redco-optimizer'),
                                    __('Slight processing overhead during minification', 'redco-optimizer'),
                                    __('May complicate troubleshooting CSS issues', 'redco-optimizer')
                                ),
                                'best_practices' => array(
                                    __('Keep original CSS files for development', 'redco-optimizer'),
                                    __('Test thoroughly after enabling minification', 'redco-optimizer'),
                                    __('Use browser dev tools for debugging minified CSS', 'redco-optimizer'),
                                    __('Consider excluding problematic CSS files if needed', 'redco-optimizer')
                                )
                            ),
                            'minify_js' => array(
                                'title' => __('Enable JavaScript Minification', 'redco-optimizer'),
                                'help' => __('Automatically minifies JavaScript files to reduce their size and improve loading performance.', 'redco-optimizer'),
                                'how_it_works' => __('Processes JavaScript files to remove whitespace, comments, and unnecessary characters while preserving all functionality.', 'redco-optimizer'),
                                'performance_impact' => __('Reduces JavaScript file sizes by 30-60%, improving page load times and script execution.', 'redco-optimizer'),
                                'technical_details' => array(
                                    __('Removes whitespace and line breaks', 'redco-optimizer'),
                                    __('Strips JavaScript comments', 'redco-optimizer'),
                                    __('Optimizes variable names and syntax', 'redco-optimizer'),
                                    __('Preserves all JavaScript functionality', 'redco-optimizer')
                                ),
                                'pros' => array(
                                    __('Dramatically smaller JavaScript files', 'redco-optimizer'),
                                    __('Faster script loading and execution', 'redco-optimizer'),
                                    __('Reduced bandwidth consumption', 'redco-optimizer'),
                                    __('Better performance on slow connections', 'redco-optimizer')
                                ),
                                'cons' => array(
                                    __('JavaScript becomes unreadable', 'redco-optimizer'),
                                    __('Debugging becomes more difficult', 'redco-optimizer'),
                                    __('Potential compatibility issues with some scripts', 'redco-optimizer'),
                                    __('Processing overhead during minification', 'redco-optimizer')
                                ),
                                'troubleshooting' => array(
                                    __('If scripts break: Exclude problematic files from minification', 'redco-optimizer'),
                                    __('For debugging: Use browser dev tools source maps', 'redco-optimizer'),
                                    __('Test all interactive features after enabling', 'redco-optimizer'),
                                    __('Check console for JavaScript errors', 'redco-optimizer')
                                )
                            )
                        )
                    ),
                    'advanced-settings' => array(
                        'title' => __('Advanced Settings', 'redco-optimizer'),
                        'content' => __('Configure advanced minification options and exclusions for specific files that may cause issues.', 'redco-optimizer'),
                        'how_it_works' => __('Allows fine-tuning of minification behavior and excluding specific files that may not work properly when minified.', 'redco-optimizer'),
                        'fields' => array(
                            'exclude_files' => array(
                                'title' => __('Exclude Files from Minification', 'redco-optimizer'),
                                'help' => __('Select specific CSS and JavaScript files to exclude from minification if they cause issues.', 'redco-optimizer'),
                                'how_it_works' => __('Creates an exclusion list of files that will be served in their original form without minification processing.', 'redco-optimizer'),
                                'when_to_exclude' => array(
                                    __('Files that break when minified', 'redco-optimizer'),
                                    __('Already minified files (*.min.css, *.min.js)', 'redco-optimizer'),
                                    __('Files with complex syntax or dependencies', 'redco-optimizer'),
                                    __('Third-party scripts that require specific formatting', 'redco-optimizer')
                                ),
                                'recommendations' => array(
                                    __('Test each file individually if issues occur', 'redco-optimizer'),
                                    __('Exclude files one by one to identify problems', 'redco-optimizer'),
                                    __('Keep exclusions to minimum for best performance', 'redco-optimizer'),
                                    __('Monitor for JavaScript console errors', 'redco-optimizer')
                                )
                            )
                        )
                    )
                )
            ),
            'database-cleanup' => array(
                'title' => __('Database Cleanup Help', 'redco-optimizer'),
                'overview' => __('Cleans up unnecessary data from your WordPress database to improve performance and reduce database size.', 'redco-optimizer'),
                'how_it_works' => __('Removes various types of unnecessary data including post revisions, spam comments, expired transients, and other database bloat.', 'redco-optimizer'),
                'performance_impact' => __('Reduces database size by 10-50% and improves query performance. Particularly effective for older sites with lots of content.', 'redco-optimizer'),
                'sections' => array(
                    'cleanup-options' => array(
                        'title' => __('Cleanup Options', 'redco-optimizer'),
                        'content' => __('Configure which types of data to clean from your database and set up automatic maintenance schedules.', 'redco-optimizer'),
                        'how_it_works' => __('Systematically removes different types of unnecessary data while preserving all important content and functionality.', 'redco-optimizer'),
                        'fields' => array(
                            'auto_cleanup' => array(
                                'title' => __('Automatic Cleanup', 'redco-optimizer'),
                                'help' => __('Enable automatic database cleanup to run on a scheduled basis without manual intervention.', 'redco-optimizer'),
                                'how_it_works' => __('Uses WordPress cron system to automatically run cleanup tasks at specified intervals.', 'redco-optimizer'),
                                'performance_impact' => __('Maintains optimal database performance over time without manual maintenance.', 'redco-optimizer'),
                                'pros' => array(
                                    __('Automated maintenance without manual work', 'redco-optimizer'),
                                    __('Consistent database performance', 'redco-optimizer'),
                                    __('Prevents database bloat accumulation', 'redco-optimizer')
                                ),
                                'cons' => array(
                                    __('Uses server resources during cleanup', 'redco-optimizer'),
                                    __('May temporarily slow site during cleanup', 'redco-optimizer')
                                )
                            ),
                            'post_revisions' => array(
                                'title' => __('Post Revisions Cleanup', 'redco-optimizer'),
                                'help' => __('Remove old post and page revisions that accumulate over time and consume database space.', 'redco-optimizer'),
                                'how_it_works' => __('Deletes revision records from wp_posts table while preserving the current published version and recent revisions.', 'redco-optimizer'),
                                'performance_impact' => __('Can reduce database size significantly on content-heavy sites. Improves query performance.', 'redco-optimizer'),
                                'safety_considerations' => array(
                                    __('Only removes old revisions, keeps current content', 'redco-optimizer'),
                                    __('Can optionally keep a specified number of recent revisions', 'redco-optimizer'),
                                    __('Does not affect published content or functionality', 'redco-optimizer')
                                )
                            ),
                            'spam_comments' => array(
                                'title' => __('Spam Comments Cleanup', 'redco-optimizer'),
                                'help' => __('Remove comments that have been marked as spam by WordPress or anti-spam plugins.', 'redco-optimizer'),
                                'how_it_works' => __('Deletes comments with spam status from wp_comments table along with associated metadata.', 'redco-optimizer'),
                                'performance_impact' => __('Reduces database size and improves comment-related query performance.', 'redco-optimizer'),
                                'safety_note' => __('Only removes comments already marked as spam - does not affect legitimate comments.', 'redco-optimizer')
                            ),
                            'expired_transients' => array(
                                'title' => __('Expired Transients Cleanup', 'redco-optimizer'),
                                'help' => __('Remove expired transient data that WordPress and plugins use for temporary caching but fail to clean up.', 'redco-optimizer'),
                                'how_it_works' => __('Deletes expired transient options from wp_options table that should have been automatically removed but weren\'t.', 'redco-optimizer'),
                                'performance_impact' => __('Reduces wp_options table size and improves option loading performance.', 'redco-optimizer'),
                                'technical_details' => array(
                                    __('Transients are temporary cached data with expiration times', 'redco-optimizer'),
                                    __('WordPress sometimes fails to clean expired transients', 'redco-optimizer'),
                                    __('Accumulated expired transients can bloat wp_options table', 'redco-optimizer'),
                                    __('Safe to remove as they are already expired', 'redco-optimizer')
                                )
                            )
                        )
                    )
                )
            ),
            'heartbeat-control' => array(
                'title' => __('Heartbeat Control Help', 'redco-optimizer'),
                'overview' => __('Controls WordPress Heartbeat API frequency to reduce server load and improve performance by managing background AJAX requests.', 'redco-optimizer'),
                'how_it_works' => __('Modifies or disables the WordPress Heartbeat API that sends regular AJAX requests for features like autosave, user activity detection, and real-time updates.', 'redco-optimizer'),
                'performance_impact' => __('Reduces server load by 10-40% and decreases bandwidth usage. Particularly effective on high-traffic sites or shared hosting.', 'redco-optimizer'),
                'sections' => array(
                    'heartbeat-settings' => array(
                        'title' => __('Heartbeat Settings', 'redco-optimizer'),
                        'content' => __('Configure WordPress Heartbeat API behavior for different areas of your site to optimize performance.', 'redco-optimizer'),
                        'how_it_works' => __('Controls the frequency of background AJAX requests that WordPress uses for real-time features and autosave functionality.', 'redco-optimizer'),
                        'fields' => array(
                            'admin_heartbeat' => array(
                                'title' => __('Admin Area Heartbeat', 'redco-optimizer'),
                                'help' => __('Control heartbeat frequency in WordPress admin dashboard and settings pages.', 'redco-optimizer'),
                                'how_it_works' => __('Manages background requests in admin area used for notifications, user activity, and real-time updates.', 'redco-optimizer'),
                                'value_options' => array(
                                    'default' => array(
                                        'label' => __('Default (15 seconds)', 'redco-optimizer'),
                                        'use_case' => __('Standard WordPress behavior, maximum real-time features', 'redco-optimizer'),
                                        'server_impact' => __('High - frequent requests every 15 seconds', 'redco-optimizer'),
                                        'user_experience' => __('Immediate notifications and updates', 'redco-optimizer')
                                    ),
                                    'modify' => array(
                                        'label' => __('Modify Frequency', 'redco-optimizer'),
                                        'use_case' => __('Balanced approach - reduce frequency but keep functionality', 'redco-optimizer'),
                                        'server_impact' => __('Medium - customizable frequency (30-300 seconds)', 'redco-optimizer'),
                                        'user_experience' => __('Slightly delayed updates but still functional', 'redco-optimizer')
                                    ),
                                    'disable' => array(
                                        'label' => __('Disable Completely', 'redco-optimizer'),
                                        'use_case' => __('Maximum performance, minimal real-time features needed', 'redco-optimizer'),
                                        'server_impact' => __('Minimal - no background requests', 'redco-optimizer'),
                                        'user_experience' => __('No real-time updates, manual refresh needed', 'redco-optimizer')
                                    )
                                ),
                                'recommendations' => array(
                                    __('Shared hosting: Modify to 60-120 seconds or disable', 'redco-optimizer'),
                                    __('VPS/Dedicated: Modify to 30-60 seconds', 'redco-optimizer'),
                                    __('High-traffic sites: Consider disabling for performance', 'redco-optimizer'),
                                    __('Single-user sites: Safe to disable or use long intervals', 'redco-optimizer')
                                )
                            ),
                            'editor_heartbeat' => array(
                                'title' => __('Post Editor Heartbeat', 'redco-optimizer'),
                                'help' => __('Control heartbeat frequency in post and page editor for autosave and user activity detection.', 'redco-optimizer'),
                                'how_it_works' => __('Manages autosave functionality and user activity detection while editing posts and pages.', 'redco-optimizer'),
                                'importance' => __('More critical than admin heartbeat as it affects content safety through autosave functionality.', 'redco-optimizer'),
                                'value_options' => array(
                                    'default' => array(
                                        'label' => __('Default (15 seconds)', 'redco-optimizer'),
                                        'use_case' => __('Maximum content protection, frequent autosaves', 'redco-optimizer'),
                                        'content_safety' => __('Excellent - minimal content loss risk', 'redco-optimizer'),
                                        'server_impact' => __('High during editing sessions', 'redco-optimizer')
                                    ),
                                    'modify' => array(
                                        'label' => __('Modify Frequency', 'redco-optimizer'),
                                        'use_case' => __('Balanced content protection with reduced server load', 'redco-optimizer'),
                                        'content_safety' => __('Good - reasonable content protection', 'redco-optimizer'),
                                        'server_impact' => __('Medium - customizable (30-120 seconds)', 'redco-optimizer')
                                    ),
                                    'disable' => array(
                                        'label' => __('Disable Completely', 'redco-optimizer'),
                                        'use_case' => __('Manual save only, maximum performance', 'redco-optimizer'),
                                        'content_safety' => __('Risk - no automatic content protection', 'redco-optimizer'),
                                        'server_impact' => __('Minimal - no background requests', 'redco-optimizer')
                                    )
                                ),
                                'warnings' => array(
                                    __('Disabling removes autosave protection', 'redco-optimizer'),
                                    __('Long intervals increase content loss risk', 'redco-optimizer'),
                                    __('Consider editor workflow and habits', 'redco-optimizer')
                                )
                            ),
                            'frontend_heartbeat' => array(
                                'title' => __('Frontend Heartbeat', 'redco-optimizer'),
                                'help' => __('Control heartbeat on frontend pages visible to site visitors.', 'redco-optimizer'),
                                'how_it_works' => __('Manages background requests on public pages, typically used for logged-in user features.', 'redco-optimizer'),
                                'recommendation' => __('Usually safe to disable as frontend visitors rarely need real-time features.', 'redco-optimizer'),
                                'value_options' => array(
                                    'disable' => array(
                                        'label' => __('Disable (Recommended)', 'redco-optimizer'),
                                        'use_case' => __('Best for most sites - visitors don\'t need real-time features', 'redco-optimizer'),
                                        'performance_benefit' => __('Significant - eliminates all frontend background requests', 'redco-optimizer'),
                                        'user_impact' => __('None for regular visitors', 'redco-optimizer')
                                    ),
                                    'modify' => array(
                                        'label' => __('Modify Frequency', 'redco-optimizer'),
                                        'use_case' => __('Sites with logged-in user interactions on frontend', 'redco-optimizer'),
                                        'performance_benefit' => __('Moderate - reduced frequency', 'redco-optimizer'),
                                        'user_impact' => __('Delayed updates for logged-in users', 'redco-optimizer')
                                    )
                                )
                            )
                        )
                    )
                )
            ),
            'emoji-stripper' => array(
                'title' => __('Emoji Stripper Help', 'redco-optimizer'),
                'overview' => __('Removes WordPress emoji support scripts and styles to reduce page load time and eliminate unnecessary HTTP requests.', 'redco-optimizer'),
                'how_it_works' => __('Disables WordPress built-in emoji support by removing emoji detection scripts, styles, and DNS prefetch requests.', 'redco-optimizer'),
                'performance_impact' => __('Reduces page load time by 50-200ms and eliminates 2-3 HTTP requests per page. Saves bandwidth and improves Core Web Vitals.', 'redco-optimizer'),
                'sections' => array(
                    'emoji-settings' => array(
                        'title' => __('Emoji Settings', 'redco-optimizer'),
                        'content' => __('Configure emoji support removal to improve page loading performance.', 'redco-optimizer'),
                        'fields' => array(
                            'remove_emoji' => array(
                                'title' => __('Remove Emoji Support', 'redco-optimizer'),
                                'help' => __('Disable WordPress built-in emoji support to improve page loading performance.', 'redco-optimizer'),
                                'how_it_works' => __('Removes emoji detection scripts, CSS styles, and DNS prefetch requests that WordPress adds to every page.', 'redco-optimizer'),
                                'what_gets_removed' => array(
                                    __('wp-emoji-release.min.js script (12KB)', 'redco-optimizer'),
                                    __('Emoji detection and replacement JavaScript', 'redco-optimizer'),
                                    __('DNS prefetch to s.w.org for emoji images', 'redco-optimizer'),
                                    __('Emoji-related CSS styles', 'redco-optimizer')
                                ),
                                'performance_benefits' => array(
                                    __('Eliminates 2-3 HTTP requests per page', 'redco-optimizer'),
                                    __('Saves 12KB+ of JavaScript download', 'redco-optimizer'),
                                    __('Reduces DNS lookup time', 'redco-optimizer'),
                                    __('Improves First Contentful Paint (FCP)', 'redco-optimizer'),
                                    __('Better mobile performance on slow connections', 'redco-optimizer')
                                ),
                                'compatibility' => array(
                                    __('Emojis will still display if typed directly', 'redco-optimizer'),
                                    __('Modern browsers have native emoji support', 'redco-optimizer'),
                                    __('No impact on emoji functionality in most cases', 'redco-optimizer'),
                                    __('Safe to enable on virtually all sites', 'redco-optimizer')
                                ),
                                'when_to_use' => array(
                                    __('All sites focused on performance', 'redco-optimizer'),
                                    __('Sites that don\'t heavily use emojis', 'redco-optimizer'),
                                    __('Mobile-optimized websites', 'redco-optimizer'),
                                    __('Sites targeting Core Web Vitals improvement', 'redco-optimizer')
                                ),
                                'pros' => array(
                                    __('Faster page loading', 'redco-optimizer'),
                                    __('Fewer HTTP requests', 'redco-optimizer'),
                                    __('Reduced bandwidth usage', 'redco-optimizer'),
                                    __('Better mobile performance', 'redco-optimizer'),
                                    __('Improved Core Web Vitals scores', 'redco-optimizer')
                                ),
                                'cons' => array(
                                    __('Emoji conversion may not work in older browsers', 'redco-optimizer'),
                                    __('Some emoji-heavy themes may be affected', 'redco-optimizer')
                                )
                            )
                        )
                    )
                )
            ),
            'query-string-remover' => array(
                'title' => __('Query String Remover Help', 'redco-optimizer'),
                'overview' => __('Removes version query strings from CSS and JavaScript files to improve caching effectiveness and reduce cache-busting issues.', 'redco-optimizer'),
                'how_it_works' => __('Strips version parameters (?ver=1.0) from static resource URLs, allowing better caching by browsers and CDNs while maintaining functionality.', 'redco-optimizer'),
                'performance_impact' => __('Improves cache hit rates by 10-30% and reduces unnecessary resource reloads. Particularly effective for sites with many plugins.', 'redco-optimizer')
            ),
            'asset-version-remover' => array(
                'title' => __('Asset Version Remover Help', 'redco-optimizer'),
                'overview' => __('Removes version numbers from CSS and JavaScript URLs to improve caching and reduce fingerprinting.', 'redco-optimizer'),
                'how_it_works' => __('Eliminates version query strings that can prevent proper caching and create unnecessary cache invalidation.', 'redco-optimizer'),
                'performance_impact' => __('Better browser caching and reduced resource reloading. Improves cache efficiency by 15-25%.', 'redco-optimizer')
            )
        );
    }

    /**
     * Render help icon for a section or field
     */
    public function render_help_icon($module, $section, $field = '', $tooltip = '') {
        $help_id = $module . '-' . $section . ($field ? '-' . $field : '');
        $tooltip_attr = $tooltip ? 'data-tooltip="' . esc_attr($tooltip) . '"' : '';

        return sprintf(
            '<span class="redco-help-icon" data-module="%s" data-section="%s" data-field="%s" %s>
                <span class="dashicons dashicons-editor-help"></span>
            </span>',
            esc_attr($module),
            esc_attr($section),
            esc_attr($field),
            $tooltip_attr
        );
    }

    /**
     * Render help content for display
     */
    private function render_help_content($help_data, $section = '', $field = '') {
        if (empty($help_data)) {
            return '<p>' . __('No help available for this item.', 'redco-optimizer') . '</p>';
        }

        $content = '<div class="help-content">';

        // Handle field-specific help
        if (!empty($help_data['is_field_help']) && !empty($help_data['field_data'])) {
            $field_data = $help_data['field_data'];

            $content .= '<div class="help-field-detail">';
            $content .= '<h3>' . esc_html($help_data['title']) . '</h3>';
            $content .= '<p>' . esc_html($field_data['help']) . '</p>';

            // Field how it works
            if (!empty($field_data['how_it_works'])) {
                $content .= '<div class="help-detail">';
                $content .= '<h4>' . __('How It Works', 'redco-optimizer') . '</h4>';
                $content .= '<p>' . esc_html($field_data['how_it_works']) . '</p>';
                $content .= '</div>';
            }

            // Field performance impact
            if (!empty($field_data['performance_impact'])) {
                $content .= '<div class="help-detail performance-impact">';
                $content .= '<h4>' . __('Performance Impact', 'redco-optimizer') . '</h4>';
                $content .= '<p>' . esc_html($field_data['performance_impact']) . '</p>';
                $content .= '</div>';
            }

            // Render all the detailed field information
            $content .= $this->render_field_details($field_data);

            $content .= '</div>';
        }
        // Handle section-specific help
        else if (!empty($help_data['is_section_help']) && !empty($help_data['section_data'])) {
            $section_data = $help_data['section_data'];

            $content .= '<div class="help-section-detail">';
            $content .= '<h3>' . esc_html($help_data['title']) . '</h3>';
            $content .= '<p>' . esc_html($section_data['content']) . '</p>';

            // Section how it works
            if (!empty($section_data['how_it_works'])) {
                $content .= '<div class="help-subsection">';
                $content .= '<h4>' . __('How It Works', 'redco-optimizer') . '</h4>';
                $content .= '<p>' . esc_html($section_data['how_it_works']) . '</p>';
                $content .= '</div>';
            }

            // Section performance impact
            if (!empty($section_data['performance_impact'])) {
                $content .= '<div class="help-subsection performance-impact">';
                $content .= '<h4>' . __('Performance Impact', 'redco-optimizer') . '</h4>';
                $content .= '<p>' . esc_html($section_data['performance_impact']) . '</p>';
                $content .= '</div>';
            }

            // Section recommendations
            if (!empty($section_data['recommendations'])) {
                $content .= '<div class="help-recommendations">';
                $content .= '<h4>' . __('Recommendations', 'redco-optimizer') . '</h4>';
                $content .= '<ul>';
                foreach ($section_data['recommendations'] as $recommendation) {
                    $content .= '<li>' . esc_html($recommendation) . '</li>';
                }
                $content .= '</ul>';
                $content .= '</div>';
            }

            // Section warnings
            if (!empty($section_data['warnings'])) {
                $content .= '<div class="help-warnings">';
                $content .= '<h4>' . __('Warnings', 'redco-optimizer') . '</h4>';
                $content .= '<ul>';
                foreach ($section_data['warnings'] as $warning) {
                    $content .= '<li>' . esc_html($warning) . '</li>';
                }
                $content .= '</ul>';
                $content .= '</div>';
            }

            $content .= '</div>';
        }
        // Handle module overview
        else {
            $content .= '<div class="help-overview">';
            $content .= '<h3>' . esc_html($help_data['title']) . '</h3>';

            if (!empty($help_data['overview'])) {
                $content .= '<p>' . esc_html($help_data['overview']) . '</p>';
            }

            // How it works
            if (!empty($help_data['how_it_works'])) {
                $content .= '<div class="help-subsection">';
                $content .= '<h4>' . __('How It Works', 'redco-optimizer') . '</h4>';
                $content .= '<p>' . esc_html($help_data['how_it_works']) . '</p>';
                $content .= '</div>';
            }

            // Performance impact
            if (!empty($help_data['performance_impact'])) {
                $content .= '<div class="help-subsection performance-impact">';
                $content .= '<h4>' . __('Performance Impact', 'redco-optimizer') . '</h4>';
                $content .= '<p>' . esc_html($help_data['performance_impact']) . '</p>';
                $content .= '</div>';
            }

            $content .= '</div>';
        }

        $content .= '</div>';

        return $content;
    }

    /**
     * Render detailed field information
     */
    private function render_field_details($field_data) {
        $content = '';

        // Technical details
        if (!empty($field_data['technical_details'])) {
            $content .= '<div class="help-detail technical-details">';
            $content .= '<h5>' . __('Technical Details', 'redco-optimizer') . '</h5>';
            $content .= '<ul>';
            foreach ($field_data['technical_details'] as $detail) {
                $content .= '<li>' . esc_html($detail) . '</li>';
            }
            $content .= '</ul>';
            $content .= '</div>';
        }

        // Value options analysis
        if (!empty($field_data['value_options'])) {
            $content .= '<div class="help-detail value-options">';
            $content .= '<h5>' . __('Value Options Analysis', 'redco-optimizer') . '</h5>';
            foreach ($field_data['value_options'] as $value => $option_data) {
                $content .= '<div class="value-option">';
                $content .= '<h6><strong>' . esc_html($option_data['label']) . ' (' . esc_html($value) . ')</strong></h6>';
                $content .= '<p>' . esc_html($option_data['use_case']) . '</p>';
                $content .= '<p><em>Server Impact:</em> ' . esc_html($option_data['server_impact']) . '</p>';
                $content .= '<p><em>User Experience:</em> ' . esc_html($option_data['user_experience']) . '</p>';
                $content .= '</div>';
            }
            $content .= '</div>';
        }

        // Value analysis for thresholds
        if (!empty($field_data['value_analysis'])) {
            $content .= '<div class="help-detail value-analysis">';
            $content .= '<h5>' . __('Value Analysis', 'redco-optimizer') . '</h5>';
            foreach ($field_data['value_analysis'] as $value => $analysis) {
                $content .= '<div class="analysis-item">';
                $content .= '<h6><strong>' . esc_html($value) . '</strong></h6>';
                $content .= '<p>' . esc_html($analysis['description']) . '</p>';
                if (isset($analysis['use_case'])) {
                    $content .= '<p><em>Best for:</em> ' . esc_html($analysis['use_case']) . '</p>';
                }
                $content .= '</div>';
            }
            $content .= '</div>';
        }

        // Browser support information
        if (!empty($field_data['browser_support'])) {
            $content .= '<div class="help-detail browser-support">';
            $content .= '<h5>' . __('Browser Support', 'redco-optimizer') . '</h5>';
            if (!empty($field_data['browser_support']['native_support'])) {
                $content .= '<p><strong>' . __('Native Support:', 'redco-optimizer') . '</strong></p>';
                $content .= '<ul>';
                foreach ($field_data['browser_support']['native_support'] as $browser => $version) {
                    if ($browser !== 'coverage') {
                        $content .= '<li>' . ucfirst($browser) . ': ' . esc_html($version) . '</li>';
                    }
                }
                $content .= '</ul>';
                if (isset($field_data['browser_support']['native_support']['coverage'])) {
                    $content .= '<p><em>Coverage: ' . esc_html($field_data['browser_support']['native_support']['coverage']) . '</em></p>';
                }
            }
            $content .= '</div>';
        }

        // Pros and cons
        if (!empty($field_data['pros']) || !empty($field_data['cons'])) {
            $content .= '<div class="pros-cons">';

            if (!empty($field_data['pros'])) {
                $content .= '<div class="pros">';
                $content .= '<h5>' . __('Pros', 'redco-optimizer') . '</h5>';
                $content .= '<ul>';
                foreach ($field_data['pros'] as $pro) {
                    $content .= '<li>' . esc_html($pro) . '</li>';
                }
                $content .= '</ul>';
                $content .= '</div>';
            }

            if (!empty($field_data['cons'])) {
                $content .= '<div class="cons">';
                $content .= '<h5>' . __('Cons', 'redco-optimizer') . '</h5>';
                $content .= '<ul>';
                foreach ($field_data['cons'] as $con) {
                    $content .= '<li>' . esc_html($con) . '</li>';
                }
                $content .= '</ul>';
                $content .= '</div>';
            }

            $content .= '</div>';
        }

        // Best practices
        if (!empty($field_data['best_practices'])) {
            $content .= '<div class="help-recommendations">';
            $content .= '<h5>' . __('Best Practices', 'redco-optimizer') . '</h5>';
            $content .= '<ul>';
            foreach ($field_data['best_practices'] as $practice) {
                $content .= '<li>' . esc_html($practice) . '</li>';
            }
            $content .= '</ul>';
            $content .= '</div>';
        }

        // Recommendations
        if (!empty($field_data['recommendations'])) {
            $content .= '<div class="help-recommendations">';
            $content .= '<h5>' . __('Recommendations', 'redco-optimizer') . '</h5>';
            $content .= '<ul>';
            foreach ($field_data['recommendations'] as $recommendation) {
                $content .= '<li>' . esc_html($recommendation) . '</li>';
            }
            $content .= '</ul>';
            $content .= '</div>';
        }

        // Troubleshooting
        if (!empty($field_data['troubleshooting'])) {
            $content .= '<div class="help-warnings">';
            $content .= '<h5>' . __('Troubleshooting', 'redco-optimizer') . '</h5>';
            $content .= '<ul>';
            foreach ($field_data['troubleshooting'] as $tip) {
                $content .= '<li>' . esc_html($tip) . '</li>';
            }
            $content .= '</ul>';
            $content .= '</div>';
        }

        return $content;
    }

    /**
     * Render the help panel HTML
     */
    public function render_help_panel() {
        ?>
        <div id="redco-help-panel" class="redco-help-panel">
            <div class="help-panel-header">
                <h3><?php _e('Help & Documentation', 'redco-optimizer'); ?></h3>
                <button type="button" class="help-panel-close">
                    <span class="dashicons dashicons-no-alt"></span>
                </button>
            </div>

            <div class="help-panel-search">
                <input type="text" id="help-search" placeholder="<?php _e('Search help topics...', 'redco-optimizer'); ?>">
                <span class="dashicons dashicons-search"></span>
            </div>

            <div class="help-panel-content">
                <div class="help-loading">
                    <span class="dashicons dashicons-update-alt"></span>
                    <?php _e('Loading help content...', 'redco-optimizer'); ?>
                </div>
            </div>
        </div>

        <div id="redco-help-overlay" class="redco-help-overlay"></div>
        <?php
    }
}

// Initialize the help system
new Redco_Help_System();
