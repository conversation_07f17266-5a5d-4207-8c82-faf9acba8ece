/**
 * CRITICAL FIX: UI Utilities Module
 * Extracted from large admin-scripts.js for better maintainability
 * Handles all UI interactions, animations, and visual feedback
 */

(function($) {
    'use strict';

    /**
     * UI Utilities Module
     */
    window.RedcoUIUtils = {
        
        /**
         * Configuration
         */
        config: {
            animationDuration: 300,
            toastDuration: 4000,
            debounceDelay: 250
        },
        
        /**
         * Initialize UI utilities
         */
        init: function() {
            this.initSidebarHeightMatching();
            this.initTooltips();
            this.initModals();
            this.initKeyboardShortcuts();
        },
        
        /**
         * Initialize sidebar height matching
         */
        initSidebarHeightMatching: function() {
            const self = this;
            
            function matchSidebarHeight() {
                const $sidebar = $('.redco-sidebar');
                const $content = $('.redco-content');
                
                if ($sidebar.length && $content.length) {
                    // Reset heights first
                    $sidebar.css('height', '');
                    
                    // Get content height
                    const contentHeight = $content.outerHeight();
                    const sidebarMinHeight = parseInt($sidebar.css('min-height')) || 0;
                    
                    // Set sidebar height to match content or min-height, whichever is larger
                    const targetHeight = Math.max(contentHeight, sidebarMinHeight);
                    $sidebar.css('height', targetHeight + 'px');
                }
            }
            
            // Match heights on load
            setTimeout(matchSidebarHeight, 100);
            
            // Match heights on window resize
            $(window).on('resize', function() {
                clearTimeout(window.sidebarResizeTimer);
                window.sidebarResizeTimer = setTimeout(matchSidebarHeight, self.config.debounceDelay);
            });
            
            // Match heights when content changes
            if (window.MutationObserver) {
                const observer = new MutationObserver(function() {
                    clearTimeout(window.sidebarMutationTimer);
                    window.sidebarMutationTimer = setTimeout(matchSidebarHeight, 100);
                });
                
                const contentElement = document.querySelector('.redco-content');
                if (contentElement) {
                    observer.observe(contentElement, {
                        childList: true,
                        subtree: true,
                        attributes: true
                    });
                }
            }
        },
        
        /**
         * Initialize tooltips
         */
        initTooltips: function() {
            // Initialize WordPress-style tooltips
            $(document).on('mouseenter', '[data-tooltip]', function() {
                const $element = $(this);
                const tooltipText = $element.data('tooltip');
                
                if (tooltipText && !$element.data('tooltip-active')) {
                    const $tooltip = $('<div class="redco-tooltip">' + tooltipText + '</div>');
                    $('body').append($tooltip);
                    
                    const elementOffset = $element.offset();
                    const elementHeight = $element.outerHeight();
                    
                    $tooltip.css({
                        top: elementOffset.top + elementHeight + 5,
                        left: elementOffset.left,
                        opacity: 0
                    }).animate({ opacity: 1 }, 200);
                    
                    $element.data('tooltip-active', $tooltip);
                }
            });
            
            $(document).on('mouseleave', '[data-tooltip]', function() {
                const $element = $(this);
                const $tooltip = $element.data('tooltip-active');
                
                if ($tooltip) {
                    $tooltip.animate({ opacity: 0 }, 200, function() {
                        $tooltip.remove();
                    });
                    $element.removeData('tooltip-active');
                }
            });
        },
        
        /**
         * Initialize modals
         */
        initModals: function() {
            // Modal open handler
            $(document).on('click', '[data-modal]', function(e) {
                e.preventDefault();
                const modalId = $(this).data('modal');
                this.openModal(modalId);
            });
            
            // Modal close handlers
            $(document).on('click', '.modal-close, .modal-overlay', function(e) {
                if (e.target === this) {
                    this.closeModal($(this).closest('.modal'));
                }
            });
            
            // ESC key to close modal
            $(document).on('keydown', function(e) {
                if (e.keyCode === 27) { // ESC key
                    const $openModal = $('.modal:visible');
                    if ($openModal.length) {
                        this.closeModal($openModal);
                    }
                }
            }.bind(this));
        },
        
        /**
         * Open modal
         */
        openModal: function(modalId) {
            const $modal = $('#' + modalId);
            if ($modal.length) {
                $modal.fadeIn(this.config.animationDuration);
                $('body').addClass('modal-open');
                
                // Focus first input
                setTimeout(function() {
                    $modal.find('input, textarea, select').first().focus();
                }, this.config.animationDuration);
            }
        },
        
        /**
         * Close modal
         */
        closeModal: function($modal) {
            if ($modal && $modal.length) {
                $modal.fadeOut(this.config.animationDuration);
                $('body').removeClass('modal-open');
            }
        },
        
        /**
         * Initialize keyboard shortcuts
         */
        initKeyboardShortcuts: function() {
            $(document).on('keydown', function(e) {
                // Ctrl/Cmd + S to save
                if ((e.ctrlKey || e.metaKey) && e.keyCode === 83) {
                    e.preventDefault();
                    this.triggerSave();
                }
                
                // Ctrl/Cmd + R to refresh metrics
                if ((e.ctrlKey || e.metaKey) && e.keyCode === 82) {
                    e.preventDefault();
                    this.triggerRefresh();
                }
            }.bind(this));
        },
        
        /**
         * Trigger save action (Updated for Global Auto-Save System)
         */
        triggerSave: function() {
            const $activeForm = $('.redco-module-form:visible').first();
            if ($activeForm.length && typeof RedcoGlobalAutoSave !== 'undefined') {
                // Trigger save for all pending changes
                RedcoGlobalAutoSave.saveAllPendingChanges();
            }
        },
        
        /**
         * Trigger refresh action
         */
        triggerRefresh: function() {
            if (typeof RedcoPerformanceMonitor !== 'undefined') {
                RedcoPerformanceMonitor.clearCache();
                RedcoPerformanceMonitor.updateMetrics();
            }
        },
        
        /**
         * Show loading state
         */
        showLoading: function($element, text) {
            text = text || 'Loading...';
            
            if (!$element.data('original-content')) {
                $element.data('original-content', $element.html());
            }
            
            $element.html('<span class="loading-spinner"></span> ' + text)
                   .prop('disabled', true)
                   .addClass('loading');
        },
        
        /**
         * Hide loading state
         */
        hideLoading: function($element) {
            const originalContent = $element.data('original-content');
            if (originalContent) {
                $element.html(originalContent)
                       .prop('disabled', false)
                       .removeClass('loading')
                       .removeData('original-content');
            }
        },
        
        /**
         * Animate number counter
         */
        animateCounter: function($element, targetValue, duration) {
            duration = duration || 1000;
            const startValue = parseInt($element.text()) || 0;
            const increment = (targetValue - startValue) / (duration / 16);
            let currentValue = startValue;
            
            const timer = setInterval(function() {
                currentValue += increment;
                
                if ((increment > 0 && currentValue >= targetValue) || 
                    (increment < 0 && currentValue <= targetValue)) {
                    currentValue = targetValue;
                    clearInterval(timer);
                }
                
                $element.text(Math.round(currentValue));
            }, 16);
        },
        
        /**
         * Smooth scroll to element
         */
        scrollTo: function(target, offset) {
            offset = offset || 0;
            const $target = $(target);
            
            if ($target.length) {
                $('html, body').animate({
                    scrollTop: $target.offset().top - offset
                }, this.config.animationDuration);
            }
        },
        
        /**
         * Highlight element temporarily
         */
        highlight: function($element, duration) {
            duration = duration || 2000;
            
            $element.addClass('highlighted');
            setTimeout(function() {
                $element.removeClass('highlighted');
            }, duration);
        },
        
        /**
         * Format file size
         */
        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 B';
            
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },
        
        /**
         * Format duration
         */
        formatDuration: function(seconds) {
            if (seconds < 60) {
                return Math.round(seconds) + 's';
            } else if (seconds < 3600) {
                return Math.round(seconds / 60) + 'm';
            } else {
                return Math.round(seconds / 3600) + 'h';
            }
        },
        
        /**
         * Debounce function
         */
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        /**
         * Throttle function
         */
        throttle: function(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        }
    };

})(jQuery);
