<?php
/**
 * CDN Provider Manager - Phase 4 Implementation
 * 
 * Manages multiple CDN providers with enterprise-grade features.
 * Provides unified interface for all supported CDN services.
 * 
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_CDN_Provider_Manager {

    /**
     * Supported CDN providers
     */
    private static $providers = array(
        'cloudflare' => array(
            'name' => 'Cloudflare',
            'class' => 'Redco_CDN_Cloudflare',
            'logo' => 'cloudflare-logo.svg',
            'features' => array(
                'Global Edge Network (200+ locations)',
                'Page Rules & Workers',
                'DDoS Protection & WAF',
                'Real-time Analytics',
                'Automatic SSL/TLS',
                'HTTP/2 & HTTP/3 Support'
            ),
            'edge_locations' => '200+',
            'performance_tier' => 'enterprise',
            'setup_complexity' => 'medium',
            'cost_tier' => 'freemium'
        ),
        'bunnycdn' => array(
            'name' => 'BunnyCDN',
            'class' => 'Redco_CDN_BunnyCDN',
            'logo' => 'bunnycdn-logo.svg',
            'features' => array(
                'Ultra-fast Global Network (114+ locations)',
                'Pull & Push Zones',
                'Video Streaming CDN',
                'Real-time Statistics',
                'Image Optimization',
                'Affordable Pricing'
            ),
            'edge_locations' => '114+',
            'performance_tier' => 'high',
            'setup_complexity' => 'easy',
            'cost_tier' => 'budget'
        ),
        'keycdn' => array(
            'name' => 'KeyCDN',
            'class' => 'Redco_CDN_KeyCDN',
            'logo' => 'keycdn-logo.svg',
            'features' => array(
                'High-performance Network (34+ locations)',
                'Real-time Analytics',
                'Image Processing',
                'HTTP/2 Support',
                'Let\'s Encrypt SSL',
                'RESTful API'
            ),
            'edge_locations' => '34+',
            'performance_tier' => 'high',
            'setup_complexity' => 'easy',
            'cost_tier' => 'budget'
        ),
        'cloudfront' => array(
            'name' => 'Amazon CloudFront',
            'class' => 'Redco_CDN_CloudFront',
            'logo' => 'cloudfront-logo.svg',
            'features' => array(
                'Massive Global Network (400+ locations)',
                'Lambda@Edge Computing',
                'Real-time Logs',
                'AWS Integration',
                'Advanced Security',
                'Enterprise Features'
            ),
            'edge_locations' => '400+',
            'performance_tier' => 'enterprise',
            'setup_complexity' => 'complex',
            'cost_tier' => 'enterprise'
        ),
        'sucuri' => array(
            'name' => 'Sucuri CDN',
            'class' => 'Redco_CDN_Sucuri',
            'logo' => 'sucuri-logo.svg',
            'features' => array(
                'Security-focused CDN',
                'DDoS Protection',
                'Malware Scanning',
                'WAF & Firewall',
                'Performance Optimization',
                'Security Monitoring'
            ),
            'edge_locations' => '11+',
            'performance_tier' => 'security',
            'setup_complexity' => 'medium',
            'cost_tier' => 'premium'
        ),
        'fastly' => array(
            'name' => 'Fastly',
            'class' => 'Redco_CDN_Fastly',
            'logo' => 'fastly-logo.svg',
            'features' => array(
                'Edge Computing Platform',
                'Instant Cache Purging',
                'Real-time Analytics',
                'VCL Configuration',
                'Image Optimization',
                'Developer-friendly API'
            ),
            'edge_locations' => '65+',
            'performance_tier' => 'enterprise',
            'setup_complexity' => 'complex',
            'cost_tier' => 'enterprise'
        ),
        'custom' => array(
            'name' => 'Custom CDN',
            'class' => 'Redco_CDN_Custom',
            'logo' => 'custom-cdn-logo.svg',
            'features' => array(
                'Any CDN Provider',
                'Manual Configuration',
                'URL Rewriting',
                'Basic Analytics',
                'Custom Headers',
                'Flexible Setup'
            ),
            'edge_locations' => 'Variable',
            'performance_tier' => 'variable',
            'setup_complexity' => 'manual',
            'cost_tier' => 'variable'
        )
    );

    /**
     * Get all available providers
     */
    public static function get_providers() {
        return self::$providers;
    }

    /**
     * Get specific provider data
     */
    public static function get_provider($provider_name) {
        return isset(self::$providers[$provider_name]) ? self::$providers[$provider_name] : null;
    }

    /**
     * Get provider instance
     */
    public static function get_provider_instance($provider_name) {
        $provider_data = self::get_provider($provider_name);
        
        if (!$provider_data) {
            return null;
        }

        $class_name = $provider_data['class'];
        
        if (!class_exists($class_name)) {
            // Load provider class file
            $file_path = REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/cdn-providers/class-' . str_replace('_', '-', strtolower($class_name)) . '.php';
            if (file_exists($file_path)) {
                require_once $file_path;
            }
        }

        if (class_exists($class_name)) {
            return new $class_name();
        }

        return null;
    }

    /**
     * Get provider capabilities
     */
    public static function get_provider_capabilities($provider_name) {
        $instance = self::get_provider_instance($provider_name);
        
        if ($instance && method_exists($instance, 'get_capabilities')) {
            return $instance->get_capabilities();
        }

        return array();
    }

    /**
     * Test provider performance
     */
    public static function test_provider_performance($provider_name, $test_urls = array()) {
        $instance = self::get_provider_instance($provider_name);
        
        if (!$instance || !method_exists($instance, 'test_performance')) {
            return array('error' => 'Provider not available for testing');
        }

        return $instance->test_performance($test_urls);
    }

    /**
     * Get provider status
     */
    public static function get_provider_status($provider_name) {
        $instance = self::get_provider_instance($provider_name);
        
        if (!$instance) {
            return 'unavailable';
        }

        if (method_exists($instance, 'get_status')) {
            return $instance->get_status();
        }

        // Check if provider is configured
        $config = redco_get_module_option('cdn-integration', $provider_name . '_config', array());
        
        if (empty($config) || !$instance->is_configured($config)) {
            return 'not_configured';
        }

        return 'configured';
    }

    /**
     * Get recommended provider based on site analysis
     */
    public static function get_recommended_provider() {
        $site_analysis = self::analyze_site_requirements();
        
        // Simple recommendation logic based on site characteristics
        if ($site_analysis['traffic_level'] === 'enterprise') {
            return 'cloudfront'; // Best for enterprise with AWS integration
        }
        
        if ($site_analysis['security_focus']) {
            return 'sucuri'; // Best for security-focused sites
        }
        
        if ($site_analysis['budget_conscious']) {
            return 'bunnycdn'; // Best value for money
        }
        
        if ($site_analysis['developer_friendly']) {
            return 'fastly'; // Best for developers
        }
        
        // Default recommendation for most sites
        return 'cloudflare';
    }

    /**
     * Analyze site requirements for provider recommendation
     */
    private static function analyze_site_requirements() {
        // Analyze current site to recommend best CDN provider
        $analysis = array(
            'traffic_level' => 'medium',
            'security_focus' => false,
            'budget_conscious' => true,
            'developer_friendly' => false,
            'geographic_distribution' => 'global'
        );

        // Check if security plugins are active
        if (is_plugin_active('wordfence/wordfence.php') || 
            is_plugin_active('sucuri-scanner/sucuri.php')) {
            $analysis['security_focus'] = true;
        }

        // Check for developer-oriented plugins
        if (is_plugin_active('query-monitor/query-monitor.php') || 
            defined('WP_DEBUG') && WP_DEBUG) {
            $analysis['developer_friendly'] = true;
        }

        // Estimate traffic level based on post count and comments
        $post_count = wp_count_posts()->publish;
        $comment_count = wp_count_comments()->approved;
        
        if ($post_count > 1000 || $comment_count > 5000) {
            $analysis['traffic_level'] = 'high';
        }
        
        if ($post_count > 10000 || $comment_count > 50000) {
            $analysis['traffic_level'] = 'enterprise';
        }

        return $analysis;
    }

    /**
     * Auto-detect existing CDN
     */
    public static function detect_existing_cdn() {
        $site_url = get_site_url();
        $parsed_url = parse_url($site_url);
        $domain = $parsed_url['host'];

        // Test a sample asset URL
        $test_url = $site_url . '/wp-includes/css/admin-bar.min.css';
        
        $response = wp_remote_head($test_url, array('timeout' => 10));
        
        if (is_wp_error($response)) {
            return array('detected' => false, 'provider' => null);
        }

        $headers = wp_remote_retrieve_headers($response);
        
        // Check for CDN-specific headers
        $cdn_indicators = array(
            'cloudflare' => array('cf-ray', 'cf-cache-status', 'server' => 'cloudflare'),
            'fastly' => array('fastly-debug-digest', 'x-served-by'),
            'cloudfront' => array('x-amz-cf-id', 'x-amz-cf-pop'),
            'keycdn' => array('x-edge-location', 'x-cache' => 'keycdn'),
            'bunnycdn' => array('x-pullzone', 'x-cache' => 'bunnycdn'),
            'sucuri' => array('x-sucuri-id', 'x-sucuri-cache')
        );

        foreach ($cdn_indicators as $provider => $indicators) {
            foreach ($indicators as $header => $value) {
                if (is_numeric($header)) {
                    // Simple header existence check
                    if (isset($headers[$value])) {
                        return array('detected' => true, 'provider' => $provider, 'confidence' => 'high');
                    }
                } else {
                    // Header value check
                    if (isset($headers[$header]) && strpos(strtolower($headers[$header]), $value) !== false) {
                        return array('detected' => true, 'provider' => $provider, 'confidence' => 'high');
                    }
                }
            }
        }

        return array('detected' => false, 'provider' => null);
    }
}
