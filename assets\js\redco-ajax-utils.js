/**
 * CRITICAL FIX: Centralized AJAX Utility Library
 * Eliminates code duplication across 12 instances of similar AJAX patterns
 * Provides consistent error handling and request management
 */

(function($) {
    'use strict';

    /**
     * Centralized AJAX Utility System
     */
    window.RedcoAjax = {
        
        /**
         * Active requests tracking
         */
        activeRequests: new Set(),
        
        /**
         * Request cache for performance
         */
        cache: new Map(),
        
        /**
         * Default AJAX settings
         */
        defaults: {
            timeout: 30000,
            retries: 2,
            cache: false
        },
        
        /**
         * Make standardized AJAX request
         * 
         * @param {Object} options Request options
         * @param {string} options.action WordPress AJAX action
         * @param {Object} options.data Request data
         * @param {Function} options.success Success callback
         * @param {Function} options.error Error callback
         * @param {Object} options.settings Additional jQuery AJAX settings
         */
        request: function(options) {
            const settings = $.extend({}, this.defaults, options.settings || {});
            const requestId = this.generateRequestId(options.action, options.data);
            
            // Check cache if enabled
            if (settings.cache && this.cache.has(requestId)) {
                const cachedData = this.cache.get(requestId);
                if (cachedData.expires > Date.now()) {
                    if (options.success) {
                        options.success(cachedData.data);
                    }
                    return;
                }
                this.cache.delete(requestId);
            }
            
            // Prepare request data
            const requestData = $.extend({
                action: options.action,
                nonce: this.getNonce()
            }, options.data || {});
            
            // Track active request
            this.activeRequests.add(requestId);
            
            // Make AJAX request with retry logic
            this.makeRequestWithRetry(requestData, settings, options, requestId, 0);
        },
        
        /**
         * Make AJAX request with automatic retry
         */
        makeRequestWithRetry: function(data, settings, options, requestId, attempt) {
            const self = this;
            
            $.ajax({
                url: this.getAjaxUrl(),
                type: 'POST',
                data: data,
                timeout: settings.timeout,
                success: function(response) {
                    self.activeRequests.delete(requestId);
                    
                    if (response.success) {
                        // Cache successful response if enabled
                        if (settings.cache) {
                            self.cache.set(requestId, {
                                data: response,
                                expires: Date.now() + (settings.cacheDuration || 300000) // 5 minutes default
                            });
                        }
                        
                        if (options.success) {
                            options.success(response);
                        }
                    } else {
                        self.handleError(response.data || 'Unknown error', options.error);
                    }
                },
                error: function(xhr, status, error) {
                    // Retry logic
                    if (attempt < settings.retries && status !== 'abort') {
                        setTimeout(function() {
                            self.makeRequestWithRetry(data, settings, options, requestId, attempt + 1);
                        }, 1000 * (attempt + 1)); // Exponential backoff
                        return;
                    }
                    
                    self.activeRequests.delete(requestId);
                    self.handleError(error, options.error);
                }
            });
        },
        
        /**
         * Standardized progress request with simulation
         * 
         * @param {Object} options Request options
         * @param {Array} options.progressSteps Array of progress steps
         * @param {Function} options.onProgress Progress callback
         */
        progressRequest: function(options) {
            const progressSteps = options.progressSteps || [
                { progress: 20, text: 'Initializing...' },
                { progress: 50, text: 'Processing...' },
                { progress: 80, text: 'Finalizing...' }
            ];
            
            // Start progress simulation
            const progressInterval = this.simulateProgress(progressSteps, options.onProgress);
            
            // Make actual request
            this.request({
                action: options.action,
                data: options.data,
                success: function(response) {
                    clearInterval(progressInterval);
                    if (options.onProgress) {
                        options.onProgress(100, 'Complete!');
                    }
                    if (options.success) {
                        options.success(response);
                    }
                },
                error: function(error) {
                    clearInterval(progressInterval);
                    if (options.error) {
                        options.error(error);
                    }
                },
                settings: options.settings
            });
            
            return progressInterval;
        },
        
        /**
         * Simulate progress updates
         * 
         * @param {Array} steps Progress steps
         * @param {Function} callback Progress callback
         * @return {number} Interval ID
         */
        simulateProgress: function(steps, callback) {
            let currentStep = 0;
            
            return setInterval(function() {
                if (currentStep < steps.length && callback) {
                    const step = steps[currentStep];
                    callback(step.progress, step.text);
                    currentStep++;
                }
            }, 800);
        },
        
        /**
         * Batch request handler
         * 
         * @param {Array} requests Array of request options
         * @param {Function} onComplete Completion callback
         * @param {Function} onProgress Progress callback
         */
        batchRequest: function(requests, onComplete, onProgress) {
            const results = [];
            let completed = 0;
            
            requests.forEach((requestOptions, index) => {
                this.request({
                    action: requestOptions.action,
                    data: requestOptions.data,
                    success: function(response) {
                        results[index] = { success: true, data: response };
                        completed++;
                        
                        if (onProgress) {
                            onProgress(completed, requests.length);
                        }
                        
                        if (completed === requests.length && onComplete) {
                            onComplete(results);
                        }
                    },
                    error: function(error) {
                        results[index] = { success: false, error: error };
                        completed++;
                        
                        if (onProgress) {
                            onProgress(completed, requests.length);
                        }
                        
                        if (completed === requests.length && onComplete) {
                            onComplete(results);
                        }
                    },
                    settings: requestOptions.settings
                });
            });
        },
        
        /**
         * Cancel all active requests
         */
        cancelAllRequests: function() {
            this.activeRequests.clear();
        },
        
        /**
         * Clear request cache
         */
        clearCache: function() {
            this.cache.clear();
        },
        
        /**
         * Generate unique request ID
         */
        generateRequestId: function(action, data) {
            return action + '_' + JSON.stringify(data).slice(0, 50);
        },
        
        /**
         * Get WordPress AJAX URL
         */
        getAjaxUrl: function() {
            return (typeof ajaxurl !== 'undefined') ? ajaxurl : 
                   (typeof redcoAjax !== 'undefined' && redcoAjax.ajaxurl) ? redcoAjax.ajaxurl :
                   '/wp-admin/admin-ajax.php';
        },
        
        /**
         * Get security nonce
         */
        getNonce: function() {
            return (typeof redcoAjax !== 'undefined' && redcoAjax.nonce) ? redcoAjax.nonce : '';
        },
        
        /**
         * Standardized error handling
         */
        handleError: function(error, callback) {
            const errorMessage = typeof error === 'string' ? error : 
                                error.message || 'An unexpected error occurred';
            
            // CRITICAL FIX: Use debug utility instead of console.log
            if (typeof redcoDebug !== 'undefined' && redcoDebug.enabled) {
                redcoDebug.error('AJAX Error:', errorMessage);
            }
            
            if (callback) {
                callback(errorMessage);
            } else if (typeof showToast === 'function') {
                showToast(errorMessage, 'error');
            }
        }
    };

})(jQuery);
