<?php
/**
 * Debug Rollback Process
 * This script helps debug why rollback is not removing items from Recent Fixes
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// Security check
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

echo "<h1>🐛 Debug Rollback Process</h1>\n";

// Check current state
$fix_history = get_option('redco_diagnostic_fix_history', array());
$rollback_history = get_option('redco_rollback_history', array());

echo "<h2>📊 Current State</h2>\n";
echo "<div style='background: #f0f0f1; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
echo "<ul>\n";
echo "<li><strong>Fix History Sessions:</strong> " . count($fix_history) . "</li>\n";
echo "<li><strong>Rollback History Entries:</strong> " . count($rollback_history) . "</li>\n";
echo "</ul>\n";
echo "</div>\n";

if (!empty($rollback_history)) {
    echo "<h2>📋 Recent Rollback History</h2>\n";
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
    
    foreach (array_reverse(array_slice($rollback_history, -5)) as $index => $rollback) {
        $timestamp = $rollback['timestamp'] ?? time();
        $backup_id = $rollback['backup_id'] ?? 'unknown';
        $message = $rollback['message'] ?? 'No message';
        $sessions_removed = $rollback['sessions_removed'] ?? 0;
        $rolled_back_issues = $rollback['rolled_back_issues'] ?? array();
        
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px 0; border-radius: 3px;'>\n";
        echo "<h4>Rollback " . ($index + 1) . " (Most Recent)</h4>\n";
        echo "<ul>\n";
        echo "<li><strong>Timestamp:</strong> " . date('Y-m-d H:i:s', $timestamp) . " (" . human_time_diff($timestamp) . " ago)</li>\n";
        echo "<li><strong>Backup ID:</strong> {$backup_id}</li>\n";
        echo "<li><strong>Message:</strong> {$message}</li>\n";
        echo "<li><strong>Sessions Removed:</strong> {$sessions_removed}</li>\n";
        echo "<li><strong>Issues Rolled Back:</strong> " . count($rolled_back_issues) . "</li>\n";
        if (!empty($rolled_back_issues)) {
            echo "<li><strong>Issue IDs:</strong> " . implode(', ', $rolled_back_issues) . "</li>\n";
        }
        echo "</ul>\n";
        echo "</div>\n";
    }
    echo "</div>\n";
} else {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>\n";
    echo "<p>No rollback history found. This suggests that rollbacks haven't been executed or aren't being recorded properly.</p>\n";
    echo "</div>\n";
}

echo "<h2>🔍 Fix History Analysis</h2>\n";

if (!empty($fix_history)) {
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
    
    foreach ($fix_history as $index => $session) {
        $timestamp = $session['timestamp'] ?? time();
        $rollback_id = $session['rollback_id'] ?? 'none';
        $backup_id = $session['backup_id'] ?? 'none';
        $session_id = $session['session_id'] ?? 'none';
        $message = $session['message'] ?? 'none';
        $details_count = isset($session['details']) ? count($session['details']) : 0;
        
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px 0; border-radius: 3px;'>\n";
        echo "<h4>Fix Session " . ($index + 1) . "</h4>\n";
        echo "<ul>\n";
        echo "<li><strong>Timestamp:</strong> " . date('Y-m-d H:i:s', $timestamp) . " (" . human_time_diff($timestamp) . " ago)</li>\n";
        echo "<li><strong>Rollback ID:</strong> {$rollback_id}</li>\n";
        echo "<li><strong>Backup ID:</strong> {$backup_id}</li>\n";
        echo "<li><strong>Session ID:</strong> {$session_id}</li>\n";
        echo "<li><strong>Message:</strong> {$message}</li>\n";
        echo "<li><strong>Details Count:</strong> {$details_count}</li>\n";
        echo "</ul>\n";
        
        // Check if this session was rolled back
        $was_rolled_back = false;
        $rollback_timestamp = null;
        
        foreach ($rollback_history as $rollback) {
            $rollback_backup_id = $rollback['backup_id'] ?? '';
            if ($rollback_backup_id === $rollback_id || $rollback_backup_id === $backup_id) {
                $was_rolled_back = true;
                $rollback_timestamp = $rollback['timestamp'] ?? null;
                break;
            }
        }
        
        if ($was_rolled_back) {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 5px 0; border-radius: 3px;'>\n";
            echo "<p><strong>⚠️ ISSUE DETECTED:</strong> This session appears to have been rolled back";
            if ($rollback_timestamp) {
                echo " on " . date('Y-m-d H:i:s', $rollback_timestamp);
            }
            echo ", but it's still present in the fix history!</p>\n";
            echo "<p>This indicates that the rollback process is not properly removing sessions from the fix history.</p>\n";
            echo "</div>\n";
        }
        
        echo "</div>\n";
    }
    echo "</div>\n";
} else {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
    echo "<p>No fix history found. This is normal if no fixes have been applied yet.</p>\n";
    echo "</div>\n";
}

echo "<h2>🧪 Test Rollback Functionality</h2>\n";

if (!empty($fix_history)) {
    $latest_session = end($fix_history);
    $test_rollback_id = $latest_session['rollback_id'] ?? $latest_session['backup_id'] ?? '';
    
    if (!empty($test_rollback_id)) {
        echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>🎯 Test Rollback for Latest Session</h3>\n";
        echo "<p><strong>Rollback ID to test:</strong> {$test_rollback_id}</p>\n";
        echo "<p><a href='?test_rollback_ajax=" . urlencode($test_rollback_id) . "' style='background: #dc3232; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🧪 Test AJAX Rollback</a></p>\n";
        echo "</div>\n";
    }
}

// Handle AJAX rollback test
$test_rollback_id = $_GET['test_rollback_ajax'] ?? '';
if (!empty($test_rollback_id)) {
    echo "<h2>🧪 Testing AJAX Rollback for ID: {$test_rollback_id}</h2>\n";
    
    try {
        // Load the diagnostic class
        require_once('modules/diagnostic-autofix/class-diagnostic-autofix.php');
        $diagnostic = new Redco_Diagnostic_AutoFix();
        
        // Simulate the AJAX request
        $_POST['nonce'] = wp_create_nonce('redco_diagnostic_nonce');
        $_POST['backup_id'] = $test_rollback_id;
        
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>🔄 Simulating AJAX Rollback Request</h3>\n";
        echo "<p><strong>Backup ID:</strong> {$test_rollback_id}</p>\n";
        echo "<p><strong>Nonce:</strong> " . $_POST['nonce'] . "</p>\n";
        echo "</div>\n";
        
        // Capture the output
        ob_start();
        
        // Call the rollback method using reflection
        $reflection = new ReflectionClass($diagnostic);
        $rollback_method = $reflection->getMethod('ajax_rollback_fixes');
        $rollback_method->setAccessible(true);
        
        try {
            $rollback_method->invoke($diagnostic);
            $ajax_output = ob_get_clean();
            
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
            echo "<h3>✅ AJAX Rollback Response</h3>\n";
            echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto;'>" . htmlspecialchars($ajax_output) . "</pre>\n";
            echo "</div>\n";
            
            // Parse the JSON response
            $response = json_decode($ajax_output, true);
            if ($response) {
                if ($response['success']) {
                    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
                    echo "<h3>✅ Rollback Successful</h3>\n";
                    echo "<ul>\n";
                    if (isset($response['data']['files_restored'])) {
                        echo "<li><strong>Files Restored:</strong> " . $response['data']['files_restored'] . "</li>\n";
                    }
                    if (isset($response['data']['options_restored'])) {
                        echo "<li><strong>Options Restored:</strong> " . $response['data']['options_restored'] . "</li>\n";
                    }
                    if (isset($response['data']['rollback_removed_from_history'])) {
                        echo "<li><strong>Removed from History:</strong> " . ($response['data']['rollback_removed_from_history'] ? 'Yes' : 'No') . "</li>\n";
                    }
                    echo "</ul>\n";
                    echo "</div>\n";
                    
                    // Check if the session was actually removed
                    $updated_fix_history = get_option('redco_diagnostic_fix_history', array());
                    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
                    echo "<h3>📊 Post-Rollback State</h3>\n";
                    echo "<ul>\n";
                    echo "<li><strong>Fix History Sessions Before:</strong> " . count($fix_history) . "</li>\n";
                    echo "<li><strong>Fix History Sessions After:</strong> " . count($updated_fix_history) . "</li>\n";
                    echo "<li><strong>Sessions Removed:</strong> " . (count($fix_history) - count($updated_fix_history)) . "</li>\n";
                    echo "</ul>\n";
                    
                    if (count($fix_history) === count($updated_fix_history)) {
                        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; border-radius: 3px;'>\n";
                        echo "<p><strong>❌ ISSUE CONFIRMED:</strong> The rollback was reported as successful, but no sessions were actually removed from the fix history!</p>\n";
                        echo "</div>\n";
                    } else {
                        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; border-radius: 3px;'>\n";
                        echo "<p><strong>✅ SUCCESS:</strong> The rollback properly removed the session from the fix history.</p>\n";
                        echo "</div>\n";
                    }
                    echo "</div>\n";
                    
                } else {
                    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
                    echo "<h3>❌ Rollback Failed</h3>\n";
                    echo "<p><strong>Error:</strong> " . ($response['data'] ?? 'Unknown error') . "</p>\n";
                    echo "</div>\n";
                }
            } else {
                echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
                echo "<h3>❌ Invalid JSON Response</h3>\n";
                echo "<p>The AJAX response was not valid JSON.</p>\n";
                echo "</div>\n";
            }
            
        } catch (Exception $e) {
            ob_end_clean();
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
            echo "<h3>❌ Exception During Rollback</h3>\n";
            echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>\n";
            echo "<p><strong>File:</strong> " . $e->getFile() . "</p>\n";
            echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>\n";
            echo "</div>\n";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>\n";
        echo "<h3>❌ Failed to Load Diagnostic Class</h3>\n";
        echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>\n";
        echo "</div>\n";
    }
    
    echo "<h3>🎯 Next Steps</h3>\n";
    echo "<ol>\n";
    echo "<li>Check the error logs for detailed rollback process information</li>\n";
    echo "<li>Go to the <a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "'>Diagnostic & Auto-Fix module</a> to see if the Recent Fixes list was updated</li>\n";
    echo "<li>If the issue persists, the problem is in the rollback logic or database update process</li>\n";
    echo "</ol>\n";
    
    echo "<p><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🚀 Go to Diagnostic Module</a></p>\n";
}

echo "<h2>🔧 Diagnostic Tools</h2>\n";
echo "<ul>\n";
echo "<li><a href='check-fix-history.php'>📋 Check Fix History State</a></li>\n";
echo "<li><a href='test-rollback-fixes.php'>🧪 Test Rollback Database Operations</a></li>\n";
echo "<li><a href='immediate-diagnostic-reset.php'>🗑️ Reset All Diagnostic Data</a></li>\n";
echo "</ul>\n";

echo "<p><a href='debug-rollback-process.php' style='background: #666; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔄 Refresh</a></p>\n";
?>
