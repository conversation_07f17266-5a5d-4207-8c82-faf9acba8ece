<?php
/**
 * Rollback State Management Validation Test
 * This script creates test data and validates the rollback functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// Security check
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

echo "<h1>🧪 Rollback State Management Validation</h1>\n";
echo "<p>This script creates test data and validates the enhanced rollback functionality.</p>\n";

// Check if we should create test data
$create_test_data = isset($_GET['create_test_data']) && $_GET['create_test_data'] === 'yes';

if (!$create_test_data) {
    echo "<h2>📋 Test Plan</h2>\n";
    echo "<ol>\n";
    echo "<li><strong>Create Test Data:</strong> Generate sample diagnostic results and fix history</li>\n";
    echo "<li><strong>Validate UI State:</strong> Check that data displays correctly</li>\n";
    echo "<li><strong>Test Rollback:</strong> Perform rollback and validate state management</li>\n";
    echo "<li><strong>Verify Statistics:</strong> Ensure statistics update correctly</li>\n";
    echo "</ol>\n";
    
    echo "<p><a href='?create_test_data=yes' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🚀 Create Test Data</a></p>\n";
    exit;
}

// Create test data
echo "<h2>🔧 Creating Test Data...</h2>\n";

try {
    // 1. Create sample diagnostic results
    $test_issues = array(
        array(
            'id' => 'test_issue_1',
            'title' => 'Test Security Headers Missing',
            'description' => 'Security headers are not properly configured.',
            'severity' => 'high',
            'category' => 'security',
            'auto_fixable' => true,
            'type' => 'issue'
        ),
        array(
            'id' => 'test_issue_2',
            'title' => 'Test Compression Not Enabled',
            'description' => 'GZIP compression is not enabled on the server.',
            'severity' => 'medium',
            'category' => 'performance',
            'auto_fixable' => true,
            'type' => 'issue'
        ),
        array(
            'id' => 'test_issue_3',
            'title' => 'Test Large Database Autoload',
            'description' => 'Database autoload size is larger than recommended.',
            'severity' => 'critical',
            'category' => 'database',
            'auto_fixable' => false,
            'type' => 'issue'
        )
    );

    $diagnostic_results = array(
        'issues' => $test_issues,
        'health_score' => 75,
        'performance_score' => 68,
        'scan_duration' => 2.5,
        'timestamp' => time(),
        'total_issues' => count($test_issues),
        'critical_issues' => 1,
        'auto_fixable_issues' => 2
    );

    update_option('redco_diagnostic_results', $diagnostic_results);
    echo "✅ Created sample diagnostic results with " . count($test_issues) . " issues<br>\n";

    // 2. Create sample fix history
    $fix_sessions = array();
    
    // Session 1 - Fixed security headers
    $session1_id = 'fix_session_' . (time() - 3600); // 1 hour ago
    $fix_sessions[] = array(
        'timestamp' => time() - 3600,
        'session_id' => $session1_id,
        'rollback_id' => $session1_id,
        'backup_id' => $session1_id,
        'message' => 'Applied security header fixes',
        'details' => array(
            array(
                'issue_id' => 'test_issue_1',
                'title' => 'Test Security Headers Missing',
                'success' => true,
                'message' => 'Security headers added to .htaccess',
                'rollback_id' => $session1_id
            )
        ),
        'files_modified' => array('.htaccess'),
        'backup_created' => true
    );

    // Session 2 - Fixed compression
    $session2_id = 'fix_session_' . (time() - 1800); // 30 minutes ago
    $fix_sessions[] = array(
        'timestamp' => time() - 1800,
        'session_id' => $session2_id,
        'rollback_id' => $session2_id,
        'backup_id' => $session2_id,
        'message' => 'Applied compression fixes',
        'details' => array(
            array(
                'issue_id' => 'test_issue_2',
                'title' => 'Test Compression Not Enabled',
                'success' => true,
                'message' => 'GZIP compression enabled in .htaccess',
                'rollback_id' => $session2_id
            )
        ),
        'files_modified' => array('.htaccess'),
        'backup_created' => true
    );

    update_option('redco_diagnostic_fix_history', $fix_sessions);
    echo "✅ Created sample fix history with " . count($fix_sessions) . " sessions<br>\n";

    // 3. Create fixed issues record
    $fixed_issues = array(
        'test_issue_1' => array(
            'fixed_at' => time() - 3600,
            'rollback_id' => $session1_id,
            'session_id' => $session1_id
        ),
        'test_issue_2' => array(
            'fixed_at' => time() - 1800,
            'rollback_id' => $session2_id,
            'session_id' => $session2_id
        )
    );

    update_option('redco_fixed_issues', $fixed_issues);
    echo "✅ Created fixed issues record with " . count($fixed_issues) . " fixed issues<br>\n";

    // 4. Update diagnostic statistics
    $stats = array(
        'health_score' => 75,
        'performance_score' => 68,
        'issues_found' => 1, // Only 1 remaining (test_issue_3)
        'critical_issues' => 1,
        'auto_fixable_issues' => 0, // All auto-fixable issues were fixed
        'fixes_applied' => 2,
        'last_scan_time' => time(),
        'scan_frequency' => 'weekly',
        'auto_fix_enabled' => true,
        'emergency_mode_active' => false,
        'test_data_created' => time()
    );

    update_option('redco_diagnostic_stats', $stats);
    echo "✅ Updated diagnostic statistics<br>\n";

    // 5. Clear caches to ensure fresh data
    delete_transient('redco_diagnostic_results');
    delete_transient('redco_recent_fixes');
    wp_cache_delete('redco_diagnostic_results');
    wp_cache_delete('redco_recent_fixes');
    echo "✅ Cleared caches<br>\n";

    echo "<h2>✅ Test Data Created Successfully!</h2>\n";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<h3>📊 Test Data Summary</h3>\n";
    echo "<ul>\n";
    echo "<li><strong>Diagnostic Issues:</strong> 3 total (1 critical, 2 auto-fixable)</li>\n";
    echo "<li><strong>Fixed Issues:</strong> 2 issues fixed</li>\n";
    echo "<li><strong>Remaining Issues:</strong> 1 issue (critical, not auto-fixable)</li>\n";
    echo "<li><strong>Fix Sessions:</strong> 2 sessions with rollback data</li>\n";
    echo "<li><strong>Statistics:</strong> Health 75%, Performance 68%</li>\n";
    echo "</ul>\n";
    echo "</div>\n";

    echo "<h3>🧪 Testing Instructions</h3>\n";
    echo "<ol>\n";
    echo "<li><strong>Go to Diagnostic Module:</strong> Navigate to the Diagnostic & Auto-Fix module</li>\n";
    echo "<li><strong>Verify Display:</strong> Check that Recent Fixes shows 2 entries with rollback buttons</li>\n";
    echo "<li><strong>Check Statistics:</strong> Verify dashboard shows correct counts (1 issue, 2 fixes applied)</li>\n";
    echo "<li><strong>Test Rollback:</strong> Click rollback on one of the Recent Fixes entries</li>\n";
    echo "<li><strong>Validate State:</strong> After rollback, verify:</li>\n";
    echo "<ul>\n";
    echo "<li>Entry is removed from Recent Fixes</li>\n";
    echo "<li>Statistics are updated (issues count increases, fixes count decreases)</li>\n";
    echo "<li>Rolled-back issue appears in Recent Issues Found</li>\n";
    echo "<li>Visual feedback shows statistics were updated</li>\n";
    echo "</ul>\n";
    echo "<li><strong>Test Reset Button:</strong> Try the new 'Reset All Fixes' button in Emergency Controls</li>\n";
    echo "</ol>\n";

    echo "<h3>🎯 Expected Rollback Behavior</h3>\n";
    echo "<div style='background: #e2e3e5; border: 1px solid #d6d8db; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
    echo "<p><strong>Before Rollback:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Recent Fixes: 2 entries</li>\n";
    echo "<li>Issues Found: 1</li>\n";
    echo "<li>Fixes Applied: 2</li>\n";
    echo "</ul>\n";
    echo "<p><strong>After Rolling Back Session 1:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Recent Fixes: 1 entry (session 2 only)</li>\n";
    echo "<li>Issues Found: 2 (test_issue_1 restored + test_issue_3)</li>\n";
    echo "<li>Fixes Applied: 1</li>\n";
    echo "<li>Statistics updated with animation</li>\n";
    echo "</ul>\n";
    echo "</div>\n";

    echo "<p><a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "' style='background: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;'>🚀 Go to Diagnostic Module</a></p>\n";
    echo "<p><a href='immediate-diagnostic-reset.php' style='background: #dc3232; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🗑️ Reset Test Data</a></p>\n";

} catch (Exception $e) {
    echo "<h2>❌ Test Data Creation Failed</h2>\n";
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; border-radius: 5px; margin: 20px 0;'>\n";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>\n";
    echo "</div>\n";
}
?>
