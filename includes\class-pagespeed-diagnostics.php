<?php
/**
 * PageSpeed API Diagnostics for Redco Optimizer
 *
 * Comprehensive diagnostic tool for PageSpeed Insights API integration
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Include API endpoints configuration
require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'includes/class-api-endpoints.php';

class Redco_Optimizer_PageSpeed_Diagnostics {

    /**
     * Initialize diagnostics
     */
    public function init() {
        add_action('wp_ajax_redco_diagnose_pagespeed_api', array($this, 'ajax_diagnose_api'));
        add_action('wp_ajax_redco_test_pagespeed_api', array($this, 'ajax_test_api'));
        add_action('wp_ajax_redco_validate_api_key', array($this, 'ajax_validate_api_key'));
    }

    /**
     * AJAX handler for comprehensive API diagnostics
     */
    public function ajax_diagnose_api() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_die('Security check failed');
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        $diagnostics = $this->run_comprehensive_diagnostics();

        wp_send_json_success(array(
            'diagnostics' => $diagnostics,
            'recommendations' => $this->get_recommendations($diagnostics)
        ));
    }

    /**
     * Run comprehensive PageSpeed API diagnostics
     */
    public function run_comprehensive_diagnostics() {
        $diagnostics = array();

        // 1. Configuration Check
        $diagnostics['configuration'] = $this->check_configuration();

        // 2. Network Connectivity Check
        $diagnostics['connectivity'] = $this->check_connectivity();

        // 3. API Key Validation
        $diagnostics['api_key'] = $this->validate_api_key();

        // 4. Server Environment Check
        $diagnostics['environment'] = $this->check_server_environment();

        // 5. WordPress Environment Check
        $diagnostics['wordpress'] = $this->check_wordpress_environment();

        // 6. Cache Status Check
        $diagnostics['cache'] = $this->check_cache_status();

        // 7. Error Log Analysis
        $diagnostics['error_logs'] = $this->analyze_error_logs();

        return $diagnostics;
    }

    /**
     * Check API configuration
     */
    private function check_configuration() {
        $performance_options = get_option('redco_optimizer_performance', array());
        $api_key = isset($performance_options['pagespeed_api_key']) ? $performance_options['pagespeed_api_key'] : '';
        
        $issues = array();
        $status = 'good';

        if (empty($api_key)) {
            $issues[] = 'No API key configured';
            $status = 'error';
        } elseif (strlen($api_key) < 30) {
            $issues[] = 'API key appears to be too short (should be ~39 characters)';
            $status = 'warning';
        }

        // Check if monitoring is enabled
        $monitoring_enabled = isset($performance_options['enable_monitoring']) ? $performance_options['enable_monitoring'] : 1;
        if (!$monitoring_enabled) {
            $issues[] = 'Performance monitoring is disabled';
            $status = 'warning';
        }

        return array(
            'status' => $status,
            'api_key_length' => strlen($api_key),
            'api_key_configured' => !empty($api_key),
            'monitoring_enabled' => $monitoring_enabled,
            'issues' => $issues
        );
    }

    /**
     * Check network connectivity to Google APIs
     */
    private function check_connectivity() {
        $issues = array();
        $status = 'good';

        // Test basic connectivity to Google
        $google_response = wp_remote_get('https://www.google.com', array('timeout' => 10));
        if (is_wp_error($google_response)) {
            $issues[] = 'Cannot connect to Google: ' . $google_response->get_error_message();
            $status = 'error';
        }

        // Test connectivity to PageSpeed API endpoint using actual site URL
        $site_url = home_url('/');
        $api_response = wp_remote_get(Redco_API_Endpoints::get_pagespeed_validation_url($site_url, ''), array(
            'timeout' => Redco_API_Endpoints::get_timeout('connectivity'),
            'headers' => Redco_API_Endpoints::get_default_headers()
        ));
        if (is_wp_error($api_response)) {
            $issues[] = 'Cannot connect to PageSpeed API: ' . $api_response->get_error_message();
            $status = 'error';
        } else {
            $response_code = wp_remote_retrieve_response_code($api_response);
            if ($response_code === 400) {
                // Expected for test without API key
                $issues[] = 'PageSpeed API reachable (400 response expected without API key)';
            } elseif ($response_code !== 200) {
                $issues[] = "Unexpected response code from PageSpeed API: {$response_code}";
                $status = 'warning';
            }
        }

        return array(
            'status' => $status,
            'google_reachable' => !is_wp_error($google_response),
            'api_reachable' => !is_wp_error($api_response),
            'issues' => $issues
        );
    }

    /**
     * Validate API key
     */
    private function validate_api_key() {
        $performance_options = get_option('redco_optimizer_performance', array());
        $api_key = isset($performance_options['pagespeed_api_key']) ? $performance_options['pagespeed_api_key'] : '';
        
        $issues = array();
        $status = 'good';

        if (empty($api_key)) {
            return array(
                'status' => 'error',
                'valid' => false,
                'issues' => array('No API key to validate')
            );
        }

        // Test API key with a simple request using the actual site URL
        $site_url = home_url('/');
        $test_url = Redco_API_Endpoints::get_pagespeed_validation_url($site_url, $api_key);

        $response = wp_remote_get($test_url, array(
            'timeout' => Redco_API_Endpoints::get_timeout('validation'),
            'headers' => Redco_API_Endpoints::get_default_headers()
        ));
        
        if (is_wp_error($response)) {
            $issues[] = 'API key validation failed: ' . $response->get_error_message();
            $status = 'error';
            return array(
                'status' => $status,
                'valid' => false,
                'issues' => $issues
            );
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        $response_data = json_decode($response_body, true);

        switch ($response_code) {
            case 200:
                $status = 'good';
                break;
            case 400:
                if (isset($response_data['error']['message'])) {
                    $issues[] = 'API Error: ' . $response_data['error']['message'];
                } else {
                    $issues[] = 'Bad request (400) - check API key format';
                }
                $status = 'error';
                break;
            case 403:
                $issues[] = 'API key invalid or quota exceeded (403)';
                $status = 'error';
                break;
            case 429:
                $issues[] = 'Rate limit exceeded (429) - too many requests';
                $status = 'warning';
                break;
            default:
                $issues[] = "Unexpected response code: {$response_code}";
                $status = 'warning';
        }

        return array(
            'status' => $status,
            'valid' => $response_code === 200,
            'response_code' => $response_code,
            'quota_available' => $response_code !== 403,
            'rate_limited' => $response_code === 429,
            'issues' => $issues
        );
    }

    /**
     * Check server environment
     */
    private function check_server_environment() {
        $issues = array();
        $status = 'good';

        // Check cURL
        if (!function_exists('curl_init')) {
            $issues[] = 'cURL is not available';
            $status = 'error';
        }

        // Check allow_url_fopen
        if (!ini_get('allow_url_fopen')) {
            $issues[] = 'allow_url_fopen is disabled';
            $status = 'warning';
        }

        // Check SSL support
        if (!extension_loaded('openssl')) {
            $issues[] = 'OpenSSL extension not loaded';
            $status = 'error';
        }

        // Check memory limit
        $memory_limit = ini_get('memory_limit');
        $memory_bytes = $this->convert_to_bytes($memory_limit);
        if ($memory_bytes < 128 * 1024 * 1024) { // 128MB
            $issues[] = "Low memory limit: {$memory_limit}";
            $status = 'warning';
        }

        // Check execution time
        $max_execution_time = ini_get('max_execution_time');
        if ($max_execution_time > 0 && $max_execution_time < 30) {
            $issues[] = "Low execution time limit: {$max_execution_time}s";
            $status = 'warning';
        }

        return array(
            'status' => $status,
            'curl_available' => function_exists('curl_init'),
            'allow_url_fopen' => ini_get('allow_url_fopen'),
            'openssl_loaded' => extension_loaded('openssl'),
            'memory_limit' => $memory_limit,
            'max_execution_time' => $max_execution_time,
            'issues' => $issues
        );
    }

    /**
     * Check WordPress environment
     */
    private function check_wordpress_environment() {
        $issues = array();
        $status = 'good';

        // Check WordPress version
        global $wp_version;
        if (version_compare($wp_version, '5.0', '<')) {
            $issues[] = "Old WordPress version: {$wp_version}";
            $status = 'warning';
        }

        // Check if wp_remote_get is available
        if (!function_exists('wp_remote_get')) {
            $issues[] = 'wp_remote_get function not available';
            $status = 'error';
        }

        // Check for conflicting plugins
        $conflicting_plugins = $this->check_conflicting_plugins();
        if (!empty($conflicting_plugins)) {
            $issues[] = 'Conflicting plugins detected: ' . implode(', ', $conflicting_plugins);
            $status = 'warning';
        }

        return array(
            'status' => $status,
            'wp_version' => $wp_version,
            'wp_remote_available' => function_exists('wp_remote_get'),
            'conflicting_plugins' => $conflicting_plugins,
            'issues' => $issues
        );
    }

    /**
     * Check cache status
     */
    private function check_cache_status() {
        $mobile_cache = get_transient('redco_pagespeed_scores_mobile');
        $desktop_cache = get_transient('redco_pagespeed_scores_desktop');
        
        $issues = array();
        $status = 'good';

        if ($mobile_cache === false && $desktop_cache === false) {
            $issues[] = 'No cached PageSpeed data available';
            $status = 'warning';
        }

        return array(
            'status' => $status,
            'mobile_cached' => $mobile_cache !== false,
            'desktop_cached' => $desktop_cache !== false,
            'mobile_cache_age' => $mobile_cache ? time() - $mobile_cache['timestamp'] : null,
            'desktop_cache_age' => $desktop_cache ? time() - $desktop_cache['timestamp'] : null,
            'issues' => $issues
        );
    }

    /**
     * Analyze error logs for PageSpeed API issues
     */
    private function analyze_error_logs() {
        $issues = array();
        $status = 'good';

        // This is a simplified version - in a real implementation,
        // you would parse actual error logs
        $recent_errors = array();

        // Check if WP_DEBUG_LOG is enabled
        if (!defined('WP_DEBUG_LOG') || !WP_DEBUG_LOG) {
            $issues[] = 'WP_DEBUG_LOG is not enabled - cannot analyze error logs';
            $status = 'warning';
        }

        return array(
            'status' => $status,
            'debug_log_enabled' => defined('WP_DEBUG_LOG') && WP_DEBUG_LOG,
            'recent_errors' => $recent_errors,
            'issues' => $issues
        );
    }

    /**
     * Check for conflicting plugins
     */
    private function check_conflicting_plugins() {
        $conflicting = array();
        
        $known_conflicts = array(
            'wp-rocket/wp-rocket.php' => 'WP Rocket',
            'autoptimize/autoptimize.php' => 'Autoptimize',
            'w3-total-cache/w3-total-cache.php' => 'W3 Total Cache'
        );

        foreach ($known_conflicts as $plugin_file => $plugin_name) {
            if (is_plugin_active($plugin_file)) {
                $conflicting[] = $plugin_name;
            }
        }

        return $conflicting;
    }

    /**
     * Convert memory limit to bytes
     */
    private function convert_to_bytes($value) {
        $value = trim($value);
        $last = strtolower($value[strlen($value) - 1]);
        $value = (int) $value;
        
        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }
        
        return $value;
    }

    /**
     * AJAX handler for testing API with current settings
     */
    public function ajax_test_api() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_die('Security check failed');
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        $strategy = isset($_POST['strategy']) ? sanitize_text_field($_POST['strategy']) : 'mobile';
        $url = isset($_POST['url']) ? esc_url_raw($_POST['url']) : home_url('/');

        $result = $this->test_api_call($url, $strategy);

        wp_send_json_success($result);
    }

    /**
     * Test API call with detailed logging
     */
    public function test_api_call($url, $strategy = 'mobile') {
        $performance_options = get_option('redco_optimizer_performance', array());
        $api_key = isset($performance_options['pagespeed_api_key']) ? $performance_options['pagespeed_api_key'] : '';

        if (empty($api_key)) {
            return array(
                'success' => false,
                'error' => 'No API key configured',
                'details' => array()
            );
        }

        // Build API URL using centralized endpoint configuration
        $api_url = Redco_API_Endpoints::get_pagespeed_url($url, $api_key, $strategy);

        $start_time = microtime(true);
        $response = wp_remote_get($api_url, array(
            'timeout' => Redco_API_Endpoints::get_timeout('pagespeed'),
            'headers' => Redco_API_Endpoints::get_default_headers()
        ));
        $end_time = microtime(true);

        $details = array(
            'api_url' => $api_url,
            'request_time' => round(($end_time - $start_time) * 1000, 2) . 'ms',
            'strategy' => $strategy,
            'url_tested' => $url
        );

        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'error' => $response->get_error_message(),
                'details' => $details
            );
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);
        $response_data = json_decode($response_body, true);

        $details['response_code'] = $response_code;
        $details['response_size'] = strlen($response_body) . ' bytes';

        if ($response_code !== 200) {
            $error_message = 'HTTP ' . $response_code;
            if (isset($response_data['error']['message'])) {
                $error_message .= ': ' . $response_data['error']['message'];
            }
            
            return array(
                'success' => false,
                'error' => $error_message,
                'details' => $details
            );
        }

        if (!isset($response_data['lighthouseResult']['categories'])) {
            return array(
                'success' => false,
                'error' => 'Invalid API response structure',
                'details' => $details
            );
        }

        $categories = $response_data['lighthouseResult']['categories'];
        $scores = array();

        foreach ($categories as $category => $data) {
            if (isset($data['score'])) {
                $scores[$category] = round($data['score'] * 100);
            }
        }

        return array(
            'success' => true,
            'scores' => $scores,
            'details' => $details
        );
    }

    /**
     * AJAX handler for validating API key
     */
    public function ajax_validate_api_key() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_die('Security check failed');
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        $api_key = isset($_POST['api_key']) ? sanitize_text_field($_POST['api_key']) : '';

        if (empty($api_key)) {
            wp_send_json_error(array('message' => 'No API key provided'));
        }

        $validation_result = $this->validate_specific_api_key($api_key);

        if ($validation_result['valid']) {
            wp_send_json_success($validation_result);
        } else {
            wp_send_json_error($validation_result);
        }
    }

    /**
     * Validate a specific API key
     */
    private function validate_specific_api_key($api_key) {
        $site_url = home_url('/');
        $test_url = Redco_API_Endpoints::get_pagespeed_validation_url($site_url, $api_key);

        $response = wp_remote_get($test_url, array(
            'timeout' => Redco_API_Endpoints::get_timeout('validation'),
            'headers' => Redco_API_Endpoints::get_default_headers()
        ));
        
        if (is_wp_error($response)) {
            return array(
                'valid' => false,
                'message' => 'Network error: ' . $response->get_error_message()
            );
        }

        $response_code = wp_remote_retrieve_response_code($response);
        
        switch ($response_code) {
            case 200:
                return array(
                    'valid' => true,
                    'message' => 'API key is valid and working'
                );
            case 400:
                return array(
                    'valid' => false,
                    'message' => 'Invalid API key format'
                );
            case 403:
                return array(
                    'valid' => false,
                    'message' => 'API key is invalid or quota exceeded'
                );
            case 429:
                return array(
                    'valid' => true,
                    'message' => 'API key is valid but rate limited'
                );
            default:
                return array(
                    'valid' => false,
                    'message' => 'Unexpected response code: ' . $response_code
                );
        }
    }

    /**
     * Get recommendations based on diagnostics
     */
    public function get_recommendations($diagnostics) {
        $recommendations = array();

        // Configuration recommendations
        if ($diagnostics['configuration']['status'] === 'error') {
            $recommendations[] = array(
                'priority' => 'high',
                'category' => 'Configuration',
                'issue' => 'API key not configured',
                'solution' => 'Add your Google PageSpeed Insights API key in Settings > Performance'
            );
        }

        // Connectivity recommendations
        if ($diagnostics['connectivity']['status'] === 'error') {
            $recommendations[] = array(
                'priority' => 'high',
                'category' => 'Network',
                'issue' => 'Cannot connect to PageSpeed API',
                'solution' => 'Check firewall settings and ensure outbound HTTPS connections are allowed'
            );
        }

        // API key recommendations
        if ($diagnostics['api_key']['status'] === 'error') {
            $recommendations[] = array(
                'priority' => 'high',
                'category' => 'API Key',
                'issue' => 'API key validation failed',
                'solution' => 'Verify your API key is correct and has PageSpeed Insights API enabled'
            );
        }

        // Environment recommendations
        if ($diagnostics['environment']['status'] === 'error') {
            $recommendations[] = array(
                'priority' => 'medium',
                'category' => 'Server',
                'issue' => 'Server environment issues detected',
                'solution' => 'Contact your hosting provider to enable required PHP extensions'
            );
        }

        return $recommendations;
    }
}
