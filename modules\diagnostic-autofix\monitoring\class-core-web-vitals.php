<?php
/**
 * Core Web Vitals Integration for Redco Optimizer
 * 
 * Phase 2 Enhancement: Google PageSpeed Insights API integration
 * Measures LCP, FID, CLS and provides actionable recommendations
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Core_Web_Vitals {
    
    private $api_key;
    private $api_endpoint = 'https://www.googleapis.com/pagespeedinsights/v5/runPagespeed';
    
    private $vitals_thresholds = array(
        'lcp' => array('good' => 2.5, 'needs_improvement' => 4.0), // seconds
        'fid' => array('good' => 100, 'needs_improvement' => 300), // milliseconds
        'cls' => array('good' => 0.1, 'needs_improvement' => 0.25), // score
        'fcp' => array('good' => 1.8, 'needs_improvement' => 3.0), // seconds
        'ttfb' => array('good' => 0.8, 'needs_improvement' => 1.8) // seconds
    );
    
    /**
     * Initialize Core Web Vitals integration
     */
    public function init() {
        $this->api_key = get_option('redco_pagespeed_api_key', '');
        
        // AJAX handlers
        add_action('wp_ajax_redco_test_core_web_vitals', array($this, 'ajax_test_core_web_vitals'));
        add_action('wp_ajax_redco_get_vitals_history', array($this, 'ajax_get_vitals_history'));
        add_action('wp_ajax_redco_save_pagespeed_api_key', array($this, 'ajax_save_api_key'));
        add_action('wp_ajax_redco_get_vitals_recommendations', array($this, 'ajax_get_recommendations'));
        
        // Cron hooks for automated testing
        add_action('redco_test_core_web_vitals_cron', array($this, 'run_automated_vitals_test'));
        
        // Schedule automated testing if enabled
        $this->maybe_schedule_vitals_testing();
        
        // Ensure vitals tables exist
        $this->maybe_create_vitals_tables();
    }
    
    /**
     * AJAX: Test Core Web Vitals
     */
    public function ajax_test_core_web_vitals() {
        // Accept both diagnostic nonce and general nonce for compatibility
        $nonce = $_POST['nonce'] ?? '';
        $nonce_valid = wp_verify_nonce($nonce, 'redco_diagnostic_nonce') ||
                      wp_verify_nonce($nonce, 'redco_optimizer_nonce');

        if (!$nonce_valid) {
            wp_send_json_error('Security verification failed');
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $url = sanitize_url($_POST['url'] ?? home_url());
        $strategy = sanitize_text_field($_POST['strategy'] ?? 'mobile'); // mobile or desktop
        
        if (empty($this->api_key)) {
            // Fallback to local measurement
            $results = $this->measure_vitals_locally($url, $strategy);
        } else {
            // Use Google PageSpeed Insights API
            $results = $this->measure_vitals_with_api($url, $strategy);
        }
        
        if (is_wp_error($results)) {
            wp_send_json_error($results->get_error_message());
        } else {
            // Store results
            $this->store_vitals_results($results);
            wp_send_json_success($results);
        }
    }
    
    /**
     * Measure Core Web Vitals using Google PageSpeed Insights API
     */
    public function measure_vitals_with_api($url, $strategy = 'mobile') {
        if (empty($this->api_key)) {
            return new WP_Error('no_api_key', 'Google PageSpeed Insights API key not configured');
        }
        
        $api_url = add_query_arg(array(
            'url' => urlencode($url),
            'key' => $this->api_key,
            'strategy' => $strategy,
            'category' => 'performance',
            'fields' => 'lighthouseResult/audits,lighthouseResult/categories/performance/score,loadingExperience/metrics'
        ), $this->api_endpoint);
        
        $response = wp_remote_get($api_url, array(
            'timeout' => 60,
            'user-agent' => 'Redco-Optimizer/2.0'
        ));
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (empty($data) || isset($data['error'])) {
            return new WP_Error('api_error', $data['error']['message'] ?? 'API request failed');
        }
        
        return $this->parse_pagespeed_results($data, $url, $strategy);
    }
    
    /**
     * Parse PageSpeed Insights results
     */
    private function parse_pagespeed_results($data, $url, $strategy) {
        $lighthouse = $data['lighthouseResult'] ?? array();
        $audits = $lighthouse['audits'] ?? array();
        $loading_experience = $data['loadingExperience'] ?? array();
        
        $results = array(
            'url' => $url,
            'strategy' => $strategy,
            'timestamp' => time(),
            'performance_score' => round(($lighthouse['categories']['performance']['score'] ?? 0) * 100),
            'core_web_vitals' => array(),
            'other_metrics' => array(),
            'opportunities' => array(),
            'diagnostics' => array(),
            'field_data' => array()
        );
        
        // Extract Core Web Vitals
        $vitals_mapping = array(
            'largest-contentful-paint' => 'lcp',
            'first-input-delay' => 'fid',
            'cumulative-layout-shift' => 'cls',
            'first-contentful-paint' => 'fcp',
            'server-response-time' => 'ttfb'
        );
        
        foreach ($vitals_mapping as $audit_key => $vital_key) {
            if (isset($audits[$audit_key])) {
                $audit = $audits[$audit_key];
                $value = $audit['numericValue'] ?? 0;
                
                // Convert to appropriate units
                if (in_array($vital_key, array('lcp', 'fcp', 'ttfb'))) {
                    $value = round($value / 1000, 2); // Convert ms to seconds
                } elseif ($vital_key === 'fid') {
                    $value = round($value, 1); // Keep in milliseconds
                } elseif ($vital_key === 'cls') {
                    $value = round($value, 3); // Keep as score
                }
                
                $results['core_web_vitals'][$vital_key] = array(
                    'value' => $value,
                    'score' => $audit['score'] ?? 0,
                    'status' => $this->get_vitals_status($value, $vital_key),
                    'displayValue' => $audit['displayValue'] ?? '',
                    'title' => $audit['title'] ?? ''
                );
            }
        }
        
        // Extract field data (real user metrics)
        if (isset($loading_experience['metrics'])) {
            foreach ($loading_experience['metrics'] as $metric_key => $metric_data) {
                $results['field_data'][$metric_key] = array(
                    'percentile' => $metric_data['percentile'] ?? 0,
                    'distributions' => $metric_data['distributions'] ?? array(),
                    'category' => $metric_data['category'] ?? 'UNKNOWN'
                );
            }
        }
        
        // Extract opportunities (performance improvements)
        foreach ($audits as $audit_key => $audit) {
            if (isset($audit['details']['type']) && $audit['details']['type'] === 'opportunity') {
                $results['opportunities'][] = array(
                    'id' => $audit_key,
                    'title' => $audit['title'],
                    'description' => $audit['description'],
                    'score' => $audit['score'],
                    'numericValue' => $audit['numericValue'] ?? 0,
                    'displayValue' => $audit['displayValue'] ?? '',
                    'details' => $audit['details'] ?? array()
                );
            }
        }
        
        // Extract diagnostics
        $diagnostic_audits = array(
            'render-blocking-resources',
            'unused-css-rules',
            'unused-javascript',
            'uses-optimized-images',
            'uses-webp-images',
            'uses-text-compression',
            'uses-responsive-images'
        );
        
        foreach ($diagnostic_audits as $audit_key) {
            if (isset($audits[$audit_key])) {
                $audit = $audits[$audit_key];
                $results['diagnostics'][] = array(
                    'id' => $audit_key,
                    'title' => $audit['title'],
                    'description' => $audit['description'],
                    'score' => $audit['score'],
                    'displayValue' => $audit['displayValue'] ?? '',
                    'details' => $audit['details'] ?? array()
                );
            }
        }
        
        return $results;
    }
    
    /**
     * Measure Core Web Vitals locally (fallback method)
     */
    public function measure_vitals_locally($url, $strategy = 'mobile') {
        $results = array(
            'url' => $url,
            'strategy' => $strategy,
            'timestamp' => time(),
            'performance_score' => 0,
            'core_web_vitals' => array(),
            'source' => 'local_measurement',
            'note' => 'Local measurement - limited accuracy. Configure Google PageSpeed API for precise results.'
        );
        
        // Basic page load measurement
        $start_time = microtime(true);
        $response = wp_remote_get($url, array(
            'timeout' => 30,
            'user-agent' => $strategy === 'mobile' ? 
                'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)' : 
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64)'
        ));
        $end_time = microtime(true);
        
        if (is_wp_error($response)) {
            return $response;
        }
        
        $load_time = ($end_time - $start_time) * 1000; // Convert to milliseconds
        
        // Estimate Core Web Vitals based on load time
        $results['core_web_vitals'] = array(
            'lcp' => array(
                'value' => round($load_time / 1000 * 1.2, 2), // Estimate LCP as 120% of load time
                'status' => $this->get_vitals_status($load_time / 1000 * 1.2, 'lcp'),
                'estimated' => true
            ),
            'fid' => array(
                'value' => round($load_time * 0.1, 1), // Estimate FID as 10% of load time
                'status' => $this->get_vitals_status($load_time * 0.1, 'fid'),
                'estimated' => true
            ),
            'cls' => array(
                'value' => 0.05, // Default estimate
                'status' => 'good',
                'estimated' => true
            )
        );
        
        // Basic performance score estimation
        $avg_status_score = 0;
        foreach ($results['core_web_vitals'] as $vital) {
            $avg_status_score += $vital['status'] === 'good' ? 100 : ($vital['status'] === 'needs_improvement' ? 50 : 0);
        }
        $results['performance_score'] = round($avg_status_score / count($results['core_web_vitals']));
        
        return $results;
    }
    
    /**
     * Get vitals status based on thresholds
     */
    private function get_vitals_status($value, $vital_type) {
        if (!isset($this->vitals_thresholds[$vital_type])) {
            return 'unknown';
        }
        
        $thresholds = $this->vitals_thresholds[$vital_type];
        
        if ($value <= $thresholds['good']) {
            return 'good';
        } elseif ($value <= $thresholds['needs_improvement']) {
            return 'needs_improvement';
        } else {
            return 'poor';
        }
    }
    
    /**
     * Store vitals results in database
     */
    private function store_vitals_results($results) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'redco_core_web_vitals';
        
        $wpdb->insert(
            $table_name,
            array(
                'url' => $results['url'],
                'strategy' => $results['strategy'],
                'timestamp' => current_time('mysql'),
                'performance_score' => $results['performance_score'],
                'lcp_value' => $results['core_web_vitals']['lcp']['value'] ?? 0,
                'fid_value' => $results['core_web_vitals']['fid']['value'] ?? 0,
                'cls_value' => $results['core_web_vitals']['cls']['value'] ?? 0,
                'full_results' => json_encode($results)
            ),
            array('%s', '%s', '%s', '%d', '%f', '%f', '%f', '%s')
        );
    }
    
    /**
     * AJAX: Get vitals history
     */
    public function ajax_get_vitals_history() {
        check_ajax_referer('redco_diagnostic_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $days = intval($_POST['days'] ?? 30);
        $url = sanitize_url($_POST['url'] ?? home_url());
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'redco_core_web_vitals';
        
        $results = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM {$table_name} 
                 WHERE url = %s AND timestamp >= %s 
                 ORDER BY timestamp DESC",
                $url,
                date('Y-m-d H:i:s', strtotime("-{$days} days"))
            ),
            ARRAY_A
        );
        
        wp_send_json_success(array(
            'history' => $results,
            'summary' => $this->calculate_vitals_summary($results)
        ));
    }
    
    /**
     * Calculate vitals summary
     */
    private function calculate_vitals_summary($results) {
        if (empty($results)) {
            return array();
        }
        
        $summary = array(
            'total_tests' => count($results),
            'avg_performance_score' => 0,
            'avg_lcp' => 0,
            'avg_fid' => 0,
            'avg_cls' => 0,
            'trend' => 'stable'
        );
        
        $total_score = 0;
        $total_lcp = 0;
        $total_fid = 0;
        $total_cls = 0;
        
        foreach ($results as $result) {
            $total_score += $result['performance_score'];
            $total_lcp += $result['lcp_value'];
            $total_fid += $result['fid_value'];
            $total_cls += $result['cls_value'];
        }
        
        $count = count($results);
        $summary['avg_performance_score'] = round($total_score / $count);
        $summary['avg_lcp'] = round($total_lcp / $count, 2);
        $summary['avg_fid'] = round($total_fid / $count, 1);
        $summary['avg_cls'] = round($total_cls / $count, 3);
        
        // Calculate trend (compare first half vs second half)
        if ($count >= 4) {
            $half = floor($count / 2);
            $first_half_avg = array_sum(array_slice(array_column($results, 'performance_score'), 0, $half)) / $half;
            $second_half_avg = array_sum(array_slice(array_column($results, 'performance_score'), -$half)) / $half;
            
            if ($second_half_avg > $first_half_avg + 5) {
                $summary['trend'] = 'improving';
            } elseif ($second_half_avg < $first_half_avg - 5) {
                $summary['trend'] = 'declining';
            }
        }
        
        return $summary;
    }

    /**
     * AJAX: Save PageSpeed API key
     */
    public function ajax_save_api_key() {
        check_ajax_referer('redco_diagnostic_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $api_key = sanitize_text_field($_POST['api_key'] ?? '');

        if (empty($api_key)) {
            wp_send_json_error('API key is required');
            return;
        }

        // Test the API key
        $test_result = $this->test_api_key($api_key);

        if (is_wp_error($test_result)) {
            wp_send_json_error('Invalid API key: ' . $test_result->get_error_message());
            return;
        }

        update_option('redco_pagespeed_api_key', $api_key);
        $this->api_key = $api_key;

        wp_send_json_success('API key saved and validated successfully');
    }

    /**
     * Test API key validity
     */
    private function test_api_key($api_key) {
        $test_url = add_query_arg(array(
            'url' => urlencode(home_url()),
            'key' => $api_key,
            'strategy' => 'mobile',
            'fields' => 'lighthouseResult/categories/performance/score'
        ), $this->api_endpoint);

        $response = wp_remote_get($test_url, array(
            'timeout' => 30,
            'user-agent' => 'Redco-Optimizer/2.0'
        ));

        if (is_wp_error($response)) {
            return $response;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (isset($data['error'])) {
            return new WP_Error('api_error', $data['error']['message']);
        }

        return true;
    }

    /**
     * AJAX: Get vitals recommendations
     */
    public function ajax_get_recommendations() {
        check_ajax_referer('redco_diagnostic_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $url = sanitize_url($_POST['url'] ?? home_url());

        // Get latest vitals results
        global $wpdb;
        $table_name = $wpdb->prefix . 'redco_core_web_vitals';

        $latest_result = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT full_results FROM {$table_name}
                 WHERE url = %s
                 ORDER BY timestamp DESC
                 LIMIT 1",
                $url
            )
        );

        if (!$latest_result) {
            wp_send_json_error('No vitals data found. Please run a test first.');
            return;
        }

        $vitals_data = json_decode($latest_result->full_results, true);
        $recommendations = $this->generate_recommendations($vitals_data);

        wp_send_json_success($recommendations);
    }

    /**
     * Generate actionable recommendations based on vitals data
     */
    private function generate_recommendations($vitals_data) {
        $recommendations = array(
            'priority_fixes' => array(),
            'optimization_opportunities' => array(),
            'technical_improvements' => array(),
            'estimated_impact' => array()
        );

        $core_vitals = $vitals_data['core_web_vitals'] ?? array();
        $opportunities = $vitals_data['opportunities'] ?? array();
        $diagnostics = $vitals_data['diagnostics'] ?? array();

        // Analyze Core Web Vitals issues
        foreach ($core_vitals as $vital_name => $vital_data) {
            if ($vital_data['status'] === 'poor') {
                $recommendations['priority_fixes'][] = $this->get_vital_recommendation($vital_name, $vital_data);
            }
        }

        // Process PageSpeed opportunities
        foreach ($opportunities as $opportunity) {
            if ($opportunity['score'] < 0.9) { // Less than 90% score
                $recommendations['optimization_opportunities'][] = array(
                    'title' => $opportunity['title'],
                    'description' => $opportunity['description'],
                    'potential_savings' => $opportunity['displayValue'],
                    'priority' => $this->calculate_opportunity_priority($opportunity),
                    'implementation' => $this->get_implementation_guide($opportunity['id'])
                );
            }
        }

        // Process diagnostics
        foreach ($diagnostics as $diagnostic) {
            if ($diagnostic['score'] < 0.9) {
                $recommendations['technical_improvements'][] = array(
                    'title' => $diagnostic['title'],
                    'description' => $diagnostic['description'],
                    'impact' => $this->estimate_diagnostic_impact($diagnostic),
                    'implementation' => $this->get_implementation_guide($diagnostic['id'])
                );
            }
        }

        // Calculate estimated impact
        $recommendations['estimated_impact'] = $this->calculate_estimated_impact($recommendations);

        return $recommendations;
    }

    /**
     * Get recommendation for specific Core Web Vital
     */
    private function get_vital_recommendation($vital_name, $vital_data) {
        $recommendations = array(
            'lcp' => array(
                'title' => 'Improve Largest Contentful Paint (LCP)',
                'current_value' => $vital_data['value'] . 's',
                'target_value' => '< 2.5s',
                'priority' => 'high',
                'actions' => array(
                    'Optimize images and use next-gen formats (WebP, AVIF)',
                    'Implement lazy loading for images',
                    'Minimize render-blocking resources',
                    'Use a Content Delivery Network (CDN)',
                    'Optimize server response times'
                )
            ),
            'fid' => array(
                'title' => 'Improve First Input Delay (FID)',
                'current_value' => $vital_data['value'] . 'ms',
                'target_value' => '< 100ms',
                'priority' => 'high',
                'actions' => array(
                    'Minimize JavaScript execution time',
                    'Remove unused JavaScript',
                    'Split code and load only what\'s needed',
                    'Use web workers for heavy computations',
                    'Optimize third-party scripts'
                )
            ),
            'cls' => array(
                'title' => 'Improve Cumulative Layout Shift (CLS)',
                'current_value' => $vital_data['value'],
                'target_value' => '< 0.1',
                'priority' => 'medium',
                'actions' => array(
                    'Set explicit dimensions for images and videos',
                    'Reserve space for ads and embeds',
                    'Avoid inserting content above existing content',
                    'Use CSS aspect-ratio for responsive images',
                    'Preload fonts to prevent font swapping'
                )
            )
        );

        return $recommendations[$vital_name] ?? array();
    }

    /**
     * Calculate opportunity priority
     */
    private function calculate_opportunity_priority($opportunity) {
        $score = $opportunity['score'] ?? 1;
        $numeric_value = $opportunity['numericValue'] ?? 0;

        if ($score < 0.5 && $numeric_value > 1000) {
            return 'high';
        } elseif ($score < 0.7 && $numeric_value > 500) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * Get implementation guide for specific optimization
     */
    private function get_implementation_guide($optimization_id) {
        $guides = array(
            'render-blocking-resources' => array(
                'description' => 'Eliminate render-blocking CSS and JavaScript',
                'steps' => array(
                    'Inline critical CSS',
                    'Defer non-critical CSS',
                    'Minify CSS and JavaScript files',
                    'Use async or defer attributes for scripts'
                ),
                'redco_fixes' => array('minify_css_js', 'defer_non_critical_css')
            ),
            'unused-css-rules' => array(
                'description' => 'Remove unused CSS to reduce file size',
                'steps' => array(
                    'Audit CSS usage with browser dev tools',
                    'Remove unused CSS rules',
                    'Use CSS purging tools',
                    'Split CSS by page/component'
                ),
                'redco_fixes' => array('cleanup_unused_css')
            ),
            'unused-javascript' => array(
                'description' => 'Remove unused JavaScript code',
                'steps' => array(
                    'Analyze JavaScript usage',
                    'Remove unused libraries and plugins',
                    'Use tree shaking for bundled code',
                    'Load JavaScript conditionally'
                ),
                'redco_fixes' => array('cleanup_unused_js')
            ),
            'uses-optimized-images' => array(
                'description' => 'Optimize images for better performance',
                'steps' => array(
                    'Compress images without quality loss',
                    'Use appropriate image formats',
                    'Implement responsive images',
                    'Use image CDN services'
                ),
                'redco_fixes' => array('optimize_images', 'enable_webp')
            )
        );

        return $guides[$optimization_id] ?? array(
            'description' => 'General optimization recommended',
            'steps' => array('Review PageSpeed Insights recommendations'),
            'redco_fixes' => array()
        );
    }

    /**
     * Estimate diagnostic impact
     */
    private function estimate_diagnostic_impact($diagnostic) {
        $score = $diagnostic['score'] ?? 1;

        if ($score < 0.3) {
            return 'high';
        } elseif ($score < 0.7) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * Calculate estimated impact of all recommendations
     */
    private function calculate_estimated_impact($recommendations) {
        $high_priority_count = 0;
        $medium_priority_count = 0;
        $low_priority_count = 0;

        // Count priority fixes
        foreach ($recommendations['priority_fixes'] as $fix) {
            if ($fix['priority'] === 'high') $high_priority_count++;
            elseif ($fix['priority'] === 'medium') $medium_priority_count++;
            else $low_priority_count++;
        }

        // Count optimization opportunities
        foreach ($recommendations['optimization_opportunities'] as $opportunity) {
            if ($opportunity['priority'] === 'high') $high_priority_count++;
            elseif ($opportunity['priority'] === 'medium') $medium_priority_count++;
            else $low_priority_count++;
        }

        // Estimate performance improvement
        $estimated_improvement = ($high_priority_count * 15) + ($medium_priority_count * 8) + ($low_priority_count * 3);
        $estimated_improvement = min(50, $estimated_improvement); // Cap at 50%

        return array(
            'performance_score_improvement' => $estimated_improvement . '%',
            'high_priority_items' => $high_priority_count,
            'medium_priority_items' => $medium_priority_count,
            'low_priority_items' => $low_priority_count,
            'total_recommendations' => $high_priority_count + $medium_priority_count + $low_priority_count
        );
    }

    /**
     * Run automated vitals test
     */
    public function run_automated_vitals_test() {
        $config = get_option('redco_vitals_config', array('enabled' => false));

        if (empty($config['enabled'])) {
            return;
        }

        $urls_to_test = $config['urls'] ?? array(home_url());
        $strategy = $config['strategy'] ?? 'mobile';

        foreach ($urls_to_test as $url) {
            if (!empty($this->api_key)) {
                $results = $this->measure_vitals_with_api($url, $strategy);
            } else {
                $results = $this->measure_vitals_locally($url, $strategy);
            }

            if (!is_wp_error($results)) {
                $this->store_vitals_results($results);
            }
        }
    }

    /**
     * Maybe schedule vitals testing
     */
    private function maybe_schedule_vitals_testing() {
        $config = get_option('redco_vitals_config', array('enabled' => false));

        if ($config['enabled'] && !wp_next_scheduled('redco_test_core_web_vitals_cron')) {
            wp_schedule_event(time(), 'daily', 'redco_test_core_web_vitals_cron');
        }
    }

    /**
     * Maybe create vitals tables
     */
    private function maybe_create_vitals_tables() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'redco_core_web_vitals';

        if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") != $table_name) {
            $charset_collate = $wpdb->get_charset_collate();

            $sql = "CREATE TABLE {$table_name} (
                id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                url varchar(500) NOT NULL,
                strategy varchar(20) NOT NULL DEFAULT 'mobile',
                timestamp datetime NOT NULL,
                performance_score int(3) NOT NULL DEFAULT 0,
                lcp_value decimal(5,2) NOT NULL DEFAULT 0,
                fid_value decimal(6,1) NOT NULL DEFAULT 0,
                cls_value decimal(5,3) NOT NULL DEFAULT 0,
                full_results longtext DEFAULT NULL,
                PRIMARY KEY (id),
                KEY url_idx (url(255)),
                KEY timestamp_idx (timestamp),
                KEY strategy_idx (strategy)
            ) {$charset_collate};";

            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
        }
    }
}
