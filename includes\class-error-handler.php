<?php
/**
 * Error Handling and Logging System for Redco Optimizer
 * 
 * Centralized error handling with different log levels and contexts
 * 
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Error_Handler {
    
    /**
     * Log levels
     */
    const LOG_EMERGENCY = 'emergency';
    const LOG_ALERT = 'alert';
    const LOG_CRITICAL = 'critical';
    const LOG_ERROR = 'error';
    const LOG_WARNING = 'warning';
    const LOG_NOTICE = 'notice';
    const LOG_INFO = 'info';
    const LOG_DEBUG = 'debug';
    
    /**
     * Log contexts
     */
    const CONTEXT_WEBP = 'webp';
    const CONTEXT_CACHE = 'cache';
    const CONTEXT_API = 'api';
    const CONTEXT_SETTINGS = 'settings';
    const CONTEXT_SECURITY = 'security';
    const CONTEXT_PERFORMANCE = 'performance';
    const CONTEXT_GENERAL = 'general';
    
    /**
     * Maximum log file size (5MB)
     */
    const MAX_LOG_SIZE = 5242880;
    
    /**
     * Maximum number of log files to keep
     */
    const MAX_LOG_FILES = 5;
    
    /**
     * Instance
     */
    private static $instance = null;
    
    /**
     * Log directory
     */
    private $log_dir;
    
    /**
     * Whether logging is enabled
     */
    private $logging_enabled;
    
    /**
     * Minimum log level
     */
    private $min_log_level;
    
    /**
     * Log level priorities
     */
    private static $log_priorities = array(
        self::LOG_EMERGENCY => 0,
        self::LOG_ALERT => 1,
        self::LOG_CRITICAL => 2,
        self::LOG_ERROR => 3,
        self::LOG_WARNING => 4,
        self::LOG_NOTICE => 5,
        self::LOG_INFO => 6,
        self::LOG_DEBUG => 7
    );
    
    /**
     * Get instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->log_dir = Redco_Config::get_cache_dir('logs');
        $this->logging_enabled = $this->should_enable_logging();
        $this->min_log_level = $this->get_min_log_level();
        
        // Create log directory if it doesn't exist
        if ($this->logging_enabled && !file_exists($this->log_dir)) {
            wp_mkdir_p($this->log_dir);
            $this->create_htaccess();
        }
    }
    
    /**
     * Log a message
     * 
     * @param string $level Log level
     * @param string $message Log message
     * @param string $context Log context
     * @param array $data Additional data
     */
    public static function log($level, $message, $context = self::CONTEXT_GENERAL, $data = array()) {
        $instance = self::get_instance();
        $instance->write_log($level, $message, $context, $data);
    }
    
    /**
     * Log emergency message
     */
    public static function emergency($message, $context = self::CONTEXT_GENERAL, $data = array()) {
        self::log(self::LOG_EMERGENCY, $message, $context, $data);
    }
    
    /**
     * Log alert message
     */
    public static function alert($message, $context = self::CONTEXT_GENERAL, $data = array()) {
        self::log(self::LOG_ALERT, $message, $context, $data);
    }
    
    /**
     * Log critical message
     */
    public static function critical($message, $context = self::CONTEXT_GENERAL, $data = array()) {
        self::log(self::LOG_CRITICAL, $message, $context, $data);
    }
    
    /**
     * Log error message
     */
    public static function error($message, $context = self::CONTEXT_GENERAL, $data = array()) {
        self::log(self::LOG_ERROR, $message, $context, $data);
    }
    
    /**
     * Log warning message
     */
    public static function warning($message, $context = self::CONTEXT_GENERAL, $data = array()) {
        self::log(self::LOG_WARNING, $message, $context, $data);
    }
    
    /**
     * Log notice message
     */
    public static function notice($message, $context = self::CONTEXT_GENERAL, $data = array()) {
        self::log(self::LOG_NOTICE, $message, $context, $data);
    }
    
    /**
     * Log info message
     */
    public static function info($message, $context = self::CONTEXT_GENERAL, $data = array()) {
        self::log(self::LOG_INFO, $message, $context, $data);
    }
    
    /**
     * Log debug message
     */
    public static function debug($message, $context = self::CONTEXT_GENERAL, $data = array()) {
        self::log(self::LOG_DEBUG, $message, $context, $data);
    }
    
    /**
     * Write log entry
     * SECURITY FIX: Enhanced logging with security context handling
     */
    private function write_log($level, $message, $context, $data) {
        if (!$this->logging_enabled) {
            return;
        }

        // Check if log level meets minimum threshold
        if (!$this->should_log_level($level)) {
            return;
        }

        // SECURITY FIX: Enhanced data for security context
        if ($context === self::CONTEXT_SECURITY) {
            $data = $this->enhance_security_data($data);
        }

        $log_file = $this->get_log_file($context);

        // Rotate log if it's too large
        $this->rotate_log_if_needed($log_file);

        $log_entry = $this->format_log_entry($level, $message, $context, $data);

        // Write to log file
        file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);

        // SECURITY FIX: Always log security events to WordPress error log
        if ($context === self::CONTEXT_SECURITY ||
            in_array($level, array(self::LOG_EMERGENCY, self::LOG_ALERT, self::LOG_CRITICAL, self::LOG_ERROR))) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("Redco Optimizer [{$level}] [{$context}]: {$message}");
            }
        }
    }
    
    /**
     * Format log entry
     * HIGH PRIORITY SECURITY FIX: Restrict debug information in production
     */
    private function format_log_entry($level, $message, $context, $data) {
        $timestamp = current_time('Y-m-d H:i:s');
        $level_upper = strtoupper($level);
        $context_upper = strtoupper($context);

        $entry = "[{$timestamp}] {$level_upper} [{$context_upper}] {$message}";

        // HIGH PRIORITY SECURITY FIX: Filter sensitive data in production
        if (!empty($data)) {
            $filtered_data = $this->filter_sensitive_data($data);
            $entry .= ' | Data: ' . wp_json_encode($filtered_data);
        }

        $entry .= PHP_EOL;

        return $entry;
    }
    
    /**
     * Get log file path for context
     */
    private function get_log_file($context) {
        $filename = "redco-{$context}.log";
        return $this->log_dir . '/' . $filename;
    }
    
    /**
     * Check if logging should be enabled
     */
    private function should_enable_logging() {
        // Enable logging in development environments
        if (Redco_Config::is_development_environment()) {
            return true;
        }
        
        // Enable logging if explicitly enabled
        if (defined('REDCO_ENABLE_LOGGING') && REDCO_ENABLE_LOGGING) {
            return true;
        }
        
        // Enable logging for critical errors only in production
        return get_option('redco_optimizer_enable_error_logging', false);
    }
    
    /**
     * Get minimum log level
     */
    private function get_min_log_level() {
        if (Redco_Config::is_development_environment()) {
            return self::LOG_DEBUG;
        }
        
        return get_option('redco_optimizer_min_log_level', self::LOG_ERROR);
    }
    
    /**
     * Check if log level should be logged
     */
    private function should_log_level($level) {
        $level_priority = isset(self::$log_priorities[$level]) ? self::$log_priorities[$level] : 7;
        $min_priority = isset(self::$log_priorities[$this->min_log_level]) ? self::$log_priorities[$this->min_log_level] : 3;
        
        return $level_priority <= $min_priority;
    }
    
    /**
     * Rotate log file if needed
     */
    private function rotate_log_if_needed($log_file) {
        if (!file_exists($log_file)) {
            return;
        }
        
        if (filesize($log_file) < self::MAX_LOG_SIZE) {
            return;
        }
        
        // Rotate logs
        for ($i = self::MAX_LOG_FILES - 1; $i > 0; $i--) {
            $old_file = $log_file . '.' . $i;
            $new_file = $log_file . '.' . ($i + 1);
            
            if (file_exists($old_file)) {
                if ($i === self::MAX_LOG_FILES - 1) {
                    unlink($old_file); // Delete oldest log
                } else {
                    rename($old_file, $new_file);
                }
            }
        }
        
        // Move current log to .1
        rename($log_file, $log_file . '.1');
    }
    
    /**
     * Create .htaccess file for log directory
     */
    private function create_htaccess() {
        $htaccess_file = $this->log_dir . '/.htaccess';
        
        if (file_exists($htaccess_file)) {
            return;
        }
        
        $htaccess_content = "# Redco Optimizer Log Directory\n";
        $htaccess_content .= "Options -Indexes\n";
        $htaccess_content .= "deny from all\n";
        
        file_put_contents($htaccess_file, $htaccess_content);
    }
    
    /**
     * Get recent log entries
     * 
     * @param string $context Log context
     * @param int $limit Number of entries to retrieve
     * @return array Log entries
     */
    public static function get_recent_logs($context = self::CONTEXT_GENERAL, $limit = 50) {
        $instance = self::get_instance();
        $log_file = $instance->get_log_file($context);
        
        if (!file_exists($log_file)) {
            return array();
        }
        
        $lines = file($log_file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        if (!$lines) {
            return array();
        }
        
        // Get last N lines
        $recent_lines = array_slice($lines, -$limit);
        
        $entries = array();
        foreach ($recent_lines as $line) {
            $entries[] = $instance->parse_log_entry($line);
        }
        
        return array_reverse($entries); // Most recent first
    }
    
    /**
     * Parse log entry
     */
    private function parse_log_entry($line) {
        $pattern = '/^\[([^\]]+)\] (\w+) \[([^\]]+)\] (.+)$/';
        
        if (preg_match($pattern, $line, $matches)) {
            return array(
                'timestamp' => $matches[1],
                'level' => strtolower($matches[2]),
                'context' => strtolower($matches[3]),
                'message' => $matches[4]
            );
        }
        
        return array(
            'timestamp' => '',
            'level' => 'unknown',
            'context' => 'unknown',
            'message' => $line
        );
    }
    
    /**
     * Clear logs for a context
     * 
     * @param string $context Log context
     * @return bool Success
     */
    public static function clear_logs($context = null) {
        $instance = self::get_instance();
        
        if ($context) {
            $log_file = $instance->get_log_file($context);
            return file_exists($log_file) ? unlink($log_file) : true;
        }
        
        // Clear all logs
        $log_files = glob($instance->log_dir . '/redco-*.log*');
        $success = true;
        
        foreach ($log_files as $file) {
            if (!unlink($file)) {
                $success = false;
            }
        }
        
        return $success;
    }
    
    /**
     * Get log file size
     * 
     * @param string $context Log context
     * @return int File size in bytes
     */
    public static function get_log_size($context = null) {
        $instance = self::get_instance();
        
        if ($context) {
            $log_file = $instance->get_log_file($context);
            return file_exists($log_file) ? filesize($log_file) : 0;
        }
        
        // Get total size of all logs
        $log_files = glob($instance->log_dir . '/redco-*.log*');
        $total_size = 0;
        
        foreach ($log_files as $file) {
            $total_size += filesize($file);
        }
        
        return $total_size;
    }

    /**
     * SECURITY FIX: Enhance security logging data
     *
     * @param array $data Original data
     * @return array Enhanced data with security context
     */
    private function enhance_security_data($data) {
        if (!is_array($data)) {
            $data = array();
        }

        // Add security-relevant information
        $security_data = array(
            'user_id' => get_current_user_id(),
            'user_ip' => $this->get_client_ip(),
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? sanitize_text_field($_SERVER['HTTP_USER_AGENT']) : '',
            'request_uri' => isset($_SERVER['REQUEST_URI']) ? sanitize_text_field($_SERVER['REQUEST_URI']) : '',
            'request_method' => isset($_SERVER['REQUEST_METHOD']) ? sanitize_text_field($_SERVER['REQUEST_METHOD']) : '',
            'timestamp' => time(),
            'session_id' => session_id() ?: 'none'
        );

        // Merge with existing data
        return array_merge($data, $security_data);
    }

    /**
     * SECURITY FIX: Get client IP address safely
     *
     * @return string Client IP address
     */
    private function get_client_ip() {
        $ip_keys = array(
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard
        );

        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'unknown';
    }

    /**
     * HIGH PRIORITY SECURITY FIX: Filter sensitive data from logs
     *
     * @param array $data Data to filter
     * @return array Filtered data
     */
    private function filter_sensitive_data($data) {
        if (!is_array($data)) {
            return $data;
        }

        // Define sensitive keys that should be filtered
        $sensitive_keys = array(
            'password', 'pass', 'pwd', 'secret', 'key', 'token', 'auth',
            'api_key', 'private_key', 'access_token', 'refresh_token',
            'nonce', 'csrf', 'session', 'cookie', 'credit_card', 'ssn',
            'social_security', 'bank_account', 'routing_number'
        );

        // Check if we're in production environment
        $is_production = !defined('WP_DEBUG') || !WP_DEBUG;

        $filtered = array();

        foreach ($data as $key => $value) {
            $key_lower = strtolower($key);
            $is_sensitive = false;

            // Check if key contains sensitive information
            foreach ($sensitive_keys as $sensitive_key) {
                if (strpos($key_lower, $sensitive_key) !== false) {
                    $is_sensitive = true;
                    break;
                }
            }

            if ($is_sensitive) {
                if ($is_production) {
                    // In production, completely hide sensitive data
                    $filtered[$key] = '[REDACTED]';
                } else {
                    // In development, partially mask sensitive data
                    if (is_string($value) && strlen($value) > 4) {
                        $filtered[$key] = substr($value, 0, 2) . str_repeat('*', strlen($value) - 4) . substr($value, -2);
                    } else {
                        $filtered[$key] = '[MASKED]';
                    }
                }
            } else if (is_array($value)) {
                // Recursively filter nested arrays
                $filtered[$key] = $this->filter_sensitive_data($value);
            } else {
                // Non-sensitive data
                $filtered[$key] = $value;
            }
        }

        return $filtered;
    }
}
