<?php
/**
 * Complete reset of the diagnostic autofix system to clean state
 * This script will:
 * 1. Revert all applied fixes
 * 2. Clean backup directories
 * 3. Clear database records
 * 4. Reset system state
 */

require_once('d:/xampp/htdocs/wordpress/wp-config.php');
require_once('modules/diagnostic-autofix/class-diagnostic-autofix-engine.php');
require_once('modules/diagnostic-autofix/class-diagnostic-autofix.php');

/**
 * Helper function to recursively delete directories
 */
function delete_directory_recursive($dir_path) {
    if (!is_dir($dir_path)) {
        return false;
    }

    try {
        $files = array_diff(scandir($dir_path), array('.', '..'));

        foreach ($files as $file) {
            $file_path = $dir_path . DIRECTORY_SEPARATOR . $file;

            if (is_dir($file_path)) {
                delete_directory_recursive($file_path);
            } else {
                unlink($file_path);
            }
        }

        return rmdir($dir_path);
    } catch (Exception $e) {
        return false;
    }
}

echo "=== DIAGNOSTIC AUTOFIX SYSTEM RESET ===\n";
echo "This will completely reset the diagnostic autofix system to a clean state.\n";
echo "All fixes will be reverted and all backup data will be removed.\n\n";

$reset_results = array(
    'fixes_reverted' => 0,
    'backups_removed' => 0,
    'database_records_cleared' => 0,
    'errors' => array()
);

// Step 1: Revert all applied fixes
echo "STEP 1: REVERTING ALL APPLIED FIXES\n";
echo "=====================================\n";

try {
    // Get fix history to identify what needs to be reverted
    $fix_history = get_option('redco_diagnostic_fix_history', array());
    echo "Found " . count($fix_history) . " fix sessions to potentially revert.\n";
    
    if (!empty($fix_history)) {
        $engine = new Redco_Diagnostic_AutoFix_Engine();
        
        foreach ($fix_history as $session) {
            if (isset($session['rollback_id']) && !empty($session['rollback_id'])) {
                $rollback_id = $session['rollback_id'];
                echo "Attempting to revert session with backup: $rollback_id\n";
                
                try {
                    $rollback_result = $engine->rollback_fixes($rollback_id);
                    if ($rollback_result['success']) {
                        echo "  ✅ Successfully reverted fixes for: $rollback_id\n";
                        $reset_results['fixes_reverted']++;
                    } else {
                        echo "  ⚠️  Failed to revert fixes for: $rollback_id\n";
                        echo "     Reason: " . $rollback_result['message'] . "\n";
                        $reset_results['errors'][] = "Failed to revert $rollback_id: " . $rollback_result['message'];
                    }
                } catch (Exception $e) {
                    echo "  ❌ Exception reverting $rollback_id: " . $e->getMessage() . "\n";
                    $reset_results['errors'][] = "Exception reverting $rollback_id: " . $e->getMessage();
                }
            }
        }
    } else {
        echo "No fix sessions found to revert.\n";
    }
    
    // Manual reversion of common fixes that might not be in history
    echo "\nPerforming manual reversion of common fixes:\n";
    
    // Revert .htaccess security headers
    $htaccess_file = ABSPATH . '.htaccess';
    if (file_exists($htaccess_file)) {
        $htaccess_content = file_get_contents($htaccess_file);
        $original_content = $htaccess_content;
        
        // Remove security headers added by the plugin
        $security_patterns = array(
            '/# BEGIN REDCO Security Headers.*?# END REDCO Security Headers\s*/s',
            '/# Security Headers - Added by Redco Optimizer.*?# End Security Headers\s*/s',
            '/Header always set X-Content-Type-Options.*?\n/i',
            '/Header always set X-Frame-Options.*?\n/i',
            '/Header always set X-XSS-Protection.*?\n/i',
            '/Header always set Referrer-Policy.*?\n/i',
            '/Header always set Strict-Transport-Security.*?\n/i'
        );
        
        foreach ($security_patterns as $pattern) {
            $htaccess_content = preg_replace($pattern, '', $htaccess_content);
        }
        
        if ($htaccess_content !== $original_content) {
            if (file_put_contents($htaccess_file, $htaccess_content, LOCK_EX) !== false) {
                echo "  ✅ Removed security headers from .htaccess\n";
            } else {
                echo "  ❌ Failed to update .htaccess file\n";
                $reset_results['errors'][] = "Failed to update .htaccess file";
            }
        } else {
            echo "  ℹ️  No security headers found in .htaccess\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error during fix reversion: " . $e->getMessage() . "\n";
    $reset_results['errors'][] = "Fix reversion error: " . $e->getMessage();
}

// Step 2: Clean backup directories
echo "\nSTEP 2: CLEANING BACKUP DIRECTORIES\n";
echo "====================================\n";

try {
    // UNIFIED: Use centralized backup directory plus legacy directories
    require_once('includes/helpers.php');
    $backup_directories = array(
        redco_get_unified_backup_dir(), // Primary unified directory
        'D:/xampp/htdocs/wordpress/wp-content/uploads/redco-cachediagnostic-backups/', // Legacy
        'D:/xampp/htdocs/wordpress/wp-content/uploads/redco-diagnostic/backups/' // Legacy
    );
    
    // Add engine backup directory
    try {
        $engine = new Redco_Diagnostic_AutoFix_Engine();
        $engine_reflection = new ReflectionClass($engine);
        $backup_dir_property = $engine_reflection->getProperty('backup_dir');
        $backup_dir_property->setAccessible(true);
        $engine_backup_dir = $backup_dir_property->getValue($engine);
        if (!in_array($engine_backup_dir, $backup_directories)) {
            $backup_directories[] = $engine_backup_dir;
        }
    } catch (Exception $e) {
        echo "Could not get engine backup directory: " . $e->getMessage() . "\n";
    }
    
    foreach ($backup_directories as $backup_dir) {
        if (is_dir($backup_dir)) {
            echo "Cleaning backup directory: $backup_dir\n";
            
            $backups = glob($backup_dir . 'backup_*');
            foreach ($backups as $backup_path) {
                if (is_dir($backup_path)) {
                    echo "  Removing backup: " . basename($backup_path) . "\n";
                    if (delete_directory_recursive($backup_path)) {
                        $reset_results['backups_removed']++;
                    } else {
                        echo "    ❌ Failed to remove backup directory\n";
                        $reset_results['errors'][] = "Failed to remove backup: " . basename($backup_path);
                    }
                }
            }
            
            // Remove any remaining files (but keep .htaccess for security)
            $remaining_files = glob($backup_dir . '*');
            foreach ($remaining_files as $file) {
                if (basename($file) !== '.htaccess' && is_file($file)) {
                    unlink($file);
                    echo "  Removed file: " . basename($file) . "\n";
                }
            }
            
            echo "  ✅ Backup directory cleaned: $backup_dir\n";
        } else {
            echo "Backup directory does not exist: $backup_dir\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error during backup cleanup: " . $e->getMessage() . "\n";
    $reset_results['errors'][] = "Backup cleanup error: " . $e->getMessage();
}

// Step 3: Clear database records
echo "\nSTEP 3: CLEARING DATABASE RECORDS\n";
echo "==================================\n";

try {
    // List of all diagnostic autofix related options
    $diagnostic_options = array(
        'redco_diagnostic_fix_history',
        'redco_diagnostic_results',
        'redco_diagnostic_stats',
        'redco_fixed_issues',
        'redco_recent_fixes',
        'redco_scan_progress',
        'redco_last_scan_time',
        'redco_diagnostic_cache',
        'redco_autofix_settings',
        'redco_backup_settings'
    );
    
    foreach ($diagnostic_options as $option) {
        if (get_option($option) !== false) {
            delete_option($option);
            echo "  ✅ Cleared option: $option\n";
            $reset_results['database_records_cleared']++;
        } else {
            echo "  ℹ️  Option not found: $option\n";
        }
    }
    
    // Clear all transients related to diagnostic autofix
    global $wpdb;
    $transients = $wpdb->get_results(
        "SELECT option_name FROM {$wpdb->options} 
         WHERE option_name LIKE '_transient_redco_%' 
         OR option_name LIKE '_transient_timeout_redco_%'"
    );
    
    foreach ($transients as $transient) {
        $transient_name = str_replace(array('_transient_', '_transient_timeout_'), '', $transient->option_name);
        delete_transient($transient_name);
        echo "  ✅ Cleared transient: $transient_name\n";
        $reset_results['database_records_cleared']++;
    }
    
    // Clear WordPress caches
    wp_cache_flush();
    echo "  ✅ WordPress caches flushed\n";
    
} catch (Exception $e) {
    echo "❌ Error during database cleanup: " . $e->getMessage() . "\n";
    $reset_results['errors'][] = "Database cleanup error: " . $e->getMessage();
}

// Step 4: Reset system state
echo "\nSTEP 4: RESETTING SYSTEM STATE\n";
echo "===============================\n";

try {
    // Verify clean state
    $remaining_fix_history = get_option('redco_diagnostic_fix_history', array());
    $remaining_results = get_option('redco_diagnostic_results');
    
    if (empty($remaining_fix_history)) {
        echo "  ✅ Fix history is clean\n";
    } else {
        echo "  ⚠️  Fix history still contains " . count($remaining_fix_history) . " entries\n";
    }
    
    if ($remaining_results === false) {
        echo "  ✅ Diagnostic results are clean\n";
    } else {
        echo "  ⚠️  Diagnostic results still exist\n";
    }
    
    // Test backup directory initialization
    try {
        $engine = new Redco_Diagnostic_AutoFix_Engine();
        echo "  ✅ Engine can be initialized\n";
    } catch (Exception $e) {
        echo "  ❌ Engine initialization failed: " . $e->getMessage() . "\n";
        $reset_results['errors'][] = "Engine initialization failed: " . $e->getMessage();
    }
    
} catch (Exception $e) {
    echo "❌ Error during state verification: " . $e->getMessage() . "\n";
    $reset_results['errors'][] = "State verification error: " . $e->getMessage();
}

// Final summary
echo "\n=== RESET SUMMARY ===\n";
echo "Fixes reverted: " . $reset_results['fixes_reverted'] . "\n";
echo "Backups removed: " . $reset_results['backups_removed'] . "\n";
echo "Database records cleared: " . $reset_results['database_records_cleared'] . "\n";
echo "Errors encountered: " . count($reset_results['errors']) . "\n";

if (!empty($reset_results['errors'])) {
    echo "\nErrors:\n";
    foreach ($reset_results['errors'] as $error) {
        echo "  - $error\n";
    }
}

if (empty($reset_results['errors'])) {
    echo "\n🎉 RESET COMPLETE: SYSTEM IS NOW IN CLEAN STATE\n";
    echo "The diagnostic autofix system has been completely reset.\n";
    echo "You can now perform fresh testing with a clean environment.\n";
} else {
    echo "\n⚠️  RESET COMPLETED WITH SOME ISSUES\n";
    echo "Most components have been reset, but some errors occurred.\n";
    echo "Please review the errors above and address them manually if needed.\n";
}

echo "\n=== NEXT STEPS FOR TESTING ===\n";
echo "1. Refresh the diagnostic autofix page\n";
echo "2. Run a new diagnostic scan\n";
echo "3. Apply some fixes to test backup creation\n";
echo "4. Test rollback functionality\n";
echo "5. Verify session mapping works correctly\n";

echo "\n=== DIAGNOSTIC AUTOFIX SYSTEM RESET COMPLETE ===\n";
