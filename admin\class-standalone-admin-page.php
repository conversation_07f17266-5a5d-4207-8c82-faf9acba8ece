<?php
/**
 * Standalone Admin Page for Redco Diagnostic & Auto-Fix
 * 
 * Unified dashboard interface for all diagnostic and monitoring features (standalone version)
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Standalone_Admin_Page {
    
    private $diagnostic_engine;
    private $tiered_system;
    private $realtime_monitor;
    private $core_web_vitals;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->diagnostic_engine = new Redco_Standalone_Diagnostic_Engine();
        $this->tiered_system = new Redco_Standalone_Tiered_Fix_System();
        $this->realtime_monitor = new Redco_Standalone_Realtime_Monitor();
        $this->core_web_vitals = new Redco_Standalone_Core_Web_Vitals();
    }
    
    /**
     * Render the main admin page
     */
    public function render() {
        // Get current data
        $scan_stats = $this->diagnostic_engine->get_scan_statistics();
        $tier_stats = $this->tiered_system->get_tier_statistics();
        $monitoring_stats = $this->realtime_monitor->get_monitoring_statistics();
        $vitals_stats = $this->core_web_vitals->get_vitals_statistics();
        $current_vitals = $this->core_web_vitals->get_current_vitals();
        
        ?>
        <div class="wrap redco-diagnostic-admin">
            <h1 class="wp-heading-inline">
                <span class="dashicons dashicons-search"></span>
                <?php _e('Redco Diagnostic & Auto-Fix', 'redco-diagnostic-autofix'); ?>
            </h1>
            
            <div class="redco-diagnostic-header">
                <div class="redco-diagnostic-nav">
                    <button class="nav-tab nav-tab-active" data-tab="dashboard">
                        <span class="dashicons dashicons-dashboard"></span>
                        <?php _e('Dashboard', 'redco-diagnostic-autofix'); ?>
                    </button>
                    <button class="nav-tab" data-tab="diagnostic">
                        <span class="dashicons dashicons-search"></span>
                        <?php _e('Diagnostic Scan', 'redco-diagnostic-autofix'); ?>
                    </button>
                    <button class="nav-tab" data-tab="monitoring">
                        <span class="dashicons dashicons-chart-line"></span>
                        <?php _e('Real-time Monitoring', 'redco-diagnostic-autofix'); ?>
                    </button>
                    <button class="nav-tab" data-tab="vitals">
                        <span class="dashicons dashicons-performance"></span>
                        <?php _e('Core Web Vitals', 'redco-diagnostic-autofix'); ?>
                    </button>
                </div>
            </div>
            
            <!-- Dashboard Tab -->
            <div id="tab-dashboard" class="redco-tab-content active">
                <?php $this->render_dashboard_tab($scan_stats, $tier_stats, $monitoring_stats, $vitals_stats, $current_vitals); ?>
            </div>
            
            <!-- Diagnostic Tab -->
            <div id="tab-diagnostic" class="redco-tab-content">
                <?php $this->render_diagnostic_tab(); ?>
            </div>
            
            <!-- Monitoring Tab -->
            <div id="tab-monitoring" class="redco-tab-content">
                <?php $this->render_monitoring_tab($monitoring_stats); ?>
            </div>
            
            <!-- Core Web Vitals Tab -->
            <div id="tab-vitals" class="redco-tab-content">
                <?php $this->render_vitals_tab($vitals_stats, $current_vitals); ?>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render dashboard tab
     */
    private function render_dashboard_tab($scan_stats, $tier_stats, $monitoring_stats, $vitals_stats, $current_vitals) {
        ?>
        <div class="redco-dashboard">
            <!-- Overview Cards -->
            <div class="redco-overview-cards">
                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><span class="dashicons dashicons-search"></span> <?php _e('Diagnostic Overview', 'redco-diagnostic-autofix'); ?></h3>
                    </div>
                    <div class="redco-card-body">
                        <div class="redco-stat-grid">
                            <div class="redco-stat">
                                <div class="redco-stat-value"><?php echo number_format($scan_stats['total_scans'] ?? 0); ?></div>
                                <div class="redco-stat-label"><?php _e('Total Scans', 'redco-diagnostic-autofix'); ?></div>
                            </div>
                            <div class="redco-stat">
                                <div class="redco-stat-value"><?php echo number_format($scan_stats['total_issues_found'] ?? 0); ?></div>
                                <div class="redco-stat-label"><?php _e('Issues Found', 'redco-diagnostic-autofix'); ?></div>
                            </div>
                            <div class="redco-stat">
                                <div class="redco-stat-value"><?php echo number_format($scan_stats['total_critical_issues'] ?? 0); ?></div>
                                <div class="redco-stat-label"><?php _e('Critical Issues', 'redco-diagnostic-autofix'); ?></div>
                            </div>
                            <div class="redco-stat">
                                <div class="redco-stat-value"><?php echo round($scan_stats['avg_health_score'] ?? 0); ?>%</div>
                                <div class="redco-stat-label"><?php _e('Avg Health Score', 'redco-diagnostic-autofix'); ?></div>
                            </div>
                        </div>
                        <div class="redco-card-actions">
                            <button class="button button-primary" id="run-quick-scan">
                                <span class="dashicons dashicons-search"></span>
                                <?php _e('Run Quick Scan', 'redco-diagnostic-autofix'); ?>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><span class="dashicons dashicons-shield-alt"></span> <?php _e('Tiered Fixes', 'redco-diagnostic-autofix'); ?></h3>
                    </div>
                    <div class="redco-card-body">
                        <div class="redco-tier-overview">
                            <div class="redco-tier-item safe">
                                <div class="redco-tier-count"><?php echo $tier_stats['safe'] ?? 0; ?></div>
                                <div class="redco-tier-label"><?php _e('Safe Fixes', 'redco-diagnostic-autofix'); ?></div>
                            </div>
                            <div class="redco-tier-item moderate">
                                <div class="redco-tier-count"><?php echo $tier_stats['moderate'] ?? 0; ?></div>
                                <div class="redco-tier-label"><?php _e('Moderate Fixes', 'redco-diagnostic-autofix'); ?></div>
                            </div>
                            <div class="redco-tier-item advanced">
                                <div class="redco-tier-count"><?php echo $tier_stats['advanced'] ?? 0; ?></div>
                                <div class="redco-tier-label"><?php _e('Advanced Fixes', 'redco-diagnostic-autofix'); ?></div>
                            </div>
                        </div>
                        <div class="redco-card-actions">
                            <button class="button" id="apply-safe-fixes">
                                <?php _e('Apply Safe Fixes', 'redco-diagnostic-autofix'); ?>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><span class="dashicons dashicons-performance"></span> <?php _e('Core Web Vitals', 'redco-diagnostic-autofix'); ?></h3>
                    </div>
                    <div class="redco-card-body">
                        <div class="redco-vitals-overview">
                            <div class="redco-vital-item">
                                <div class="redco-vital-value <?php echo $this->get_vital_status($current_vitals['lcp'] ?? 0, 'lcp'); ?>">
                                    <?php echo round($current_vitals['lcp'] ?? 0, 2); ?>s
                                </div>
                                <div class="redco-vital-label">LCP</div>
                            </div>
                            <div class="redco-vital-item">
                                <div class="redco-vital-value <?php echo $this->get_vital_status($current_vitals['fid'] ?? 0, 'fid'); ?>">
                                    <?php echo round($current_vitals['fid'] ?? 0); ?>ms
                                </div>
                                <div class="redco-vital-label">FID</div>
                            </div>
                            <div class="redco-vital-item">
                                <div class="redco-vital-value <?php echo $this->get_vital_status($current_vitals['cls'] ?? 0, 'cls'); ?>">
                                    <?php echo round($current_vitals['cls'] ?? 0, 3); ?>
                                </div>
                                <div class="redco-vital-label">CLS</div>
                            </div>
                        </div>
                        <div class="redco-overall-score">
                            <div class="redco-score-circle">
                                <div class="redco-score-value"><?php echo $current_vitals['scores']['overall'] ?? 0; ?></div>
                            </div>
                            <div class="redco-score-label"><?php _e('Overall Score', 'redco-diagnostic-autofix'); ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Get vital status class
     */
    private function get_vital_status($value, $metric) {
        $thresholds = array(
            'lcp' => array('good' => 2.5, 'needs_improvement' => 4.0),
            'fid' => array('good' => 100, 'needs_improvement' => 300),
            'cls' => array('good' => 0.1, 'needs_improvement' => 0.25)
        );
        
        if (!isset($thresholds[$metric])) {
            return 'unknown';
        }
        
        if ($value <= $thresholds[$metric]['good']) {
            return 'good';
        } elseif ($value <= $thresholds[$metric]['needs_improvement']) {
            return 'needs-improvement';
        } else {
            return 'poor';
        }
    }
    
    /**
     * Render diagnostic tab
     */
    private function render_diagnostic_tab() {
        ?>
        <div class="redco-diagnostic-scan">
            <div class="redco-scan-controls">
                <div class="redco-card">
                    <div class="redco-card-header">
                        <h3><?php _e('Diagnostic Scan', 'redco-diagnostic-autofix'); ?></h3>
                    </div>
                    <div class="redco-card-body">
                        <div class="redco-scan-options">
                            <label>
                                <input type="radio" name="scan_type" value="quick" checked>
                                <strong><?php _e('Quick Scan', 'redco-diagnostic-autofix'); ?></strong>
                                <span class="description"><?php _e('Essential issues only (2-3 minutes)', 'redco-diagnostic-autofix'); ?></span>
                            </label>
                            <label>
                                <input type="radio" name="scan_type" value="performance">
                                <strong><?php _e('Performance Scan', 'redco-diagnostic-autofix'); ?></strong>
                                <span class="description"><?php _e('Focus on performance issues (5-7 minutes)', 'redco-diagnostic-autofix'); ?></span>
                            </label>
                            <label>
                                <input type="radio" name="scan_type" value="security">
                                <strong><?php _e('Security Scan', 'redco-diagnostic-autofix'); ?></strong>
                                <span class="description"><?php _e('Focus on security vulnerabilities (3-5 minutes)', 'redco-diagnostic-autofix'); ?></span>
                            </label>
                            <label>
                                <input type="radio" name="scan_type" value="comprehensive">
                                <strong><?php _e('Comprehensive Scan', 'redco-diagnostic-autofix'); ?></strong>
                                <span class="description"><?php _e('Complete analysis (10-15 minutes)', 'redco-diagnostic-autofix'); ?></span>
                            </label>
                        </div>
                        <div class="redco-scan-actions">
                            <button class="button button-primary button-large" id="start-diagnostic-scan">
                                <span class="dashicons dashicons-search"></span>
                                <?php _e('Start Scan', 'redco-diagnostic-autofix'); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="redco-scan-progress" id="scan-progress" style="display: none;">
                <div class="redco-card">
                    <div class="redco-card-body">
                        <div class="redco-progress-bar">
                            <div class="redco-progress-fill" style="width: 0%"></div>
                        </div>
                        <div class="redco-progress-text"><?php _e('Initializing scan...', 'redco-diagnostic-autofix'); ?></div>
                    </div>
                </div>
            </div>
            
            <div class="redco-scan-results" id="scan-results" style="display: none;">
                <!-- Results will be populated by JavaScript -->
            </div>
        </div>
        <?php
    }
    
    /**
     * Render monitoring tab
     */
    private function render_monitoring_tab($monitoring_stats) {
        ?>
        <div class="redco-monitoring">
            <div class="redco-card">
                <div class="redco-card-header">
                    <h3><?php _e('Real-time Performance Monitoring', 'redco-diagnostic-autofix'); ?></h3>
                </div>
                <div class="redco-card-body">
                    <div class="redco-stat-grid">
                        <div class="redco-stat">
                            <div class="redco-stat-value"><?php echo number_format($monitoring_stats['total_measurements'] ?? 0); ?></div>
                            <div class="redco-stat-label"><?php _e('Total Measurements', 'redco-diagnostic-autofix'); ?></div>
                        </div>
                        <div class="redco-stat">
                            <div class="redco-stat-value"><?php echo round($monitoring_stats['avg_page_load_time'] ?? 0, 2); ?>s</div>
                            <div class="redco-stat-label"><?php _e('Avg Page Load Time', 'redco-diagnostic-autofix'); ?></div>
                        </div>
                        <div class="redco-stat">
                            <div class="redco-stat-value"><?php echo redco_diagnostic_format_bytes($monitoring_stats['avg_memory_usage'] ?? 0); ?></div>
                            <div class="redco-stat-label"><?php _e('Avg Memory Usage', 'redco-diagnostic-autofix'); ?></div>
                        </div>
                        <div class="redco-stat">
                            <div class="redco-stat-value"><?php echo round($monitoring_stats['avg_database_queries'] ?? 0); ?></div>
                            <div class="redco-stat-label"><?php _e('Avg DB Queries', 'redco-diagnostic-autofix'); ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Render vitals tab
     */
    private function render_vitals_tab($vitals_stats, $current_vitals) {
        ?>
        <div class="redco-vitals">
            <div class="redco-card">
                <div class="redco-card-header">
                    <h3><?php _e('Core Web Vitals Performance', 'redco-diagnostic-autofix'); ?></h3>
                </div>
                <div class="redco-card-body">
                    <div class="redco-stat-grid">
                        <div class="redco-stat">
                            <div class="redco-stat-value"><?php echo number_format($vitals_stats['total_measurements'] ?? 0); ?></div>
                            <div class="redco-stat-label"><?php _e('Total Measurements', 'redco-diagnostic-autofix'); ?></div>
                        </div>
                        <div class="redco-stat">
                            <div class="redco-stat-value"><?php echo round($vitals_stats['avg_lcp'] ?? 0, 2); ?>s</div>
                            <div class="redco-stat-label"><?php _e('Avg LCP', 'redco-diagnostic-autofix'); ?></div>
                        </div>
                        <div class="redco-stat">
                            <div class="redco-stat-value"><?php echo round($vitals_stats['avg_fid'] ?? 0); ?>ms</div>
                            <div class="redco-stat-label"><?php _e('Avg FID', 'redco-diagnostic-autofix'); ?></div>
                        </div>
                        <div class="redco-stat">
                            <div class="redco-stat-value"><?php echo round($vitals_stats['avg_cls'] ?? 0, 3); ?></div>
                            <div class="redco-stat-label"><?php _e('Avg CLS', 'redco-diagnostic-autofix'); ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
}
