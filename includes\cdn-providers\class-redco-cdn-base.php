<?php
/**
 * Base CDN Provider Class - Phase 4 Implementation
 * 
 * Abstract base class for all CDN providers.
 * Defines common interface and shared functionality.
 * 
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

abstract class Redco_CDN_Base {

    /**
     * Provider name
     */
    protected $provider_name = '';

    /**
     * Provider configuration
     */
    protected $config = array();

    /**
     * Default capabilities
     */
    protected $capabilities = array(
        'url_rewriting' => true,
        'cache_purging' => false,
        'real_time_analytics' => false,
        'ssl_support' => true,
        'image_optimization' => false,
        'video_streaming' => false,
        'edge_computing' => false,
        'security_features' => false,
        'api_integration' => false
    );

    /**
     * Constructor
     */
    public function __construct() {
        $this->load_configuration();
    }

    /**
     * Load provider configuration
     */
    protected function load_configuration() {
        $this->config = redco_get_module_option('cdn-integration', $this->provider_name . '_config', array());
    }

    /**
     * Save provider configuration
     */
    protected function save_configuration($config) {
        $this->config = $config;
        redco_update_module_option('cdn-integration', $this->provider_name . '_config', $config);
    }

    /**
     * Get provider capabilities
     */
    public function get_capabilities() {
        return $this->capabilities;
    }

    /**
     * Check if provider is configured
     */
    public function is_configured($config = null) {
        $config = $config ?: $this->config;
        return !empty($config) && $this->validate_configuration($config);
    }

    /**
     * Get provider status
     */
    public function get_status() {
        if (!$this->is_configured()) {
            return 'not_configured';
        }

        if ($this->test_connection()) {
            return 'active';
        }

        return 'error';
    }

    /**
     * Test provider connection
     */
    public function test_connection() {
        // Default implementation - override in specific providers
        return true;
    }

    /**
     * Get configuration fields for admin interface
     */
    abstract public function get_configuration_fields();

    /**
     * Validate configuration
     */
    abstract public function validate_configuration($config);

    /**
     * Rewrite URL for CDN
     */
    abstract public function rewrite_url($url);

    /**
     * Test performance
     */
    public function test_performance($test_urls = array()) {
        if (empty($test_urls)) {
            $test_urls = $this->get_default_test_urls();
        }

        $results = array();
        
        foreach ($test_urls as $url) {
            $start_time = microtime(true);
            
            $response = wp_remote_head($this->rewrite_url($url), array(
                'timeout' => 10,
                'user-agent' => 'RedCo Optimizer CDN Test'
            ));
            
            $end_time = microtime(true);
            $response_time = ($end_time - $start_time) * 1000; // Convert to milliseconds

            if (!is_wp_error($response)) {
                $results[] = array(
                    'url' => $url,
                    'cdn_url' => $this->rewrite_url($url),
                    'response_time' => round($response_time, 2),
                    'status_code' => wp_remote_retrieve_response_code($response),
                    'headers' => wp_remote_retrieve_headers($response)
                );
            } else {
                $results[] = array(
                    'url' => $url,
                    'cdn_url' => $this->rewrite_url($url),
                    'error' => $response->get_error_message()
                );
            }
        }

        return $results;
    }

    /**
     * Get default test URLs
     */
    protected function get_default_test_urls() {
        $site_url = get_site_url();
        
        return array(
            $site_url . '/wp-includes/css/admin-bar.min.css',
            $site_url . '/wp-includes/js/jquery/jquery.min.js',
            $site_url . '/wp-content/themes/' . get_template() . '/style.css'
        );
    }

    /**
     * Purge cache (if supported)
     */
    public function purge_cache($urls = array()) {
        if (!$this->capabilities['cache_purging']) {
            return array('success' => false, 'message' => 'Cache purging not supported');
        }

        // Override in specific providers
        return array('success' => false, 'message' => 'Not implemented');
    }

    /**
     * Get analytics data (if supported)
     */
    public function get_analytics($period = '24h') {
        if (!$this->capabilities['real_time_analytics']) {
            return array('success' => false, 'message' => 'Analytics not supported');
        }

        // Override in specific providers
        return array('success' => false, 'message' => 'Not implemented');
    }

    /**
     * Get cache statistics
     */
    public function get_cache_stats() {
        // Default implementation - can be overridden
        return array(
            'cache_hit_ratio' => 0,
            'bandwidth_saved' => 0,
            'requests_served' => 0,
            'data_transferred' => 0
        );
    }

    /**
     * Setup edge caching rules
     */
    public function setup_cache_rules($rules = array()) {
        // Override in specific providers that support cache rules
        return array('success' => false, 'message' => 'Cache rules not supported');
    }

    /**
     * Get edge locations
     */
    public function get_edge_locations() {
        // Override in specific providers
        return array();
    }

    /**
     * Optimize for WordPress
     */
    public function optimize_for_wordpress() {
        $optimization_results = array();

        // Set up common WordPress cache rules
        $wordpress_rules = array(
            'static_assets' => array(
                'pattern' => '*.css,*.js,*.png,*.jpg,*.jpeg,*.gif,*.svg,*.woff,*.woff2,*.ttf,*.eot,*.ico',
                'cache_duration' => '1 year',
                'edge_cache_ttl' => '30 days'
            ),
            'html_pages' => array(
                'pattern' => '*.html,/',
                'cache_duration' => '4 hours',
                'edge_cache_ttl' => '1 hour'
            ),
            'wp_json_api' => array(
                'pattern' => '/wp-json/*',
                'cache_duration' => '5 minutes',
                'edge_cache_ttl' => '1 minute'
            ),
            'wp_admin' => array(
                'pattern' => '/wp-admin/*',
                'cache_duration' => 'no-cache',
                'edge_cache_ttl' => 'no-cache'
            )
        );

        if ($this->capabilities['cache_purging']) {
            $cache_result = $this->setup_cache_rules($wordpress_rules);
            $optimization_results['cache_rules'] = $cache_result;
        }

        // Set up SSL if supported
        if ($this->capabilities['ssl_support']) {
            $ssl_result = $this->setup_ssl();
            $optimization_results['ssl'] = $ssl_result;
        }

        return $optimization_results;
    }

    /**
     * Setup SSL (if supported)
     */
    protected function setup_ssl() {
        // Override in specific providers
        return array('success' => false, 'message' => 'SSL setup not implemented');
    }

    /**
     * Get provider-specific metrics
     */
    public function get_provider_metrics() {
        return array(
            'provider' => $this->provider_name,
            'status' => $this->get_status(),
            'capabilities' => $this->capabilities,
            'cache_stats' => $this->get_cache_stats(),
            'edge_locations' => count($this->get_edge_locations())
        );
    }

    /**
     * Format bytes for display
     */
    protected function format_bytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB', 'PB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Log provider activity
     */
    protected function log($message, $level = 'info') {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log(sprintf('[RedCo CDN %s] %s: %s', $this->provider_name, strtoupper($level), $message));
        }
    }

    /**
     * Make API request (helper for providers with APIs)
     */
    protected function make_api_request($url, $args = array()) {
        $default_args = array(
            'timeout' => 30,
            'user-agent' => 'RedCo Optimizer CDN/' . REDCO_OPTIMIZER_VERSION,
            'headers' => array(
                'Content-Type' => 'application/json'
            )
        );

        $args = wp_parse_args($args, $default_args);
        
        $response = wp_remote_request($url, $args);

        if (is_wp_error($response)) {
            $this->log('API request failed: ' . $response->get_error_message(), 'error');
            return array('success' => false, 'error' => $response->get_error_message());
        }

        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        if ($status_code >= 200 && $status_code < 300) {
            return array('success' => true, 'data' => json_decode($body, true), 'status_code' => $status_code);
        } else {
            $this->log('API request failed with status ' . $status_code . ': ' . $body, 'error');
            return array('success' => false, 'error' => 'HTTP ' . $status_code, 'data' => json_decode($body, true));
        }
    }
}
