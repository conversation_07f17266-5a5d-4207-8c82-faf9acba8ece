=== Redco Optimizer ===
Contributors: redco
Tags: performance, optimization, cache, lazy-load, minify, database, speed
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 1.0.0
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

A modern, scalable, and professional WordPress performance optimization plugin with modular features.

== Description ==

Redco Optimizer is a comprehensive performance optimization plugin designed to make your WordPress website faster and more efficient. With its modular architecture, you can enable only the features you need while keeping your site lightweight.

= Key Features =

**Free Modules:**
* **Page Cache** - Full page caching for faster load times
* **Lazy Load Images** - Load images only when they come into view
* **CSS/JS Minifier** - Minify CSS and JavaScript files
* **Database Cleanup** - Clean up unnecessary database entries
* **Heartbeat Control** - Control WordPress Heartbeat API frequency
* **Emoji Stripper** - Remove WordPress emoji scripts and styles
* **Query String Remover** - Remove version query strings from static resources
* **Asset Version Remover** - Remove version numbers from CSS and JS files
* **Autosave Reducer** - Reduce WordPress autosave frequency

**Premium Modules (Coming Soon):**
* AI-Based Auto Optimizer
* AI Image Upscaler
* Smart WebP Conversion
* CDN Integrations
* WooCommerce Booster
* Preload Crawler
* Role-Based Module Access

= Why Choose Redco Optimizer? =

* **Modular Design** - Enable only the features you need
* **Real Data Integration** - All settings pull actual data from your WordPress site
* **Modern Interface** - Clean, intuitive admin interface with sidebar navigation
* **Performance Focused** - Built with performance best practices
* **Extensible** - Ready for future premium features and add-ons
* **Developer Friendly** - Clean code following WordPress standards

= Technical Features =

* Uses WordPress object cache for page caching
* Intersection Observer API for lazy loading (with fallback)
* Advanced CSS/JS minification
* Intelligent database cleanup with safety measures
* Comprehensive settings for each module
* Real-time statistics and monitoring
* AJAX-powered admin interface

== Installation ==

1. Upload the plugin files to the `/wp-content/plugins/redco-optimizer` directory, or install the plugin through the WordPress plugins screen directly.
2. Activate the plugin through the 'Plugins' screen in WordPress.
3. Use the Redco Optimizer menu item to configure the plugin.
4. Enable the modules you want to use and configure their settings.

== Frequently Asked Questions ==

= Is this plugin compatible with other caching plugins? =

Redco Optimizer's page cache module may conflict with other caching plugins. We recommend using only one caching solution at a time for best results.

= Will this plugin slow down my admin area? =

No, most optimization features are disabled in the admin area to ensure smooth administration experience.

= Can I exclude specific pages from caching? =

Yes, the page cache module allows you to exclude specific pages from being cached.

= Is the plugin compatible with WooCommerce? =

Yes, the plugin includes special handling for WooCommerce pages and can exclude WooCommerce-specific elements when needed.

= How often should I run database cleanup? =

The database cleanup module can be configured to run automatically on a schedule (weekly, monthly, etc.) or run manually when needed.

== Screenshots ==

1. Main dashboard showing module overview and statistics
2. Modular interface with sidebar navigation
3. Page cache settings with real page data
4. Lazy load configuration options
5. Database cleanup with live statistics
6. CSS/JS minifier settings

== Changelog ==

= 1.0.0 =
* Initial release
* Page Cache module with WordPress object cache integration
* Lazy Load Images with Intersection Observer API
* CSS/JS Minifier with intelligent exclusions
* Database Cleanup with safety measures
* Heartbeat Control for admin, editor, and frontend
* Emoji Stripper for cleaner output
* Query String Remover for better caching
* Asset Version Remover for cache optimization
* Autosave Reducer for reduced server load
* Modern admin interface with sidebar navigation
* Real-time statistics and monitoring
* AJAX-powered settings management
* Comprehensive module system
* Premium module placeholders
* Extensible architecture for future features

== Upgrade Notice ==

= 1.0.0 =
Initial release of Redco Optimizer. Install to start optimizing your WordPress site performance.

== Developer Information ==

Redco Optimizer is built with extensibility in mind. Developers can:

* Add custom modules using the plugin's hook system
* Extend existing modules with filters and actions
* Integrate with the licensing system for premium features
* Use the admin UI framework for consistent interfaces

For more information, visit our documentation at https://redco.com/optimizer/docs

== Support ==

For support, please visit our support forum or contact <NAME_EMAIL>

== Privacy Policy ==

Redco Optimizer does not collect or transmit any personal data. All optimization happens locally on your server.

== Credits ==

Developed by the Redco team with love for WordPress performance optimization.
