<?php
/**
 * Fix backup metadata paths after directory migration
 * Updates backup_data.json files to use new directory paths
 */

require_once('d:/xampp/htdocs/wordpress/wp-config.php');

echo "=== FIXING BACKUP METADATA PATHS ===\n";

// UNIFIED: Use centralized backup directory function
require_once('includes/helpers.php');
$backup_dir = redco_get_unified_backup_dir();
$old_path_pattern = 'redco-cachediagnostic-backups';
$new_path_pattern = 'redco-backups';

echo "Backup directory: $backup_dir\n";
echo "Updating paths from: $old_path_pattern\n";
echo "Updating paths to: $new_path_pattern\n";

// Get all backup directories
$backups = glob($backup_dir . 'backup_*');
echo "Found " . count($backups) . " backup directories.\n";

$updated_count = 0;
$failed_count = 0;
$skipped_count = 0;

foreach ($backups as $backup_path) {
    $backup_name = basename($backup_path);
    $metadata_file = $backup_path . '/backup_data.json';
    
    echo "\nProcessing backup: $backup_name\n";
    
    // Check if metadata file exists
    if (!file_exists($metadata_file)) {
        echo "  ⚠️  Metadata file not found. Skipping.\n";
        $skipped_count++;
        continue;
    }
    
    // Read current metadata
    $metadata_content = file_get_contents($metadata_file);
    if ($metadata_content === false) {
        echo "  ❌ Failed to read metadata file. Skipping.\n";
        $failed_count++;
        continue;
    }
    
    // Check if update is needed
    if (strpos($metadata_content, $old_path_pattern) === false) {
        echo "  ✅ Metadata already uses correct paths. Skipping.\n";
        $skipped_count++;
        continue;
    }
    
    // Parse JSON to validate structure
    $metadata = json_decode($metadata_content, true);
    if ($metadata === null) {
        echo "  ❌ Invalid JSON in metadata file. Skipping.\n";
        $failed_count++;
        continue;
    }
    
    echo "  📝 Updating metadata paths...\n";
    
    // Update file paths in metadata
    $updated_content = str_replace($old_path_pattern, $new_path_pattern, $metadata_content);
    
    // Validate the updated JSON
    $updated_metadata = json_decode($updated_content, true);
    if ($updated_metadata === null) {
        echo "  ❌ Updated JSON is invalid. Skipping.\n";
        $failed_count++;
        continue;
    }
    
    // Create backup of original metadata
    $backup_metadata_file = $metadata_file . '.backup-' . time();
    if (!copy($metadata_file, $backup_metadata_file)) {
        echo "  ⚠️  Failed to create metadata backup, but continuing...\n";
    }
    
    // Write updated metadata
    if (file_put_contents($metadata_file, $updated_content, LOCK_EX) !== false) {
        echo "  ✅ Successfully updated metadata paths.\n";
        $updated_count++;
        
        // Verify the update
        $verification_content = file_get_contents($metadata_file);
        if (strpos($verification_content, $old_path_pattern) === false && 
            strpos($verification_content, $new_path_pattern) !== false) {
            echo "  ✅ Update verified.\n";
        } else {
            echo "  ⚠️  Update verification failed.\n";
        }
        
        // Show sample of updated paths
        $sample_metadata = json_decode($verification_content, true);
        if (isset($sample_metadata['files'][0]['backup'])) {
            echo "  📁 Sample updated path: " . $sample_metadata['files'][0]['backup'] . "\n";
        }
        
    } else {
        echo "  ❌ Failed to write updated metadata.\n";
        $failed_count++;
        
        // Restore backup if write failed
        if (file_exists($backup_metadata_file)) {
            copy($backup_metadata_file, $metadata_file);
            unlink($backup_metadata_file);
        }
    }
}

echo "\n=== UPDATE SUMMARY ===\n";
echo "Total backups processed: " . count($backups) . "\n";
echo "Successfully updated: $updated_count\n";
echo "Skipped (no update needed): $skipped_count\n";
echo "Failed: $failed_count\n";

// Test validation after updates
if ($updated_count > 0) {
    echo "\n=== TESTING VALIDATION AFTER UPDATES ===\n";
    
    try {
        require_once('modules/diagnostic-autofix/class-diagnostic-autofix-engine.php');
        $engine = new Redco_Diagnostic_AutoFix_Engine();
        
        // Test validation on first updated backup
        $test_backups = glob($backup_dir . 'backup_*');
        if (!empty($test_backups)) {
            $test_backup = basename($test_backups[0]);
            echo "Testing validation on: $test_backup\n";
            
            // Use reflection to access private validation method
            $reflection = new ReflectionClass($engine);
            $validate_method = $reflection->getMethod('validate_backup_for_rollback');
            $validate_method->setAccessible(true);
            
            $validation_result = $validate_method->invoke($engine, $test_backup);
            
            echo "Validation result: " . ($validation_result['valid'] ? 'VALID' : 'INVALID') . "\n";
            if (!$validation_result['valid']) {
                echo "Validation reason: " . $validation_result['reason'] . "\n";
            } else {
                echo "✅ Backup validation now works correctly!\n";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Error testing validation: " . $e->getMessage() . "\n";
    }
}

echo "\n=== METADATA PATH FIXING COMPLETE ===\n";
echo "Backup metadata has been updated to use the new directory structure.\n";
echo "Rollback operations should now work correctly.\n";
