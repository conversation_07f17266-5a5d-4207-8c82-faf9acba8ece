<?php
/**
 * Check WordPress Error Logs for Rollback Debug Information
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    require_once('../../../wp-config.php');
}

// Security check
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

echo "<h1>📋 WordPress Error Logs - Rollback Debug</h1>\n";

// Find the error log file
$error_log_paths = array(
    ABSPATH . 'wp-content/debug.log',
    ABSPATH . 'debug.log',
    ini_get('error_log'),
    '/tmp/error_log',
    '/var/log/apache2/error.log',
    '/var/log/nginx/error.log'
);

$error_log_file = null;
foreach ($error_log_paths as $path) {
    if (!empty($path) && file_exists($path) && is_readable($path)) {
        $error_log_file = $path;
        break;
    }
}

if (!$error_log_file) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>⚠️ Error Log Not Found</h3>\n";
    echo "<p>Could not locate the WordPress error log file. Checked paths:</p>\n";
    echo "<ul>\n";
    foreach ($error_log_paths as $path) {
        $exists = !empty($path) && file_exists($path) ? 'EXISTS' : 'NOT FOUND';
        $readable = !empty($path) && is_readable($path) ? 'READABLE' : 'NOT READABLE';
        echo "<li><code>{$path}</code> - {$exists}, {$readable}</li>\n";
    }
    echo "</ul>\n";
    echo "<p>Make sure WP_DEBUG and WP_DEBUG_LOG are enabled in wp-config.php:</p>\n";
    echo "<pre>define('WP_DEBUG', true);\ndefine('WP_DEBUG_LOG', true);</pre>\n";
    echo "</div>\n";
    exit;
}

echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>\n";
echo "<h3>✅ Error Log Found</h3>\n";
echo "<p><strong>File:</strong> {$error_log_file}</p>\n";
echo "<p><strong>Size:</strong> " . number_format(filesize($error_log_file)) . " bytes</p>\n";
echo "<p><strong>Last Modified:</strong> " . date('Y-m-d H:i:s', filemtime($error_log_file)) . "</p>\n";
echo "</div>\n";

// Read the last 100 lines of the error log
$lines = array();
$handle = fopen($error_log_file, 'r');
if ($handle) {
    // Read file in reverse to get the most recent entries
    fseek($handle, -1, SEEK_END);
    $pos = ftell($handle);
    $line = '';
    $lines = array();
    
    while ($pos >= 0 && count($lines) < 200) {
        $char = fgetc($handle);
        if ($char === "\n" || $pos === 0) {
            if (!empty(trim($line))) {
                $lines[] = $line;
            }
            $line = '';
        } else {
            $line = $char . $line;
        }
        fseek($handle, --$pos);
    }
    fclose($handle);
    
    // Reverse to get chronological order
    $lines = array_reverse($lines);
}

// Filter for REDCO ROLLBACK entries
$rollback_logs = array();
$recent_logs = array();

foreach ($lines as $line) {
    if (strpos($line, 'REDCO ROLLBACK') !== false) {
        $rollback_logs[] = $line;
    }
    if (strpos($line, 'REDCO') !== false) {
        $recent_logs[] = $line;
    }
}

echo "<h2>🔍 Rollback Debug Logs</h2>\n";

if (!empty($rollback_logs)) {
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>📋 Recent Rollback Logs (" . count($rollback_logs) . " entries)</h3>\n";
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 3px; max-height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px;'>\n";
    
    foreach (array_slice($rollback_logs, -20) as $log) {
        $log = htmlspecialchars($log);
        
        // Highlight important parts
        $log = str_replace('REDCO ROLLBACK:', '<strong style="color: #dc3232;">REDCO ROLLBACK:</strong>', $log);
        $log = str_replace('Match found', '<span style="color: #28a745; font-weight: bold;">Match found</span>', $log);
        $log = str_replace('should_remove: YES', '<span style="color: #28a745; font-weight: bold;">should_remove: YES</span>', $log);
        $log = str_replace('should_remove: NO', '<span style="color: #6c757d;">should_remove: NO</span>', $log);
        $log = str_replace('Database updates', '<span style="color: #007bff; font-weight: bold;">Database updates</span>', $log);
        $log = str_replace('CRITICAL ERROR', '<span style="color: #dc3232; font-weight: bold; background: #f8d7da; padding: 2px 4px;">CRITICAL ERROR</span>', $log);
        $log = str_replace('Database update verified', '<span style="color: #28a745; font-weight: bold;">Database update verified</span>', $log);
        
        echo $log . "<br>\n";
    }
    echo "</div>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>⚠️ No Rollback Logs Found</h3>\n";
    echo "<p>No REDCO ROLLBACK entries found in the error log. This suggests that:</p>\n";
    echo "<ul>\n";
    echo "<li>The rollback process hasn't been executed recently</li>\n";
    echo "<li>The rollback AJAX handler isn't being called</li>\n";
    echo "<li>Error logging is not working properly</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
}

echo "<h2>🔍 Recent REDCO Logs</h2>\n";

if (!empty($recent_logs)) {
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>📋 Recent REDCO Logs (" . count($recent_logs) . " entries)</h3>\n";
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 3px; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;'>\n";
    
    foreach (array_slice($recent_logs, -15) as $log) {
        $log = htmlspecialchars($log);
        
        // Highlight different types of logs
        if (strpos($log, 'ROLLBACK') !== false) {
            $log = '<span style="background: #fff3cd; padding: 1px 3px;">' . $log . '</span>';
        } elseif (strpos($log, 'ERROR') !== false) {
            $log = '<span style="background: #f8d7da; padding: 1px 3px;">' . $log . '</span>';
        } elseif (strpos($log, 'SUCCESS') !== false) {
            $log = '<span style="background: #d4edda; padding: 1px 3px;">' . $log . '</span>';
        }
        
        echo $log . "<br>\n";
    }
    echo "</div>\n";
    echo "</div>\n";
} else {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px;'>\n";
    echo "<h3>⚠️ No REDCO Logs Found</h3>\n";
    echo "<p>No REDCO entries found in the error log.</p>\n";
    echo "</div>\n";
}

echo "<h2>🧪 Test Rollback and Monitor Logs</h2>\n";
echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>\n";
echo "<h3>🎯 Testing Instructions</h3>\n";
echo "<ol>\n";
echo "<li>Go to the <a href='" . admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix') . "' target='_blank'>Diagnostic & Auto-Fix module</a></li>\n";
echo "<li>Click a 'Rollback' button in the Recent Fixes section</li>\n";
echo "<li>Return to this page and <a href='check-error-logs.php'>refresh</a> to see the new logs</li>\n";
echo "<li>Look for REDCO ROLLBACK entries to see what's happening during the rollback process</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<h2>🔧 Additional Tools</h2>\n";
echo "<ul>\n";
echo "<li><a href='test-ajax-rollback.php'>🧪 Test AJAX Rollback Endpoint</a></li>\n";
echo "<li><a href='debug-rollback-process.php'>🐛 Debug Rollback Process</a></li>\n";
echo "<li><a href='check-fix-history.php'>📋 Check Fix History State</a></li>\n";
echo "</ul>\n";

echo "<p><a href='check-error-logs.php' style='background: #666; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔄 Refresh Logs</a></p>\n";
?>
