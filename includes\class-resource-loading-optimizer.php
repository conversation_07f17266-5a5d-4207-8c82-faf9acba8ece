<?php
/**
 * Resource Loading Optimizer - Phase 3 Consolidation
 * 
 * Shared resource loading optimization utilities for Lazy Load and Heartbeat Control modules.
 * Consolidates duplicate functionality and provides unified performance optimization.
 * 
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Resource_Loading_Optimizer {

    /**
     * Shared performance thresholds
     */
    private static $performance_thresholds = array(
        'lazy_load_threshold' => 200,        // pixels before viewport
        'heartbeat_admin_optimal' => 60,     // seconds
        'heartbeat_editor_optimal' => 30,    // seconds
        'heartbeat_frontend_optimal' => 0,   // disabled
        'intersection_observer_margin' => 50, // pixels
        'performance_budget_images' => 2,    // exclude first N images
        'loading_priority_threshold' => 100  // ms for critical resources
    );

    /**
     * Shared performance monitoring
     */
    private static $performance_metrics = array();

    /**
     * Initialize shared optimization system
     */
    public static function init() {
        // EMERGENCY: Safety check to prevent fatal errors when plugin is disabled
        if (!function_exists('redco_is_module_enabled')) {
            return; // Plugin is disabled, exit gracefully
        }

        // Only initialize if either module is enabled
        if (!redco_is_module_enabled('lazy-load') && !redco_is_module_enabled('heartbeat-control')) {
            return;
        }

        // Initialize shared hooks
        add_action('wp_head', array(__CLASS__, 'add_performance_hints'), 1);
        add_action('wp_footer', array(__CLASS__, 'add_performance_monitoring'), 999);
        
        // Admin hooks for performance monitoring
        if (is_admin()) {
            add_action('wp_ajax_redco_get_resource_performance', array(__CLASS__, 'ajax_get_performance_metrics'));
        }
    }

    /**
     * Get optimal threshold for resource loading
     * 
     * @param string $resource_type Type of resource (lazy_load, heartbeat, etc.)
     * @param string $context Context (admin, editor, frontend)
     * @return int Optimal threshold value
     */
    public static function get_optimal_threshold($resource_type, $context = 'frontend') {
        $key = $resource_type . '_' . $context . '_optimal';
        
        if (isset(self::$performance_thresholds[$key])) {
            return self::$performance_thresholds[$key];
        }
        
        // Fallback to base threshold
        $base_key = $resource_type . '_threshold';
        return isset(self::$performance_thresholds[$base_key]) 
            ? self::$performance_thresholds[$base_key] 
            : 200;
    }

    /**
     * Calculate unified performance score
     *
     * @return array Performance metrics
     */
    public static function calculate_performance_score() {
        // EMERGENCY: Safety check to prevent fatal errors when plugin is disabled
        if (!function_exists('redco_is_module_enabled')) {
            return array('score' => 0, 'status' => 'disabled', 'recommendations' => array());
        }

        $lazy_load_enabled = redco_is_module_enabled('lazy-load');
        $heartbeat_enabled = redco_is_module_enabled('heartbeat-control');
        
        $score = 0;
        $max_score = 0;
        $recommendations = array();

        // Lazy Load scoring
        if ($lazy_load_enabled) {
            $lazy_stats = self::get_lazy_load_performance();
            $score += $lazy_stats['score'];
            $max_score += 40; // 40% of total score
            
            if (!empty($lazy_stats['recommendations'])) {
                $recommendations = array_merge($recommendations, $lazy_stats['recommendations']);
            }
        }

        // Heartbeat Control scoring
        if ($heartbeat_enabled) {
            $heartbeat_stats = self::get_heartbeat_performance();
            $score += $heartbeat_stats['score'];
            $max_score += 30; // 30% of total score
            
            if (!empty($heartbeat_stats['recommendations'])) {
                $recommendations = array_merge($recommendations, $heartbeat_stats['recommendations']);
            }
        }

        // Combined optimization scoring
        if ($lazy_load_enabled && $heartbeat_enabled) {
            $combined_score = self::get_combined_optimization_score();
            $score += $combined_score['score'];
            $max_score += 30; // 30% for synergy
            
            if (!empty($combined_score['recommendations'])) {
                $recommendations = array_merge($recommendations, $combined_score['recommendations']);
            }
        }

        $final_score = $max_score > 0 ? round(($score / $max_score) * 100) : 0;

        return array(
            'score' => $final_score,
            'status' => self::get_performance_status($final_score),
            'lazy_load_enabled' => $lazy_load_enabled,
            'heartbeat_enabled' => $heartbeat_enabled,
            'recommendations' => array_slice($recommendations, 0, 5), // Top 5 recommendations
            'metrics' => array(
                'resource_loading_optimized' => $lazy_load_enabled,
                'background_processing_optimized' => $heartbeat_enabled,
                'combined_optimization_active' => $lazy_load_enabled && $heartbeat_enabled
            )
        );
    }

    /**
     * Get lazy load performance metrics
     */
    private static function get_lazy_load_performance() {
        if (!class_exists('Redco_Lazy_Load')) {
            return array('score' => 0, 'recommendations' => array());
        }

        $lazy_load = new Redco_Lazy_Load();
        $stats = $lazy_load->get_stats();
        
        $score = 0;
        $recommendations = array();

        // Score based on images processed
        if ($stats['images_processed'] > 50) {
            $score += 15;
        } elseif ($stats['images_processed'] > 10) {
            $score += 10;
        } elseif ($stats['images_processed'] > 0) {
            $score += 5;
        } else {
            $recommendations[] = 'Enable lazy loading to optimize image loading performance';
        }

        // Score based on settings optimization
        if ($stats['settings_optimized']['optimized']) {
            $score += 15;
        } else {
            $recommendations = array_merge($recommendations, $stats['settings_optimized']['recommendations']);
        }

        // Score based on threshold optimization
        $threshold = $stats['threshold'];
        if ($threshold <= 200) {
            $score += 10;
        } elseif ($threshold <= 300) {
            $score += 5;
        } else {
            $recommendations[] = 'Reduce lazy loading threshold to 200px for better performance';
        }

        return array(
            'score' => $score,
            'recommendations' => $recommendations
        );
    }

    /**
     * Get heartbeat control performance metrics
     */
    private static function get_heartbeat_performance() {
        // EMERGENCY: Safety check to prevent fatal errors when plugin is disabled
        if (!function_exists('redco_get_module_option')) {
            return array('score' => 0, 'recommendations' => array());
        }

        $score = 0;
        $recommendations = array();

        $admin_heartbeat = redco_get_module_option('heartbeat-control', 'admin_heartbeat', 'modify');
        $admin_frequency = redco_get_module_option('heartbeat-control', 'admin_frequency', 60);
        $frontend_heartbeat = redco_get_module_option('heartbeat-control', 'frontend_heartbeat', 'disable');

        // Frontend heartbeat optimization
        if ($frontend_heartbeat === 'disable') {
            $score += 15;
        } elseif ($frontend_heartbeat === 'modify') {
            $score += 5;
            $recommendations[] = 'Consider disabling frontend heartbeat for better performance';
        } else {
            $recommendations[] = 'Disable frontend heartbeat to reduce server load';
        }

        // Admin heartbeat optimization
        if ($admin_heartbeat === 'modify' && $admin_frequency >= 60) {
            $score += 10;
        } elseif ($admin_heartbeat === 'modify') {
            $score += 5;
        } else {
            $recommendations[] = 'Optimize admin heartbeat frequency to reduce server load';
        }

        // Editor heartbeat optimization
        $editor_heartbeat = redco_get_module_option('heartbeat-control', 'editor_heartbeat', 'modify');
        $editor_frequency = redco_get_module_option('heartbeat-control', 'editor_frequency', 30);
        
        if ($editor_heartbeat === 'modify' && $editor_frequency >= 30) {
            $score += 5;
        }

        return array(
            'score' => $score,
            'recommendations' => $recommendations
        );
    }

    /**
     * Get combined optimization score
     */
    private static function get_combined_optimization_score() {
        $score = 0;
        $recommendations = array();

        // Synergy bonus for having both modules enabled
        $score += 10;

        // Check for optimal configuration synergy
        $lazy_threshold = redco_get_module_option('lazy-load', 'threshold', 200);
        $heartbeat_frontend = redco_get_module_option('heartbeat-control', 'frontend_heartbeat', 'disable');

        if ($lazy_threshold <= 200 && $heartbeat_frontend === 'disable') {
            $score += 10;
            $recommendations[] = 'Excellent! Both modules are optimally configured for maximum performance';
        } else {
            $score += 5;
            $recommendations[] = 'Fine-tune both modules for optimal performance synergy';
        }

        // Performance budget optimization
        $exclude_first_images = redco_get_module_option('lazy-load', 'exclude_first_images', 2);
        if ($exclude_first_images >= 2) {
            $score += 10;
        } else {
            $recommendations[] = 'Exclude first 2 images from lazy loading to improve LCP';
        }

        return array(
            'score' => $score,
            'recommendations' => $recommendations
        );
    }

    /**
     * Get performance status based on score
     */
    private static function get_performance_status($score) {
        if ($score >= 90) {
            return 'excellent';
        } elseif ($score >= 75) {
            return 'good';
        } elseif ($score >= 60) {
            return 'fair';
        } else {
            return 'needs_improvement';
        }
    }

    /**
     * Add performance hints to page head
     */
    public static function add_performance_hints() {
        // EMERGENCY: Safety check to prevent fatal errors when plugin is disabled
        if (!function_exists('redco_is_module_enabled')) {
            return;
        }

        // Only add hints on frontend
        if (is_admin()) {
            return;
        }

        echo "<!-- Redco Resource Loading Optimization -->\n";

        // Add Intersection Observer polyfill hint for older browsers
        if (redco_is_module_enabled('lazy-load')) {
            echo '<link rel="preconnect" href="https://polyfill.io" crossorigin>' . "\n";
        }
        
        // Add performance monitoring hint
        echo '<meta name="redco-resource-optimization" content="active">' . "\n";
        echo "<!-- End Redco Resource Loading Optimization -->\n";
    }

    /**
     * Add performance monitoring script
     */
    public static function add_performance_monitoring() {
        // EMERGENCY: Safety check to prevent fatal errors when plugin is disabled
        if (!function_exists('redco_is_module_enabled')) {
            return;
        }

        if (is_admin()) {
            return;
        }

        ?>
        <script>
        // Redco Resource Loading Performance Monitoring
        (function() {
            if (typeof window.redcoResourcePerformance === 'undefined') {
                window.redcoResourcePerformance = {
                    lazyLoadActive: <?php echo redco_is_module_enabled('lazy-load') ? 'true' : 'false'; ?>,
                    heartbeatOptimized: <?php echo redco_is_module_enabled('heartbeat-control') ? 'true' : 'false'; ?>,
                    startTime: performance.now()
                };
            }
        })();
        </script>
        <?php
    }

    /**
     * AJAX handler for performance metrics
     * NOTE: This handler is disabled in favor of Phase 2 diagnostic system
     */
    public static function ajax_get_performance_metrics_disabled() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
            return;
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Insufficient permissions'));
            return;
        }

        try {
            $performance_data = self::calculate_performance_score();
            wp_send_json_success($performance_data);
        } catch (Exception $e) {
            wp_send_json_error(array('message' => 'Failed to get performance metrics'));
        }
    }

    /**
     * Get unified resource loading recommendations with enhanced synergy detection
     */
    public static function get_optimization_recommendations() {
        $recommendations = array();

        if (!redco_is_module_enabled('lazy-load')) {
            $recommendations[] = array(
                'type' => 'enable_module',
                'module' => 'lazy-load',
                'title' => 'Enable Lazy Loading',
                'description' => 'Reduce initial page load time by deferring image loading',
                'impact' => 'high'
            );
        }

        if (!redco_is_module_enabled('heartbeat-control')) {
            $recommendations[] = array(
                'type' => 'enable_module',
                'module' => 'heartbeat-control',
                'title' => 'Enable Heartbeat Control',
                'description' => 'Reduce server load by optimizing WordPress heartbeat frequency',
                'impact' => 'medium'
            );
        }

        // Enhanced synergy recommendations
        if (redco_is_module_enabled('lazy-load') && redco_is_module_enabled('heartbeat-control')) {
            $lazy_threshold = redco_get_module_option('lazy-load', 'threshold', 200);
            $heartbeat_frontend = redco_get_module_option('heartbeat-control', 'frontend_heartbeat', 'disable');

            if ($lazy_threshold > 300 || $heartbeat_frontend !== 'disable') {
                $recommendations[] = array(
                    'type' => 'optimize_settings',
                    'title' => 'Optimize Module Synergy',
                    'description' => 'Fine-tune both modules for maximum performance impact',
                    'impact' => 'medium',
                    'action' => 'apply_performance_preset'
                );
            }
        }

        return $recommendations;
    }

    /**
     * Apply consolidated optimization preset to both modules
     */
    public static function apply_optimization_preset($preset) {
        $results = array();

        switch ($preset) {
            case 'performance':
                $lazy_settings = array(
                    'threshold' => 150,
                    'exclude_first_images' => 1,
                    'exclude_featured' => false
                );
                $heartbeat_settings = array(
                    'admin_heartbeat' => 'modify',
                    'admin_frequency' => 120,
                    'editor_heartbeat' => 'modify',
                    'editor_frequency' => 60,
                    'frontend_heartbeat' => 'disable'
                );
                break;

            case 'balanced':
                $lazy_settings = array(
                    'threshold' => 200,
                    'exclude_first_images' => 2,
                    'exclude_featured' => false
                );
                $heartbeat_settings = array(
                    'admin_heartbeat' => 'modify',
                    'admin_frequency' => 60,
                    'editor_heartbeat' => 'modify',
                    'editor_frequency' => 30,
                    'frontend_heartbeat' => 'disable'
                );
                break;

            case 'conservative':
                $lazy_settings = array(
                    'threshold' => 300,
                    'exclude_first_images' => 3,
                    'exclude_featured' => true
                );
                $heartbeat_settings = array(
                    'admin_heartbeat' => 'modify',
                    'admin_frequency' => 45,
                    'editor_heartbeat' => 'modify',
                    'editor_frequency' => 15,
                    'frontend_heartbeat' => 'modify',
                    'frontend_frequency' => 120
                );
                break;

            default:
                return array('success' => false, 'message' => 'Unknown preset');
        }

        // Apply settings to both modules
        foreach ($lazy_settings as $key => $value) {
            redco_update_module_option('lazy-load', $key, $value);
        }

        foreach ($heartbeat_settings as $key => $value) {
            redco_update_module_option('heartbeat-control', $key, $value);
        }

        return array(
            'success' => true,
            'message' => ucfirst($preset) . ' preset applied to both modules',
            'settings' => array(
                'lazy_load' => $lazy_settings,
                'heartbeat_control' => $heartbeat_settings
            )
        );
    }
}

// Initialize the shared optimization system
add_action('init', array('Redco_Resource_Loading_Optimizer', 'init'), 5);
