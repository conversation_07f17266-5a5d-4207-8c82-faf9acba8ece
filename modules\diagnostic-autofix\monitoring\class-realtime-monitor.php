<?php
/**
 * Real-Time Performance Monitoring System for Redco Optimizer
 * 
 * Phase 2 Enhancement: Continuous performance tracking with intelligent alerting
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Realtime_Monitor {
    
    private $monitoring_intervals = array(
        '5sec' => 5,
        '30sec' => 30,
        '1min' => 60,
        '5min' => 300,
        '15min' => 900
    );
    
    private $metric_types = array(
        'page_load_time' => 'Page Load Time (ms)',
        'ttfb' => 'Time to First Byte (ms)',
        'memory_usage' => 'Memory Usage (MB)',
        'database_queries' => 'Database Queries',
        'cache_hit_ratio' => 'Cache Hit Ratio (%)',
        'error_rate' => 'Error Rate (%)',
        'uptime_status' => 'Uptime Status'
    );
    
    /**
     * Initialize real-time monitoring
     */
    public function init() {
        // AJAX handlers for monitoring
        add_action('wp_ajax_redco_get_live_metrics', array($this, 'ajax_get_live_metrics'));
        add_action('wp_ajax_redco_start_monitoring', array($this, 'ajax_start_monitoring'));
        add_action('wp_ajax_redco_stop_monitoring', array($this, 'ajax_stop_monitoring'));
        add_action('wp_ajax_redco_get_monitoring_config', array($this, 'ajax_get_monitoring_config'));
        add_action('wp_ajax_redco_update_monitoring_config', array($this, 'ajax_update_monitoring_config'));
        
        // Cron hooks for automated monitoring
        add_action('redco_collect_metrics', array($this, 'collect_performance_metrics'));
        add_action('redco_check_alerts', array($this, 'check_performance_alerts'));
        
        // Schedule monitoring if enabled
        $this->maybe_schedule_monitoring();
        
        // Ensure monitoring tables exist
        $this->maybe_create_monitoring_tables();
    }
    
    /**
     * AJAX: Get live performance metrics
     */
    public function ajax_get_live_metrics() {
        // Accept both diagnostic nonce and general nonce for compatibility
        $nonce = $_POST['nonce'] ?? '';
        $nonce_valid = wp_verify_nonce($nonce, 'redco_diagnostic_nonce') ||
                      wp_verify_nonce($nonce, 'redco_optimizer_nonce');

        if (!$nonce_valid) {
            wp_send_json_error('Security verification failed');
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $metrics = $this->collect_current_metrics();
        $historical_data = $this->get_historical_metrics(24); // Last 24 hours
        $alerts = $this->get_active_alerts();
        
        wp_send_json_success(array(
            'current_metrics' => $metrics,
            'historical_data' => $historical_data,
            'active_alerts' => $alerts,
            'timestamp' => time(),
            'monitoring_status' => $this->get_monitoring_status()
        ));
    }
    
    /**
     * Collect current performance metrics
     */
    public function collect_current_metrics() {
        $metrics = array();
        
        // Page load time measurement
        $metrics['page_load_time'] = $this->measure_page_load_time();
        
        // Time to First Byte
        $metrics['ttfb'] = $this->measure_ttfb();
        
        // Memory usage
        $metrics['memory_usage'] = $this->get_memory_usage();
        
        // Database performance
        $metrics['database_queries'] = $this->count_database_queries();
        
        // Cache performance
        $metrics['cache_hit_ratio'] = $this->get_cache_hit_ratio();
        
        // Error rate
        $metrics['error_rate'] = $this->calculate_error_rate();
        
        // Uptime status
        $metrics['uptime_status'] = $this->check_uptime_status();
        
        // Core Web Vitals (if available)
        $metrics['core_web_vitals'] = $this->get_core_web_vitals();
        
        return $metrics;
    }
    
    /**
     * Measure page load time
     */
    private function measure_page_load_time() {
        $start_time = microtime(true);
        
        // Test homepage load time
        $response = wp_remote_get(home_url(), array(
            'timeout' => 30,
            'user-agent' => 'Redco-Monitor/1.0'
        ));
        
        $end_time = microtime(true);
        
        if (is_wp_error($response)) {
            return array(
                'value' => 0,
                'status' => 'error',
                'error' => $response->get_error_message()
            );
        }
        
        $load_time = round(($end_time - $start_time) * 1000, 2);
        
        return array(
            'value' => $load_time,
            'status' => $this->get_performance_status($load_time, 'page_load_time'),
            'unit' => 'ms'
        );
    }
    
    /**
     * Measure Time to First Byte
     */
    private function measure_ttfb() {
        $start_time = microtime(true);
        
        $response = wp_remote_head(home_url(), array(
            'timeout' => 10,
            'user-agent' => 'Redco-Monitor/1.0'
        ));
        
        $ttfb = microtime(true) - $start_time;
        
        if (is_wp_error($response)) {
            return array(
                'value' => 0,
                'status' => 'error',
                'error' => $response->get_error_message()
            );
        }
        
        $ttfb_ms = round($ttfb * 1000, 2);
        
        return array(
            'value' => $ttfb_ms,
            'status' => $this->get_performance_status($ttfb_ms, 'ttfb'),
            'unit' => 'ms'
        );
    }
    
    /**
     * Get memory usage
     */
    private function get_memory_usage() {
        $memory_usage = memory_get_usage(true);
        $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));
        
        $usage_mb = round($memory_usage / 1024 / 1024, 2);
        $limit_mb = round($memory_limit / 1024 / 1024, 2);
        $usage_percentage = round(($memory_usage / $memory_limit) * 100, 2);
        
        return array(
            'value' => $usage_mb,
            'percentage' => $usage_percentage,
            'limit' => $limit_mb,
            'status' => $this->get_performance_status($usage_percentage, 'memory_usage'),
            'unit' => 'MB'
        );
    }
    
    /**
     * Count database queries
     */
    private function count_database_queries() {
        global $wpdb;
        
        $query_count = $wpdb->num_queries;
        
        return array(
            'value' => $query_count,
            'status' => $this->get_performance_status($query_count, 'database_queries'),
            'unit' => 'queries'
        );
    }
    
    /**
     * Get cache hit ratio
     */
    private function get_cache_hit_ratio() {
        // Check for common caching plugins
        $cache_ratio = 0;
        $cache_plugin = 'none';
        
        if (function_exists('w3tc_get_stats')) {
            // W3 Total Cache
            $stats = w3tc_get_stats();
            $cache_ratio = isset($stats['cache_hit_ratio']) ? $stats['cache_hit_ratio'] : 0;
            $cache_plugin = 'W3 Total Cache';
        } elseif (function_exists('wp_cache_get_stats')) {
            // WP Super Cache
            $stats = wp_cache_get_stats();
            $cache_ratio = isset($stats['hit_ratio']) ? $stats['hit_ratio'] : 0;
            $cache_plugin = 'WP Super Cache';
        } elseif (defined('WP_ROCKET_VERSION')) {
            // WP Rocket - estimate based on cached files
            $cache_ratio = $this->estimate_wp_rocket_hit_ratio();
            $cache_plugin = 'WP Rocket';
        }
        
        return array(
            'value' => $cache_ratio,
            'status' => $this->get_performance_status($cache_ratio, 'cache_hit_ratio'),
            'plugin' => $cache_plugin,
            'unit' => '%'
        );
    }
    
    /**
     * Calculate error rate
     */
    private function calculate_error_rate() {
        // Check error log for recent errors
        $error_log = ini_get('error_log');
        $error_count = 0;
        
        if ($error_log && file_exists($error_log)) {
            $log_content = file_get_contents($error_log);
            $recent_errors = substr_count($log_content, date('Y-m-d'));
            $error_count = $recent_errors;
        }
        
        // Calculate error rate as percentage
        $total_requests = $this->estimate_daily_requests();
        $error_rate = $total_requests > 0 ? round(($error_count / $total_requests) * 100, 4) : 0;
        
        return array(
            'value' => $error_rate,
            'error_count' => $error_count,
            'total_requests' => $total_requests,
            'status' => $this->get_performance_status($error_rate, 'error_rate'),
            'unit' => '%'
        );
    }
    
    /**
     * Check uptime status
     */
    private function check_uptime_status() {
        $response = wp_remote_get(home_url(), array(
            'timeout' => 10,
            'user-agent' => 'Redco-Monitor/1.0'
        ));
        
        if (is_wp_error($response)) {
            return array(
                'value' => 0,
                'status' => 'down',
                'error' => $response->get_error_message()
            );
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        
        return array(
            'value' => $response_code,
            'status' => $response_code === 200 ? 'up' : 'degraded',
            'response_code' => $response_code
        );
    }
    
    /**
     * Get Core Web Vitals (placeholder for Phase 2 integration)
     */
    private function get_core_web_vitals() {
        // This will be enhanced in Core Web Vitals integration
        return array(
            'lcp' => array('value' => 0, 'status' => 'pending'),
            'fid' => array('value' => 0, 'status' => 'pending'),
            'cls' => array('value' => 0, 'status' => 'pending'),
            'source' => 'local_measurement'
        );
    }
    
    /**
     * Get performance status based on thresholds
     */
    private function get_performance_status($value, $metric_type) {
        $thresholds = array(
            'page_load_time' => array('good' => 1000, 'needs_improvement' => 2500),
            'ttfb' => array('good' => 200, 'needs_improvement' => 600),
            'memory_usage' => array('good' => 50, 'needs_improvement' => 80),
            'database_queries' => array('good' => 25, 'needs_improvement' => 50),
            'cache_hit_ratio' => array('good' => 80, 'needs_improvement' => 60),
            'error_rate' => array('good' => 0.1, 'needs_improvement' => 1.0)
        );
        
        if (!isset($thresholds[$metric_type])) {
            return 'unknown';
        }
        
        $threshold = $thresholds[$metric_type];
        
        // For cache hit ratio, higher is better
        if ($metric_type === 'cache_hit_ratio') {
            if ($value >= $threshold['good']) return 'good';
            if ($value >= $threshold['needs_improvement']) return 'needs_improvement';
            return 'poor';
        }
        
        // For other metrics, lower is better
        if ($value <= $threshold['good']) return 'good';
        if ($value <= $threshold['needs_improvement']) return 'needs_improvement';
        return 'poor';
    }
    
    /**
     * Store performance metrics in database
     */
    public function collect_performance_metrics() {
        $metrics = $this->collect_current_metrics();
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'redco_performance_metrics';
        
        foreach ($metrics as $metric_name => $metric_data) {
            if (is_array($metric_data) && isset($metric_data['value'])) {
                $wpdb->insert(
                    $table_name,
                    array(
                        'timestamp' => current_time('mysql'),
                        'metric_type' => 'performance',
                        'metric_name' => $metric_name,
                        'metric_value' => $metric_data['value'],
                        'metric_unit' => $metric_data['unit'] ?? '',
                        'additional_data' => json_encode($metric_data)
                    ),
                    array('%s', '%s', '%s', '%f', '%s', '%s')
                );
            }
        }
    }
    
    /**
     * Maybe create monitoring tables
     */
    private function maybe_create_monitoring_tables() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'redco_performance_metrics';
        
        if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") != $table_name) {
            $charset_collate = $wpdb->get_charset_collate();
            
            $sql = "CREATE TABLE {$table_name} (
                id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                timestamp datetime NOT NULL,
                metric_type varchar(50) NOT NULL,
                metric_name varchar(100) NOT NULL,
                metric_value decimal(10,4) NOT NULL,
                metric_unit varchar(20) DEFAULT NULL,
                page_url varchar(500) DEFAULT NULL,
                additional_data longtext DEFAULT NULL,
                PRIMARY KEY (id),
                KEY timestamp_idx (timestamp),
                KEY metric_type_idx (metric_type),
                KEY metric_name_idx (metric_name)
            ) {$charset_collate};";
            
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
        }
    }

    /**
     * Helper methods for monitoring operations
     */
    private function estimate_wp_rocket_hit_ratio() {
        // Estimate cache hit ratio for WP Rocket
        $cache_dir = WP_CONTENT_DIR . '/cache/wp-rocket/';
        if (!is_dir($cache_dir)) {
            return 0;
        }

        // Count cached files vs total pages (rough estimate)
        $cached_files = glob($cache_dir . '*/*.html');
        $total_pages = wp_count_posts('page')->publish + wp_count_posts('post')->publish;

        if ($total_pages == 0) return 0;

        return min(100, round((count($cached_files) / $total_pages) * 100, 2));
    }

    private function estimate_daily_requests() {
        // Rough estimate based on site activity
        $posts_count = wp_count_posts('post')->publish;
        $pages_count = wp_count_posts('page')->publish;
        $comments_count = wp_count_comments()->approved;

        // Basic estimation formula
        $base_requests = ($posts_count + $pages_count) * 10; // 10 views per post/page per day
        $comment_requests = $comments_count * 2; // Comments generate additional requests

        return max(100, $base_requests + $comment_requests); // Minimum 100 requests
    }

    private function get_historical_metrics($hours = 24) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'redco_performance_metrics';

        $since = date('Y-m-d H:i:s', strtotime("-{$hours} hours"));

        $results = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT metric_name, metric_value, metric_unit, timestamp
                 FROM {$table_name}
                 WHERE timestamp >= %s
                 ORDER BY timestamp ASC",
                $since
            ),
            ARRAY_A
        );

        // Group by metric name
        $grouped_data = array();
        foreach ($results as $row) {
            $metric_name = $row['metric_name'];
            if (!isset($grouped_data[$metric_name])) {
                $grouped_data[$metric_name] = array();
            }
            $grouped_data[$metric_name][] = array(
                'value' => floatval($row['metric_value']),
                'timestamp' => $row['timestamp'],
                'unit' => $row['metric_unit']
            );
        }

        return $grouped_data;
    }

    private function get_active_alerts() {
        $alerts = array();
        $current_metrics = $this->collect_current_metrics();

        foreach ($current_metrics as $metric_name => $metric_data) {
            if (isset($metric_data['status']) && $metric_data['status'] === 'poor') {
                $alerts[] = array(
                    'type' => 'performance',
                    'severity' => 'high',
                    'metric' => $metric_name,
                    'message' => $this->get_alert_message($metric_name, $metric_data),
                    'timestamp' => time()
                );
            }
        }

        return $alerts;
    }

    private function get_alert_message($metric_name, $metric_data) {
        $value = $metric_data['value'] ?? 'unknown';
        $unit = $metric_data['unit'] ?? '';

        $messages = array(
            'page_load_time' => "Page load time is {$value}{$unit} - exceeds recommended threshold",
            'ttfb' => "Time to First Byte is {$value}{$unit} - server response is slow",
            'memory_usage' => "Memory usage is " . (isset($metric_data['percentage']) ? $metric_data['percentage'] . '% (' . $value . $unit . ')' : $value . $unit) . " - approaching limit",
            'database_queries' => "Database queries count is {$value} - consider optimization",
            'cache_hit_ratio' => "Cache hit ratio is {$value}{$unit} - cache performance is poor",
            'error_rate' => "Error rate is {$value}{$unit} - multiple errors detected"
        );

        return $messages[$metric_name] ?? "Performance issue detected with {$metric_name}";
    }

    private function get_monitoring_status() {
        $config = get_option('redco_monitoring_config', array(
            'enabled' => false,
            'interval' => '5min',
            'metrics' => array_keys($this->metric_types)
        ));

        return array(
            'enabled' => $config['enabled'],
            'interval' => $config['interval'],
            'next_collection' => wp_next_scheduled('redco_collect_metrics'),
            'active_metrics' => count($config['metrics'])
        );
    }

    private function maybe_schedule_monitoring() {
        $config = get_option('redco_monitoring_config', array('enabled' => false));

        if ($config['enabled'] && !wp_next_scheduled('redco_collect_metrics')) {
            $interval = $config['interval'] ?? '5min';
            $interval_seconds = $this->monitoring_intervals[$interval] ?? 300;

            wp_schedule_event(time(), 'redco_monitoring_interval', 'redco_collect_metrics');

            // Add custom cron interval
            add_filter('cron_schedules', function($schedules) use ($interval_seconds) {
                $schedules['redco_monitoring_interval'] = array(
                    'interval' => $interval_seconds,
                    'display' => 'Redco Monitoring Interval'
                );
                return $schedules;
            });
        }
    }

    /**
     * AJAX: Start monitoring
     */
    public function ajax_start_monitoring() {
        // Accept both diagnostic nonce and general nonce for compatibility
        $nonce = $_POST['nonce'] ?? '';
        $nonce_valid = wp_verify_nonce($nonce, 'redco_diagnostic_nonce') ||
                      wp_verify_nonce($nonce, 'redco_optimizer_nonce');

        if (!$nonce_valid) {
            wp_send_json_error('Security verification failed');
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $config = get_option('redco_monitoring_config', array());
        $config['enabled'] = true;
        $config['started_at'] = current_time('mysql');

        update_option('redco_monitoring_config', $config);
        $this->maybe_schedule_monitoring();

        wp_send_json_success('Monitoring started successfully');
    }

    /**
     * AJAX: Stop monitoring
     */
    public function ajax_stop_monitoring() {
        check_ajax_referer('redco_diagnostic_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $config = get_option('redco_monitoring_config', array());
        $config['enabled'] = false;
        $config['stopped_at'] = current_time('mysql');

        update_option('redco_monitoring_config', $config);

        // Clear scheduled events
        wp_clear_scheduled_hook('redco_collect_metrics');
        wp_clear_scheduled_hook('redco_check_alerts');

        wp_send_json_success('Monitoring stopped successfully');
    }

    /**
     * AJAX: Get monitoring configuration
     */
    public function ajax_get_monitoring_config() {
        check_ajax_referer('redco_diagnostic_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $config = get_option('redco_monitoring_config', array(
            'enabled' => false,
            'interval' => '5min',
            'metrics' => array_keys($this->metric_types),
            'alert_thresholds' => array(),
            'notification_email' => get_option('admin_email')
        ));

        wp_send_json_success(array(
            'config' => $config,
            'available_intervals' => $this->monitoring_intervals,
            'available_metrics' => $this->metric_types
        ));
    }

    /**
     * AJAX: Update monitoring configuration
     */
    public function ajax_update_monitoring_config() {
        check_ajax_referer('redco_diagnostic_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $new_config = array(
            'enabled' => !empty($_POST['enabled']),
            'interval' => sanitize_text_field($_POST['interval'] ?? '5min'),
            'metrics' => array_map('sanitize_text_field', $_POST['metrics'] ?? array()),
            'notification_email' => sanitize_email($_POST['notification_email'] ?? get_option('admin_email')),
            'alert_thresholds' => array_map('floatval', $_POST['alert_thresholds'] ?? array())
        );

        update_option('redco_monitoring_config', $new_config);

        // Reschedule if needed
        wp_clear_scheduled_hook('redco_collect_metrics');
        if ($new_config['enabled']) {
            $this->maybe_schedule_monitoring();
        }

        wp_send_json_success('Monitoring configuration updated successfully');
    }

    /**
     * Check performance alerts
     */
    public function check_performance_alerts() {
        $config = get_option('redco_monitoring_config', array());

        if (empty($config['enabled'])) {
            return;
        }

        $alerts = $this->get_active_alerts();

        if (!empty($alerts) && !empty($config['notification_email'])) {
            $this->send_alert_notification($alerts, $config['notification_email']);
        }

        // Store alerts for dashboard display
        update_option('redco_active_alerts', $alerts);
    }

    private function send_alert_notification($alerts, $email) {
        $subject = 'Redco Optimizer: Performance Alert';
        $message = "Performance alerts detected on " . get_bloginfo('name') . ":\n\n";

        foreach ($alerts as $alert) {
            $message .= "• " . $alert['message'] . "\n";
        }

        $message .= "\nPlease check your WordPress dashboard for more details.";

        wp_mail($email, $subject, $message);
    }
}
