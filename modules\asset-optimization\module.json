{"name": "Asset Optimization", "slug": "asset-optimization", "description": "Unified CSS/JS minification with critical resource optimization. Combines minification, critical CSS, resource hints, and performance optimization in one powerful module.", "version": "1.0.0", "category": "performance", "priority": 10, "dependencies": [], "replaces": ["css-js-minifier", "critical-resource-optimizer"], "features": ["CSS Minification", "JavaScript Minification", "Inline Code Minification", "Critical CSS Extraction", "Critical CSS Inlining", "Non-Critical CSS Deferring", "JavaScript Loading Optimization", "Font Loading Optimization", "Resource Hints (preconnect, dns-prefetch)", "Stylesheet Preloading", "Unified Caching System", "Performance Statistics", "AJAX Cache Management"], "benefits": ["Faster page loading times", "Reduced bandwidth usage", "Improved Core Web Vitals", "Better user experience", "SEO performance boost", "Unified optimization strategy"], "settings": {"minify_css": {"type": "boolean", "default": true, "label": "Minify CSS Files", "description": "Automatically minify CSS files to reduce file sizes"}, "minify_js": {"type": "boolean", "default": true, "label": "Minify JavaScript Files", "description": "Automatically minify JavaScript files to reduce file sizes"}, "minify_inline": {"type": "boolean", "default": true, "label": "Minify Inline CSS/JS", "description": "Minify CSS and JavaScript code embedded directly in HTML"}, "exclude_css": {"type": "array", "default": [], "label": "Exclude CSS Files", "description": "CSS file handles to exclude from minification"}, "exclude_js": {"type": "array", "default": ["jquery-core", "jquery-migrate"], "label": "Exclude JavaScript Files", "description": "JavaScript file handles to exclude from minification"}, "critical_css": {"type": "boolean", "default": true, "label": "Critical CSS Inlining", "description": "Inline critical above-the-fold CSS for faster rendering"}, "defer_non_critical": {"type": "boolean", "default": true, "label": "Defer Non-Critical CSS", "description": "Load non-critical CSS asynchronously to improve page speed"}, "optimize_js": {"type": "boolean", "default": true, "label": "JavaScript Loading Optimization", "description": "Optimize JavaScript loading with async and defer attributes"}, "optimize_fonts": {"type": "boolean", "default": true, "label": "Font Loading Optimization", "description": "Optimize font loading with font-display: swap and preconnect hints"}, "resource_hints": {"type": "boolean", "default": true, "label": "Enable Resource Hints", "description": "Add DNS prefetch and preconnect hints for external resources"}, "preconnect_google_fonts": {"type": "boolean", "default": true, "label": "Preconnect to Google Fonts", "description": "Add preconnect hints for Google Fonts domains"}, "preconnect_analytics": {"type": "boolean", "default": true, "label": "Preconnect to Analytics", "description": "Add preconnect hints for Google Analytics domains"}, "combine_css": {"type": "boolean", "default": false, "label": "Combine CSS Files", "description": "Combine multiple CSS files into one (advanced feature)"}, "combine_js": {"type": "boolean", "default": false, "label": "Combine JavaScript Files", "description": "Combine multiple JavaScript files into one (advanced feature)"}, "async_js": {"type": "boolean", "default": true, "label": "Async JavaScript Loading", "description": "Load JavaScript files asynchronously when possible"}, "defer_js": {"type": "boolean", "default": true, "label": "Defer JavaScript Loading", "description": "Defer JavaScript execution until DOM is ready"}, "preload_critical": {"type": "boolean", "default": true, "label": "Preload Critical Resources", "description": "Preload critical stylesheets and scripts"}, "remove_unused_css": {"type": "boolean", "default": false, "label": "Remove Unused CSS", "description": "Remove unused CSS rules (advanced feature)"}, "enable_gzip": {"type": "boolean", "default": true, "label": "Enable Gzip Compression", "description": "Enable Gzip compression for optimized files"}, "cache_duration": {"type": "integer", "default": 86400, "label": "<PERSON><PERSON> (seconds)", "description": "How long to cache optimized files"}, "enable_brotli": {"type": "boolean", "default": false, "label": "Enable Brotli Compression", "description": "Enable Brotli compression for better compression ratios"}}, "ajax_actions": ["redco_clear_asset_cache", "redco_generate_critical_css", "redco_optimize_assets"], "cache_directories": ["optimized", "critical-css"], "hooks": {"wp_head": ["inject_critical_css", "add_resource_hints", "optimize_font_loading"], "style_loader_src": ["optimize_css_file"], "style_loader_tag": ["defer_non_critical_css"], "script_loader_src": ["optimize_js_file"], "script_loader_tag": ["optimize_javascript_loading"], "save_post": ["clear_critical_cache"], "switch_theme": ["clear_all_cache"]}, "compatibility": {"wordpress": "5.0+", "php": "7.4+", "conflicts": ["wp-rocket", "w3-total-cache", "wp-super-cache", "autoptimize"], "tested_with": ["elementor", "<PERSON><PERSON>", "woocommerce", "contact-form-7"]}, "performance_impact": {"memory_usage": "low", "cpu_usage": "medium", "disk_usage": "medium", "network_impact": "positive"}, "migration": {"from_modules": [{"module": "css-js-minifier", "settings_map": {"minify_css": "minify_css", "minify_js": "minify_js", "minify_inline": "minify_inline", "exclude_css": "exclude_css", "exclude_js": "exclude_js"}}, {"module": "critical-resource-optimizer", "settings_map": {"critical_css": "critical_css", "defer_non_critical": "defer_non_critical", "optimize_js": "optimize_js", "resource_hints": "resource_hints"}}]}}