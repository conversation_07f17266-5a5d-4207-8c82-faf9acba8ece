<?php
/**
 * Heartbeat Control Module for Redco Optimizer
 *
 * Controls WordPress Heartbeat API frequency to reduce server load.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Heartbeat_Control {

    /**
     * Module settings
     */
    private $settings = array();

    /**
     * Constructor
     */
    public function __construct() {
        if (redco_is_module_enabled('heartbeat-control')) {
            $this->init();
        }
    }

    /**
     * Initialize the module
     */
    private function init() {
        // Load settings
        $this->load_settings();

        // Initialize hooks
        $this->init_hooks();
    }

    /**
     * Load module settings with shared optimization
     */
    private function load_settings() {
        $this->settings = array(
            'admin_heartbeat' => redco_get_module_option('heartbeat-control', 'admin_heartbeat', 'modify'),
            'admin_frequency' => redco_get_module_option('heartbeat-control', 'admin_frequency', 60),
            'editor_heartbeat' => redco_get_module_option('heartbeat-control', 'editor_heartbeat', 'modify'),
            'editor_frequency' => redco_get_module_option('heartbeat-control', 'editor_frequency', 30),
            'frontend_heartbeat' => redco_get_module_option('heartbeat-control', 'frontend_heartbeat', 'disable'),
            'frontend_frequency' => redco_get_module_option('heartbeat-control', 'frontend_frequency', 60)
        );

        // Use shared resource loading optimizer for optimal frequencies
        if (class_exists('Redco_Resource_Loading_Optimizer')) {
            // Get optimal frequencies if not customized by user
            if (!redco_get_module_option('heartbeat-control', 'frequencies_customized', false)) {
                $this->settings['admin_frequency'] = Redco_Resource_Loading_Optimizer::get_optimal_threshold('heartbeat', 'admin');
                $this->settings['editor_frequency'] = Redco_Resource_Loading_Optimizer::get_optimal_threshold('heartbeat', 'editor');
                $this->settings['frontend_frequency'] = Redco_Resource_Loading_Optimizer::get_optimal_threshold('heartbeat', 'frontend');
            }
        }
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Control heartbeat based on context
        add_action('init', array($this, 'control_heartbeat'));

        // Modify heartbeat frequency
        add_filter('heartbeat_settings', array($this, 'modify_heartbeat_settings'));
    }

    /**
     * Control heartbeat based on settings
     */
    public function control_heartbeat() {
        // Frontend heartbeat control
        if (!is_admin() && $this->settings['frontend_heartbeat'] === 'disable') {
            wp_deregister_script('heartbeat');
        }

        // Admin heartbeat control
        if (is_admin() && !$this->is_post_editor()) {
            if ($this->settings['admin_heartbeat'] === 'disable') {
                wp_deregister_script('heartbeat');
            }
        }

        // Post editor heartbeat control
        if ($this->is_post_editor()) {
            if ($this->settings['editor_heartbeat'] === 'disable') {
                wp_deregister_script('heartbeat');
            }
        }
    }

    /**
     * Modify heartbeat settings
     */
    public function modify_heartbeat_settings($settings) {
        // Frontend frequency
        if (!is_admin() && $this->settings['frontend_heartbeat'] === 'modify') {
            $settings['interval'] = $this->settings['frontend_frequency'];
        }

        // Admin frequency
        if (is_admin() && !$this->is_post_editor() && $this->settings['admin_heartbeat'] === 'modify') {
            $settings['interval'] = $this->settings['admin_frequency'];
        }

        // Post editor frequency
        if ($this->is_post_editor() && $this->settings['editor_heartbeat'] === 'modify') {
            $settings['interval'] = $this->settings['editor_frequency'];
        }

        return $settings;
    }

    /**
     * Check if current page is post editor
     */
    private function is_post_editor() {
        global $pagenow;

        return in_array($pagenow, array('post.php', 'post-new.php'));
    }

    /**
     * Get heartbeat statistics with enhanced optimization metrics
     */
    public function get_stats() {
        $stats = array(
            'admin_status' => $this->settings['admin_heartbeat'],
            'admin_frequency' => $this->settings['admin_frequency'],
            'editor_status' => $this->settings['editor_heartbeat'],
            'editor_frequency' => $this->settings['editor_frequency'],
            'frontend_status' => $this->settings['frontend_heartbeat'],
            'frontend_frequency' => $this->settings['frontend_frequency'],
            'enabled' => redco_is_module_enabled('heartbeat-control'),
            'optimization_level' => $this->calculate_optimization_level(),
            'server_load_reduction' => $this->estimate_server_load_reduction(),
            'performance_impact' => $this->calculate_performance_impact()
        );

        return $stats;
    }

    /**
     * Calculate optimization level for shared system
     */
    private function calculate_optimization_level() {
        $score = 0;

        // Frontend optimization (highest impact)
        if ($this->settings['frontend_heartbeat'] === 'disable') {
            $score += 40;
        } elseif ($this->settings['frontend_heartbeat'] === 'modify') {
            $score += 20;
        }

        // Admin optimization
        if ($this->settings['admin_heartbeat'] === 'modify' && $this->settings['admin_frequency'] >= 60) {
            $score += 30;
        } elseif ($this->settings['admin_heartbeat'] === 'modify') {
            $score += 15;
        }

        // Editor optimization
        if ($this->settings['editor_heartbeat'] === 'modify' && $this->settings['editor_frequency'] >= 30) {
            $score += 30;
        } elseif ($this->settings['editor_heartbeat'] === 'modify') {
            $score += 15;
        }

        return min(100, $score);
    }

    /**
     * Estimate server load reduction
     */
    private function estimate_server_load_reduction() {
        $reduction = 0;

        // Frontend heartbeat disabled = major reduction
        if ($this->settings['frontend_heartbeat'] === 'disable') {
            $reduction += 60; // 60% reduction in frontend requests
        }

        // Admin heartbeat optimization
        if ($this->settings['admin_heartbeat'] === 'modify') {
            $default_frequency = 15; // WordPress default
            $current_frequency = $this->settings['admin_frequency'];
            $reduction += max(0, (($current_frequency - $default_frequency) / $current_frequency) * 20);
        }

        return min(80, $reduction); // Cap at 80% reduction
    }

    /**
     * Calculate performance impact
     */
    private function calculate_performance_impact() {
        $optimization_level = $this->calculate_optimization_level();

        if ($optimization_level >= 80) {
            return array(
                'status' => 'excellent',
                'message' => 'Heartbeat control is excellently optimized',
                'requests_reduced' => $this->estimate_server_load_reduction()
            );
        } elseif ($optimization_level >= 60) {
            return array(
                'status' => 'good',
                'message' => 'Heartbeat control is well optimized',
                'requests_reduced' => $this->estimate_server_load_reduction()
            );
        } elseif ($optimization_level >= 40) {
            return array(
                'status' => 'fair',
                'message' => 'Heartbeat control has room for improvement',
                'requests_reduced' => $this->estimate_server_load_reduction()
            );
        } else {
            return array(
                'status' => 'needs_improvement',
                'message' => 'Heartbeat control needs optimization',
                'requests_reduced' => $this->estimate_server_load_reduction()
            );
        }
    }
}

// Initialize the module only if enabled and after init hook
function redco_init_heartbeat_control() {
    if (redco_is_module_enabled('heartbeat-control')) {
        new Redco_Heartbeat_Control();
    }
}
add_action('init', 'redco_init_heartbeat_control', 10);
